import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/oms/return/order/list',
    method: 'get',
    params: data
  })
}

export function getReturnOrder(data) {
  return request({
    url: '/oms/return/order/' + data,
    method: 'get'
  })
}
export function getAuditLog(data) {
  return request({
    url: '/oms/return/order/audit/log/' + data,
    method: 'get'
  })
}
export function getRefund(data) {
  return request({
    url: '/oms/return/order/refund/' + data,
    method: 'get'
  })
}
export function orderAudit(orderId, status) {
  return request({
    url: '/oms/return/order/audit/' + orderId + '/' + status,
    method: 'get'
  })
}
export function refundAudit(orderId, status) {
  return request({
    url: '/oms/return/order/refund/audit/' + orderId + '/' + status,
    method: 'get'
  })
}

