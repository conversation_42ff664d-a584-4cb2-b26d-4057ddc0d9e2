import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/oms/order/list',
    method: 'get',
    params: data
  })
}

export function get(data) {
  return request({
    url: '/oms/order/details/' + data,
    method: 'get'
  })
}
export function deliverGoods(data) {
  return request({
    url: '/oms/order/deliver/' + data,
    method: 'get'
  })
}
export function cancelOfTheGoods(data) {
  return request({
    url: '/oms/order/cancel/goods/' + data,
    method: 'get'
  })
}
export function returnOfTheGoods(orderId, data) {
  return request({
    url: '/oms/order/return/goods/' + orderId,
    method: 'post',
    data
  })
}
export function statistical(data) {
  return request({
    url: '/oms/order/statistical',
    method: 'get',
    params: data
  })
}

export function canelOrders(data) {
  return request({
    url: '/oms/order/cancel',
    method: 'post',
    data
  })
}

export function canelAudit(data) {
  return request({
    url: '/oms/order/audit',
    method: 'post',
    data
  })
}

export function editOrder(data) {
  return request({
    url: '/oms/order',
    method: 'post',
    data
  })
}

export function returnAudit(data) {
  return request({
    url: '/oms/order/return/audit',
    method: 'post',
    data
  })
}

export function tempUrl() {
  return request({
    url: '/oms/order/expert/model ',
    method: 'post'
  })
}

export function orderExport(data) {
  const query = qs.stringify(data)
  return process.env.VUE_APP_BASE_API + '/oms/order/export?' + query
}

export function excelURL() {
  return process.env.VUE_APP_BASE_API + '/oms/order/update/status/import'
}
