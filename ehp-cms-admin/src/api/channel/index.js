import request from '@/utils/request'
import qs from 'qs'

// 渠道管理列表
export function getChannelList(data) {
  return request({
    url: '/channel/page',
    method: 'get',
    params: data
  })
}

// 新增渠道
export function addChannel(data) {
  return request({
    url: '/channel',
    method: 'post',
    data
  })
}

// 编辑渠道
export function editChannel(data) {
  return request({
    url: '/channel',
    method: 'put',
    data
  })
}

// 业务员选择列表
export function getSalesmanList(data) {
  return request({
    url: '/salesman/select',
    method: 'get',
    params: data
  })
}

// 业务员管理列表
export function getList(data) {
  return request({
    url: '/salesman/page',
    method: 'get',
    params: data
  })
}

// 获取业务员团队
export function getTeam(data) {
  return request({
    url: '/team/seelct',
    method: 'get',
    params: data
  })
}

// 业务员批量导入
export function importSalesman(data) {
  return process.env.VUE_APP_BASE_API + '/salesman/import'
}

// 业务员基本信息
export const getSalesmanInfo = (data) => {
  return request({
    url: '/salesman/info',
    method: 'get',
    params: data
  })
}

// 下属业务员列表
export const getSubordinateSalesmanList = (data) => {
  return request({
    url: '/salesman/subordinate/page',
    method: 'get',
    params: data
  })
}

// 下属业务员统计
export const getSubordinateStatistics = (data) => {
  return request({
    url: '/salesman/subordinate/statistics',
    method: 'get',
    params: data
  })
}

// 导出下属业务员
export const subordinateExport = (data) => {
  return process.env.VUE_APP_BASE_API + '/salesman/subordinate/export/?' + qs.stringify(data)
}

// 新增患者列表
export const getAddPatientList = (data) => {
  return request({
    url: '/salesman/patient/page',
    method: 'get',
    params: data
  })
}

// 新增患者统计
export const getAddPatientStatistics = (data) => {
  return request({
    url: '/salesman/patient/statistics',
    method: 'get',
    params: data
  })
}

// 渠道列表
export const getChannelStatistics = () => {
  return request({
    url: '/channel/select',
    method: 'get'
  })
}

//业务员基本信息编辑
export const putSalesmanInfoEdit = (data) => {
  return request({
    url: '/salesman/edit',
    method: 'put',
    data: data
  })
}
