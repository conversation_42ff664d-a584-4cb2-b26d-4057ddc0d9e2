import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/medication/recom/list',
    method: 'get',
    params: data
  })
}
export function getDetail(data) {
  return request({
    url: '/medication/recom/detail',
    method: 'get',
    params: data
  })
}
export function getAuditList(data) {
  return request({
    url: '/medication/recom/audit/list',
    method: 'get',
    params: data
  })
}
export function getAuditDetail(data) {
  return request({
    url: '/medication/recom/audit/detail',
    method: 'get',
    params: data
  })
}
export function audit(data, transformResponse) {
  return request({
    url: '/medication/recom/audit',
    method: 'post',
    data: qs.stringify(data),
    transformResponse: transformResponse
  })
}
export function configset(data) {
  return request({
    url: '/medication/recom/audit/config/set',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function configlist(data) {
  return request({
    url: '/medication/recom/audit/config/list',
    method: 'get',
    params: data
  })
}
export function veridentity(data) {
  return request({
    url: '/medication/recom/verify/identity',
    method: 'get'
  })
}
export function setidentity(data) {
  return request({
    url: '/medication/recom/identity/' + data.cistId + '/' + data.idCard,
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getAuditLogs(data) {
  return request({
    url: '/medication/recom/logs/audit',
    method: 'get',
    params: data
  })
}
export function getCode(data) {
  return request({
    url: '/medication/recom/preSign',
    method: 'post',
    data: qs.stringify(data)
  })
}
export function getAuthCode(data) {
  return request({
    url: '/medication/recom/sendRealnameCode/' + data.pharmacistId,
    method: 'post'
  })
}

export function getPharmacist(data) {
  return request({
    url: '/medication/recom/pharmacist/' + data.phone + '/' + data.verifyCode,
    method: 'post'
  })
}
// 获取二维码
export function getQrCode(data) {
  return request({
    url: '/medication/recom/qrcode',
    method: 'get',
    params: data
  })
}
// 解除绑定
export function removeBind(data) {
  return request({
    url: '/medication/recom/untie',
    method: 'post',
    params: data
  })
}
// 获取免密状态
export function getNoSecret(data) {
  return request({
    url: '/user/pharmacis/getNoSecret',
    method: 'get',
    params: data
  })
}
// 处方单药师签名预签名
export function getSignPre(data) {
  return request({
    url: '/medication/recom/sign/pre',
    method: 'post',
    params: data
  })
}
// 处方单药师签名预签名
export function getServiceType() {
  return request({
    url: '/medication/recom/esign/service',
    method: 'get'
  })
}
// 中药处方列表
export function getCnAuditList(data) {
  return request({
    url: '/medication/recom/audit/list/tcm',
    method: 'get',
    params: data
  })
}
// 中药处方详情
export function getCnAuditDetail(data) {
  return request({
    url: '/medication/recom/audit/detail/tcm',
    method: 'get',
    params: data
  })
}

export default {
  getList,
  getDetail,
  getAuditList,
  getAuditDetail,
  audit,
  configset,
  configlist,
  veridentity,
  setidentity,
  getAuditLogs,
  getCode,
  getAuthCode,
  getPharmacist,
  getQrCode,
  removeBind,
  getNoSecret,
  getSignPre,
  getServiceType,
  getCnAuditList,
  getCnAuditDetail

}
