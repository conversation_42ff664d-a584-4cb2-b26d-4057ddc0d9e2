import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/medication/recom/list/tcm',
    method: 'get',
    params: data
  })
}

export function categorylist() {
  return request({
    url: 'wms/category/list',
    method: 'get'
  })
}
export function info(productId) {
  return request({
    url: '/wms/medicine/drug/info/' + productId,
    method: 'get'
  })
}
export function infosku(productId) {
  return request({
    url: '/wms/medicine/sku/list/' + productId,
    method: 'get'
  })
}
export function update(data) {
  return request({
    url: '/wms/medicine/drug/update',
    method: 'post',
    data
  })
}
export function skulist(productId) {
  return request({
    url: '/wms/medicine/sku/list/' + productId,
    method: 'get'
  })
}
export function skuupdate(data) {
  return request({
    url: '/wms/medicine/sku/update',
    method: 'post',
    data
  })
}
export function updateDefatule(productId, skuId) {
  return request({
    url: '/wms/medicine/sku/default/' + productId + '/' + skuId,
    method: 'post'
  })
}
export function upload(data) {
  return request({
    url: '/wms/medicine/sku/upload',
    method: 'post',
    data: data
  })
}

export function warehouse() {
  return request({
    url: '/wms/warehouse/select',
    method: 'get'
  })
}
export function pharmacology() {
  return request({
    url: '/wms/classification/pharmacology/list',
    method: 'get'
  })
}

export function getDetail(data) {
  return request({
    url: '/medication/recom/detail/tcm',
    method: 'get',
    params: data
  })
}

export default {
  list,
  getDetail,
  categorylist,
  info,
  infosku,
  update,
  skulist,
  skuupdate,
  updateDefatule,
  upload,
  warehouse,
  pharmacology
}
