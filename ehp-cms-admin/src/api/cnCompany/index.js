import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/tcm/pe',
    method: 'get',
    params: data
  })
}
export function update(data) {
  let method = 'post'
  if (data.id) {
    method = 'put'
  }
  return request({
    url: '/tcm/pe',
    method: method,
    data
  })
}

export function del(id) {
  return request({
    url: '/tcm/pe/' + id,
    method: 'delete'
  })
}

export default {
  list,
  update,
  del
}
