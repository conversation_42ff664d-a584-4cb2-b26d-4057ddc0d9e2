import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/followup/form/list',
    method: 'get',
    params: data
  })
}
export function saveData(data) {
  return request({
    url: '/followup/form/',
    method: 'post',
    data
  })
}
export function getDetail(id) {
  return request({
    url: '/followup/form/detail/' + id,
    method: 'get'
  })
}
export function getDrawing(id) {
  return request({
    url: '/followup/form/' + id,
    method: 'get'
  })
}
export function delForm(data) {
  return request({
    url: '/followup/form',
    method: 'delete',
    params: data
  })
}
export function setStatus(data) {
  return request({
    url: `/followup/form/status/${data.id}/${data.status}`,
    method: 'put'
  })
}
export function getQalist(data) {
  return request({
    url: `/followup/form/qa`,
    method: 'get',
    params: data
  })
}
export function getRecordDetail(data) {
  return request({
    url: `/followup/form/qa/record/detail`,
    method: 'get',
    params: data
  })
}

/**
 * 新增表单类型
 * @param data.name 名称
 * @param data.id  id
 */
export function formTypeAdd(data) {
  return request({
    url: '/followup/formType/save',
    method: 'post',
    data
  })
}

/**
 * 表单类型列表
 */
export function formTypeList() {
  return request({
    url: '/followup/formType/list',
    method: 'get'
  })
}

/**
 * 表单类型删除
 * @param id
 */
export function formTypeDelete(id) {
  return request({
    url: '/followup/formType/' + id,
    method: 'delete'
  })
}

/**
 * 修改表单类型
 * @param id
 * @param name
 */
export function formTypeEdit(data) {
  return request({
    url: '/followup/formType/update',
    method: 'post',
    data
  })
}

/**
 * 表单类型枚举
 */
export function formTypeAllList() {
  return request({
    url: '/followup/formType/allList',
    method: 'get'
  })
}
export function exportQalist(data) {
  return process.env.VUE_APP_BASE_API + `/followup/form/qa/export?` + qs.stringify(data)
}
