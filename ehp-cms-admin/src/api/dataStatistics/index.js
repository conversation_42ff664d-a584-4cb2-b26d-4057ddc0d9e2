import request from '@/utils/request'
import qs from 'qs'

// 实况数据
export function getDashboard(data) {
  return request({
    url: '/statitics/dashboard/overview',
    method: 'get',
    params: data
  })
}

//问诊处方趋势
export function getConsult(data) {
  return request({
    url: '/statitics/dashboard/consult',
    method: 'get',
    params: data
  })
}

// 问诊科室排行榜
export function getConsultRank(data) {
  return request({
    url: '/statitics/dashboard/rankings/department',
    method: 'get',
    params: data
  })
}

// 商品销售排行榜
export function getProducts(data) {
  return request({
    url: '/statitics/dashboard/rankings/products',
    method: 'get',
    params: data
  })
}

// 商品订单分析
export function getProductOrder(data) {
  return request({
    url: '/statitics/dashboard/orders',
    method: 'get',
    params: data
  })
}

// 医生运营分析-等级分布
export function getDoctorLevel(data) {
  return request({
    url: '/statitics/analysis/doctor/levels',
    method: 'get',
    params: data
  })
}

// 医生销售额排行
export function getDoctorSalesRank(data) {
  return request({
    url: '/statitics/analysis/doctor/salesRanking',
    method: 'get',
    params: data
  })
}

// 患者数分析
export function getPatient(data) {
  return request({
    url: '/statitics/analysis/doctor/patient',
    method: 'get',
    params: data
  })
}

// 医生数据明细
export function getDoctorDetail(params) {
  return request({
    url: '/statitics/analysis/doctor/details',
    method: 'get',
    params,
    paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' })
  })
}

// 问诊收入统计
export function getConsultation(data) {
  return request({
    url: '/statitics/consult',
    method: 'get',
    params: data
  })
}
