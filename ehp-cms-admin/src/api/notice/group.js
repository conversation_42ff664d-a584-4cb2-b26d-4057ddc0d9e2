import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/notice/group/list',
    method: 'get',
    params: data
  })
}

export function addGroup(data) {
  return request({
    url: '/notice/group',
    method: 'post',
    data
  })
}

export function updateGroup(data) {
  return request({
    url: '/notice/group',
    method: 'put',
    data
  })
}
export function uploadImage(data) {
  return request({
    url: '/storage',
    method: 'post',
    data
  })
}
export function deleteGroup(data) {
  return request({
    url: '/notice/group',
    method: 'DELETE',
    params: data
  })
}
