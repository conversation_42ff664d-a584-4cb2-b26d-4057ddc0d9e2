import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/notice/message/list',
    method: 'get',
    params: data
  })
}

export function addNotice(data) {
  return request({
    url: '/notice/message',
    method: 'post',
    data
  })
}

export function updateNotice(data) {
  return request({
    url: '/notice/message',
    method: 'put',
    data
  })
}
export function uploadImage(data) {
  return request({
    url: '/storage',
    method: 'post',
    data
  })
}
export function deleteNotice(data) {
  return request({
    url: '/notice/message',
    method: 'DELETE',
    params: data
  })
}

export function groupList(data) {
  return request({
    url: '/notice/group/list',
    method: 'get',
    params: data
  })
}
