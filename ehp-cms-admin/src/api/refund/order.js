import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/refund/list',
    method: 'get',
    params: data
  })
}

export function get(refundOrderId) {
  return request({
    url: '/refund/' + refundOrderId,
    method: 'get'
  })
}
export function queryOrder(orderSn) {
  return request({
    url: '/refund/query/' + orderSn,
    method: 'get'
  })
}

export function update(data) {
  return request({
    url: 'refund',
    method: 'post',
    data
  })
}
export function updateAndSubmit(data) {
  return request({
    url: 'refund/submit',
    method: 'post',
    data
  })
}
export function submit(data) {
  return request({
    url: 'refund/submit/',
    method: 'put',
    data
  })
}
export function invalid(data) {
  return request({
    url: 'refund/invalid/',
    method: 'post',
    data
  })
}

export function pay(data) {
  return request({
    url: 'refund/pay/',
    method: 'post',
    data
  })
}
export function audit(data) {
  return request({
    url: 'refund/audit/',
    method: 'post',
    data
  })
}
export function reverseAudit(data) {
  return request({
    url: 'refund/reverseAudit/',
    method: 'post',
    data
  })
}

export function del(refundOrderId) {
  return request({
    url: '/refund/' + refundOrderId,
    method: 'delete'
  })
}
