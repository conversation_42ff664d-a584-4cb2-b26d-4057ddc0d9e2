import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/consult/refund/order/page',
    method: 'get',
    params: data
  })
}

export function get(refundOrderId) {
  return request({
    url: '/consult/refund/order/' + refundOrderId,
    method: 'get'
  })
}
export function queryOrder(orderSn) {
  return request({
    url: '/consult/refund/order/select/' + orderSn,
    method: 'get'
  })
}
export function verify(data) {
  return request({
    url: '/consult/refund/order/verify/',
    method: 'post',
    params: data
  })
}

export function update(data) {
  return request({
    url: 'consult/refund/order/edit',
    method: 'post',
    data
  })
}
export function updateAndSubmit(data) {
  return request({
    url: 'consult/refund/order/save',
    method: 'post',
    data
  })
}
export function submit(data) {
  return request({
    url: 'consult/refund/order/submit/',
    method: 'post',
    data
  })
}
export function invalid(data) {
  return request({
    url: 'consult/refund/order/invalid',
    method: 'post',
    data
  })
}

export function pay(data) {
  return request({
    url: 'consult/refund/order/pay/',
    method: 'post',
    data
  })
}
export function audit(data) {
  return request({
    url: 'consult/refund/order/audit',
    method: 'post',
    data
  })
}
export function reverseAudit(data) {
  return request({
    url: 'consult/refund/order/reverse/audit/',
    method: 'post',
    data
  })
}

export function orderExport(data) {
  const query = qs.stringify(data)
  return process.env.VUE_APP_BASE_API + '/consult/refund/order/export?' + query
}

