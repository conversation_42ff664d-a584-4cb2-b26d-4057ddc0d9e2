import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/tcm/slice',
    method: 'get',
    params: data
  })
}
export function getSlices(id) {
  return request({
    url: '/tcm/slice/' + id,
    method: 'get'
  })
}

export function update(data) {
  let method = 'post'
  if (data.id) {
    method = 'put'
  }
  return request({
    url: '/tcm/slice',
    method: method,
    data
  })
}
export function del(id) {
  return request({
    url: '/tcm/slice/' + id,
    method: 'delete'
  })
}
export function particleList(data) {
  return request({
    url: '/tcm/particle',
    method: 'get',
    params: data
  })
}
export function getParticle(id) {
  return request({
    url: '/tcm/particle/' + id,
    method: 'get'
  })
}

export function updateParticle(data) {
  let method = 'post'
  if (data.id) {
    method = 'put'
  }
  return request({
    url: '/tcm/particle',
    method: method,
    data
  })
}
export function delParticle(id) {
  return request({
    url: '/tcm/particle/' + id,
    method: 'delete'
  })
}

export default {
  list,
  getSlices,
  update,
  del,
  particleList,
  updateParticle,
  getParticle,
  delParticle
}
