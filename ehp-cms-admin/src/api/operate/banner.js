import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/banner/list',
    method: 'get',
    params: data
  })
}

export function add(data) {
  return request({
    url: '/banner',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: '/banner',
    method: 'put',
    data
  })
}

export function detail(id) {
  return request({
    url: `/banner/${id}`,
    method: 'get'
  })
}

export function uploadImage(data) {
  return request({
    url: '/storage',
    method: 'post',
    data
  })
}

export function enable(data) {
  return request({
    url: `/banner/enable/${data.id}/${data.status}`,
    method: 'post'
  })
}
