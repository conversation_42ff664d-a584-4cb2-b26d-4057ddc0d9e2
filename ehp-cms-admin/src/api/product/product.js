import request from '@/utils/request'
import qs from 'qs'

export function list(data) {
  return request({
    url: '/wms/product/list',
    method: 'get',
    params: data
  })
}
// export function add(data) {
//   return request({
//     url: '/wms/medicine/drug/info',
//     method: 'get',
//     params: data
//   })
// }
export function categorylist() {
  return request({
    url: 'wms/category/list',
    method: 'get'
  })
}
export function info(productId) {
  return request({
    url: '/wms/medicine/drug/info/' + productId,
    method: 'get'
  })
}
export function infosku(productId) {
  return request({
    url: '/wms/medicine/sku/list/' + productId,
    method: 'get'
  })
}
export function update(data) {
  return request({
    url: '/wms/medicine/drug/update',
    method: 'post',
    data
  })
}
export function skulist(productId) {
  return request({
    url: '/wms/medicine/sku/list/' + productId,
    method: 'get'
  })
}
export function skuupdate(data) {
  return request({
    url: '/wms/medicine/sku/update',
    method: 'post',
    data
  })
}
export function updateDefatule(productId, skuId) {
  return request({
    url: '/wms/medicine/sku/default/' + productId + '/' + skuId,
    method: 'post'
  })
}
export function upload(data) {
  return request({
    url: '/wms/medicine/sku/upload',
    method: 'post',
    data: data
  })
}

export function warehouse() {
  return request({
    url: '/wms/warehouse/select',
    method: 'get'
  })
}

export function getZhuanti() {
  return request({
    url: '/wms/section/default',
    method: 'get'
  })
}

// 编辑专题标题
export function setZhuanti(data) {
  return request({
    url: '/wms/section/default',
    method: 'post',
    data: qs.stringify(data)
  })
}
// 编辑专题商品
export function setZtProducts(data) {
  return request({
    url: '/wms/section/default/products',
    method: 'post',
    data
  })
}

// 根据关键字搜索商品
export function getProduct(data) {
  return request({
    url: '/wms/section/product/search',
    method: 'get',
    params: data
  })
}
// 根据关键字搜索商品
export function delProduct(data) {
  return request({
    url: 'wms/section/default/products',
    method: 'delete',
    params: data
  })
}

// 展示分类-新增编辑分类
export function addOrEditCategory(data) {
  return request({
    url: '/wms/category',
    method: 'post',
    data
  })
}
// 展示分类-列表
export function getCategoryList(data) {
  return request({
    url: '/wms/category/pages',
    method: 'get',
    params: data
  })
}

// 删除展示分类
export function delCategory(data) {
  return request({
    url: '/wms/category',
    method: 'delete',
    params: data
  })
}
// 删除展示分类
export function changeFeatured(data) {
  return request({
    url: '/wms/product/' + data.productId + '/featured',
    method: 'post',
    params: data
  })
}

export default {
  list,
  categorylist,
  // add,
  info,
  infosku,
  update,
  skulist,
  skuupdate,
  updateDefatule,
  upload,
  warehouse,
  getZhuanti,
  setZhuanti,
  setZtProducts,
  getProduct,
  delProduct,
  addOrEditCategory,
  getCategoryList,
  delCategory,
  changeFeatured
}
