import request from '@/utils/request'
import qs from 'qs'

export function agents() {
  return request({
    url: '/wms/classification/agents/list',
    method: 'get'
  })
}

export function drug() {
  return request({
    url: '/wms/classification/drug/list',
    method: 'get'
  })
}

export function pharmacology() {
  return request({
    url: '/wms/classification/pharmacology/list',
    method: 'get'
  })
}

export function validateName(data) {
  return request({
    url: '/wms/medicine/verify/drug/info',
    method: 'post',
    data: qs.stringify(data)
  })
}

export default {
  agents,
  drug,
  pharmacology,
  validateName
}
