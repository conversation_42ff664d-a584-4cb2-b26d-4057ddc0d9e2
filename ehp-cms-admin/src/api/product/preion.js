import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/wms/medicine/sku/default/usage/list',
    method: 'get',
    params: data
  })
}
export function getDetail(data) {
  return request({
    url: '/wms/medicine/sku/default/usage/detail',
    method: 'get',
    params: data
  })
}
export function setUsage(data) {
  return request({
    url: '/wms/medicine/sku/default/usage/edit',
    method: 'post',
    data
  })
}
export function tempUrl() {
  return request({
    url: '/wms/medicine/sku/default/usage/expert/model',
    method: 'get'
  })
}
export function excelURL() {
  return process.env.VUE_APP_BASE_API + '/wms/medicine/sku/default/usage/importExcel'
}

export default {
  getList,
  getDetail,
  setUsage,
  excelURL,
  tempUrl
}
