import request from '@/utils/request'

export function list(data) {
  return request({
    url: '/tcm/recipe',
    method: 'get',
    params: data
  })
}
export function del(id) {
  return request({
    url: '/tcm/recipe/' + id,
    method: 'delete'
  })
}
export function info(id) {
  return request({
    url: '/tcm/recipe/' + id,
    method: 'get'
  })
}
export function select(data) {
  return request({
    url: '/tcm/slice/select',
    method: 'get',
    params: data
  })
}
export function upload(data) {
  let method = 'post'
  if (data.id) {
    method = 'put'
  }
  return request({
    url: '/tcm/recipe',
    method: method,
    data
  })
}

// export function add(data) {
//   return request({
//     url: '/wms/medicine/drug/info',
//     method: 'get',
//     params: data
//   })
// }
export function categorylist() {
  return request({
    url: '/wms/category/list',
    method: 'get'
  })
}

export function infosku(productId) {
  return request({
    url: '/wms/medicine/sku/list/' + productId,
    method: 'get'
  })
}
export function update(data) {
  return request({
    url: '/wms/medicine/drug/update',
    method: 'post',
    data
  })
}
export function skulist(productId) {
  return request({
    url: '/wms/medicine/sku/list/' + productId,
    method: 'get'
  })
}
export function skuupdate(data) {
  return request({
    url: '/wms/medicine/sku/update',
    method: 'post',
    data
  })
}
export function updateDefatule(productId, skuId) {
  return request({
    url: '/wms/medicine/sku/default/' + productId + '/' + skuId,
    method: 'post'
  })
}

export function warehouse() {
  return request({
    url: '/wms/warehouse/select',
    method: 'get'
  })
}
export function pharmacology() {
  return request({
    url: '/wms/classification/pharmacology/list',
    method: 'get'
  })
}
export default {
  list,
  del,
  select,
  info,
  categorylist,
  // add,
  infosku,
  update,
  skulist,
  skuupdate,
  updateDefatule,
  upload,
  warehouse,
  pharmacology
}
