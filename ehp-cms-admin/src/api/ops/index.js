import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/version',
    method: 'get',
    params: data
  })
}
export function save(data) {
  return request({
    url: '/version',
    method: 'post',
    data
  })
}
export function update(data) {
  return request({
    url: '/version',
    method: 'put',
    data
  })
}
export function enable(id) {
  return request({
    url: '/version/enable/' + id,
    method: 'post'
  })
}
export function disabled(id) {
  return request({
    url: '/version/disable/' + id,
    method: 'post'
  })
}
// 新增定时任务
export function scheduleAdd(data) {
  return request({
    url: '/devops/schedule/add',
    method: 'post',
    data
  })
}
// 取消定时任务
export function scheduleCancel(data) {
  return request({
    url: '/devops/schedule/cancel',
    method: 'get',
    params: data
  })
}
// 开始定时任务
export function scheduleRestart(data) {
  return request({
    url: '/devops/schedule/restart',
    method: 'get',
    params: data
  })
}
// 定时任务列表
export function scheduleList(data) {
  return request({
    url: '/devops/schedule/job/list',
    method: 'get',
    params: data
  })
}
// 暂停定时任务
export function scheduleStop(data) {
  return request({
    url: '/devops/schedule/stop',
    method: 'get',
    params: data
  })
}
// 修改定时任务
export function scheduleUpdate(data) {
  return request({
    url: '/devops/schedule/update',
    method: 'post',
    data
  })
}

// 文件初始化配置列表
export function initConfigList() {
  return request({
    url: '/devops/sys/init/file/config/list',
    method: 'get'
  })
}
// 文件初始化配置检测
export function initConfigCheck(id) {
  return request({
    url: '/devops/sys/init/file/config/check',
    method: 'get',
    params: { id }
  })
}
// 系统自检列表
export function inspectList() {
  return request({
    url: '/devops/self/inspect/list',
    method: 'get'
  })
}
// 系统自检
export function systemCheck(data) {
  return request({
    url: data.invokeUrl,
    method: 'get',
    params: data
  })
}
// ocr自检
export function inspectOcr(data) {
  return request({
    url: `/devops/self/inspect/ocr`,
    method: 'post',
    data
  })
}
// 系统自检成功
export function inspectSuccess(id) {
  return request({
    url: `/devops/self/inspect/success/${id}`,
    method: 'put'
  })
}

export function uploadUrl(data) {
  return process.env.VUE_APP_BASE_API + `/version/upload/apk`
}
