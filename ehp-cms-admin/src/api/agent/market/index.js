import request from '@/utils/request'
import qs from 'qs'
//销售内容列表
export function getList(data) {
  return request({
    url: '/agent/notice/list',
    method: 'get',
    params: data
  })
}
//查看详情
export function get(data) {
  return request({
    url: '/agent/notice/get',
    method: 'post',
    params: data
  })
}
//新增保存
export function create(data) {
  return request({
    url: '/agent/notice/save',
    method: 'post',
    data: qs.stringify(data)
  })
}
//编辑保存
export function update(data) {
  return request({
    url: '/agent/notice/save',
    method: 'post',
    data: qs.stringify(data)
  })
}
//发布
export function issue(data) {
  return request({
    url: '/agent/notice/release',
    method: 'post',
    data: qs.stringify(data)
  })
}
//下架
export function undercarriage(data) {
  return request({
    url: '/agent/notice/release/cancel',
    method: 'post',
    data: qs.stringify(data)
  })
}
