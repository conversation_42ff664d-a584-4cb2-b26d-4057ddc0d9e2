import request from '@/utils/request'
import qs from 'qs'

//全平台数据统计
export function getStatistics(data) {
  return request({
    url: '/business/report/statistics',
    method: 'get',
    params: data
  })
}
//营业列表
export function getList(data) {
  return request({
    url: '/business/report/area/list',
    method: 'get',
    params: data
  })
}
//大区详情
export function get(data) {
  return request({
    url: '/business/report/area/detail',
    method: 'get',
    params: data
  })
}
//(大区-省区)报表列表
export function getTableList(data) {
  return request({
    url: '/business/report/list',
    method: 'get',
    params: data
  })
}
//下载报表
export function downloadExport(data) {
  const query = qs.stringify(data)
  return process.env.VUE_APP_BASE_API + '/business/report/export?' + query
}
//下载报表(reportDates参数方式：数组)
// export function downloadExport(data, obj = []) {
//   return request({
//     url: '/business/report/export?' + qs.stringify(obj, { arrayFormat: 'repeat' }),
//     method: 'get',
//     params: data
//   })
// }

