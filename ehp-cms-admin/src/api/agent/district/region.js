import request from '@/utils/request'
import qs from 'qs'

// 获取大区列表
export function getListData(data) {
  return request({
    url: '/agent/area/list',
    method: 'get',
    params: data
  })
}
// 新增修改大区数据
export function saveArea(data) {
  return request({
    url: '/agent/area/save',
    method: 'post',
    data: data
  })
}
// 查询大区下属城市
export function findCity(data) {
  return request({
    url: '/agent/area/city/find',
    method: 'get',
    params: data
  })
}
// 查询大区详情
export function findAreaDetails(data) {
  return request({
    url: '/agent/area/get',
    method: 'get',
    params: data
  })
}
