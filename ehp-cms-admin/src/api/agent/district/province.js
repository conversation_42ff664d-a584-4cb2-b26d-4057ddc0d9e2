import request from '@/utils/request'
import qs from 'qs'
// 查询城市列表
export function getListData(data) {
  return request({
    url: '/agent/area/city/list',
    method: 'get',
    params: data
  })
}
//查询所有城市列表
export function findAllArea(data) {
  return request({
    url: '/agent/area/city/findAll',
    method: 'get',
    params: data
  })
}
// 查询城市详情
export function fetchAreaDetails(data) {
  return request({
    url: '/agent/area/city/get',
    method: 'get',
    params: data
  })
}
// 修改省区经纪人
export function saveAreaDate(data) {
  return request({
    url: '/agent/area/city/save',
    method: 'post',
    data: data
  })
}
// 查询经纪人
export function findAgent(data) {
  return request({
    url: '/agent/find',
    method: 'get',
    params: data
  })
}

