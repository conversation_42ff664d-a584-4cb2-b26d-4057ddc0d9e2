import request from '@/utils/request'
import qs from 'qs'
// 经纪人列表
export function getList(data) {
  return request({
    url: '/agent/list',
    method: 'get',
    params: data
  })
}
// 经纪人基本信息
export function getDetail(data) {
  return request({
    url: '/agent/get',
    method: 'get',
    params: data
  })
}
// 修改经纪人信息
export function save(data) {
  return request({
    url: '/agent/save',
    method: 'post',
    data
  })
}
// 经纪人审核
export function audit(data) {
  return request({
    url: '/agent/audit',
    method: 'post',
    data
  })
}
// 经纪人下属医生
export function doctorList(data) {
  return request({
    url: '/agent/custom/find',
    method: 'get',
    params: data
  })
}
// 经纪人列表
export function achievementList(data) {
  return request({
    url: '/agent/achievement/list',
    method: 'get',
    params: data
  })
}

// 经纪人业绩信息
export function achievementInfo(data) {
  return request({
    url: '/agent/achievement/info',
    method: 'get',
    params: data
  })
}
// 业绩信息导出
export function achievementExport(data) {
  return process.env.VUE_APP_BASE_API + '/agent/achievement/export/?' + qs.stringify(data)
}
// 下属医生导出
export function customExport(data) {
  return process.env.VUE_APP_BASE_API + '/agent/custom/export/?' + qs.stringify(data)
}
// 大区省区信息
export function areaList(data) {
  return request({
    url: '/agent/area/find/tree',
    method: 'get',
    params: data
  })
}
// 经纪人批量导入
export function agentImport() {
  return process.env.VUE_APP_BASE_API + '/agent/import'
}
// 经纪人批量导入模版
export function tempUrl() {
  return request({
    url: '/agent/template/get',
    method: 'get'
  })
}
export default {
  save,
  audit,
  doctorList,
  getDetail,
  getList,
  agentImport,
  achievementExport,
  achievementList,
  achievementInfo,
  areaList,
  tempUrl,
  customExport
}
