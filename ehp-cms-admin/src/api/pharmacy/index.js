import request from '@/utils/request'

export function citylist(params) {
  return request({
    url: '/pharmacy/warehouse/city/list',
    method: 'get',
    params: params
  })
}

export function warehouseDetail(id) {
  return request({
    url: '/pharmacy/warehouse/' + id,
    method: 'get'
  })
}

export function list(data) {
  return request({
    url: '/pharmacy/warehouse/list',
    method: 'get',
    params: data
  })
}

export function save(data) {
  return request({
    url: '/pharmacy/warehouse/save',
    method: 'post',
    data
  })
}

export function select() {
  return request({
    url: '/pharmacy/warehouse/select',
    method: 'get'
  })
}

export function update(data) {
  return request({
    url: '/pharmacy/warehouse/update',
    method: 'post',
    data
  })
}

export function warehouseStatus(id, status) {
  return request({
    url: '/pharmacy/warehouse/update/' + id + '/' + status,
    method: 'get'
  })
}

export function setManage(id, data) {
  return request({
    url: '/pharmacy/warehouse/manage/' + id,
    method: 'post',
    data
  })
}
// 获取辅料信息
function getAccessories(data) {
  return request({
    url: '/pharmacy/warehouse/accessories',
    method: 'get',
    data
  })
}
// 批量导入药店
export function exportURL() {
  return process.env.VUE_APP_BASE_API + '/pharmacy/warehouse/import'
}

export default {
  citylist,
  warehouseDetail,
  list,
  save,
  select,
  update,
  warehouseStatus,
  setManage,
  getAccessories
}
