import request from '@/utils/request'

export function goodslist(data) {
  return request({
    url: '/pharmacy/warehouse/goods/list',
    method: 'get',
    params: data
  })
}

export function delGoods(id, data) {
  return request({
    url: '/pharmacy/warehouse/goods/select/list/' + id,
    method: 'delete',
    data
  })
}

export function getGoodsList(id, data) {
  return request({
    url: '/pharmacy/warehouse/goods/select/list/' + id,
    method: 'get',
    params: data
  })
}

export function setGoods(id, data) {
  return request({
    url: '/pharmacy/warehouse/goods/select/list/' + id,
    method: 'post',
    data
  })
}

export default {
  goodslist,
  delGoods,
  getGoodsList,
  setGoods
}
