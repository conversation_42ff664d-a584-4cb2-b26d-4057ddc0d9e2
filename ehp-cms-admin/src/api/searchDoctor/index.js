import request from '@/utils/request'
import qs from 'qs'
export function getList(data) {
  return request({
    url: '/user/doctor/expert/list',
    method: 'get',
    params: data
  })
}

export function doctorRemove(data) {
  return request({
    url: '/user/doctor/expert/remove',
    method: 'post',
    data:qs.stringify(data)
  })
}

export function excelURL() {
  return process.env.VUE_APP_BASE_API + '/user/doctor/expert/import';
}
export function tempUrl() {
  return request({
    url: '/user/doctor/expert/model',
    method: 'post'
  })
}
