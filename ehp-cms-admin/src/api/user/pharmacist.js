import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/user/pharmacis/list',
    method: 'get',
    params: data
  })
}

export function getDetail(id) {
  return request({
    url: '/user/pharmacis/' + id,
    method: 'get'
  })
}

export function addPharmacis(data) {
  return request({
    url: '/user/pharmacis/add',
    method: 'post',
    data
  })
}

export function updatePharmacis(data) {
  return request({
    url: '/user/pharmacis/update/basic/info',
    method: 'post',
    data
  })
}

export function uploadCertificate(data) {
  return request({
    url: '/user/pharmacis/upload/6',
    method: 'post',
    data
  })
}

export function uploadCertiDcard(data) {
  return request({
    url: '/user/pharmacis/upload/4',
    method: 'post',
    data
  })
}

export function nationList() {
  return request({
    url: '/user/pharmacis/nations',
    method: 'get'
  })
}

export function getRecordInfo(id) {
  if (id == null) {
    return request({
      url: '/user/pharmacis/record',
      method: 'get'
    })
  } else {
    return request({
      url: '/user/pharmacis/record?pharmacistId=' + id,
      method: 'get'
    })
  }
}

export function getCityList(data) {
  return request({
    url: '/user/doctor/city/list',
    method: 'get',
    params: data
  })
}

export function getStandardList() {
  return request({
    url: '/user/doctor/standard/list',
    method: 'get'
  })
}

export function updateRecordInfo(data) {
  return request({
    url: '/user/pharmacis/record',
    method: 'post',
    data: data,
    timeout: 60000
  })
}

export function changeDoctorStatus(data) {
  return request({
    url: `/user/pharmacis/${data.pharmacistId}/${data.status}`,
    method: 'get'
  })
}
