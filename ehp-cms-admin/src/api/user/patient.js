import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/user/patient/list',
    method: 'get',
    params: data
  })
}

export function get(patientId) {
  return request({
    url: '/user/patient/' + patientId,
    method: 'get'
  })
}

export function getCaseList(data) {
  return request({
    url: '/user/patient/case',
    method: 'get',
    params: data
  })
}

export function getInquirerList(data) {
  return request({
    url: '/user/patient/inquirer/list',
    method: 'get',
    params: data
  })
}

export function getCaseLogList(id, data) {
  return request({
    url: '/user/patient/case/' + id,
    method: 'get',
    params: data
  })
}

export function setCaseLogList(data) {
  return request({
    url: '/user/patient/case/download/report',
    method: 'get',
    params: data
  })
}
export function getFormList(data) {
  return request({
    url: '/user/patient/counselor/form/List',
    method: 'get',
    params: data
  })
}
export function getFollowDetail(counselorFollowId) {
  return request({
    url: '/user/patient/counselor/form/detail?counselorFollowId=' + counselorFollowId,
    method: 'get'
  })
}

export function getCounselorsDetail(patientId) {
  return request({
    url: `user/patient/${patientId}/counselors`,
    method: 'get'
  })
}
