import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/user/doctor/list',
    method: 'get',
    params: data
  })
}

export function get(doctorId) {
  return request({
    url: '/user/doctor/' + doctorId,
    method: 'get'
  })
}

export function authAudit(data) {
  return request({
    url: '/user/doctor/audit/auth',
    method: 'post',
    data
  })
}

export function recordAudit(data) {
  return request({
    url: '/user/doctor/audit/record',
    method: 'post',
    data
  })
}

export function doctorOperation(id, status) {
  return request({
    url: '/user/doctor/' + id + '/' + status,
    method: 'get'
  })
}
export function doctorSetTest(id, type) {
  return request({
    url: '/user/doctor/' + id + '/type',
    method: 'get',
    params: { type: type }
  })
}
export function uploadImg(id, status, data) {
  return request({
    url: '/user/doctor/upload/' + id + '/' + status,
    method: 'post',
    data
  })
}

export function updateDoctor(data) {
  return request({
    url: '/user/doctor/update',
    method: 'post',
    data
  })
}

export function uploadDoctorSign(id, data) {
  return request({
    url: '/user/doctor/upload/seal/' + id,
    method: 'post',
    data
  })
}

export function cityList() {
  return request({
    url: '/user/doctor/city/list',
    method: 'get'
  })
}

export function hospitalList(id) {
  return request({
    url: '/user/doctor/hospital/' + id,
    method: 'get'
  })
}

export function department(data) {
  return request({
    url: '/user/doctor/department',
    method: 'get',
    params: data
  })
}

export function getRecordInfo(id) {
  return request({
    url: '/user/doctor/record/' + id,
    method: 'get'
  })
}
export function getCityList() {
  return request({
    url: '/user/doctor/city/list',
    method: 'get'
  })
}
export function getStandardList() {
  return request({
    url: '/user/doctor/standard/list',
    method: 'get'
  })
}

export function updateRecordInfo(data) {
  return request({
    url: '/user/doctor/record',
    method: 'post',
    data: data,
    timeout: 60000
  })
}

export function getCommentList(data) {
  return request({
    url: '/user/doctor/consult/comment/list',
    method: 'get',
    params: data
  })
}

export function getComplaintList(data) {
  return request({
    url: '/user/doctor/consult/complaint/list',
    method: 'get',
    params: data
  })
}
export function getImageOCR(data) {
  return request({
    url: '/user/doctor/ocr',
    method: 'post',
    data
  })
}
export function saveNumber(data) {
  return request({
    url: '/user/doctor/saveNumber',
    method: 'post',
    data
  })
}
export function addPharmacy(warehouseId, doctorId) {
  return request({
    url: `/pharmacy/warehouse/${warehouseId}/${doctorId}`,
    method: 'post'
  })
}

export function pharmacyList(doctorId, data) {
  return request({
    url: '/pharmacy/warehouse/list',
    method: 'get',
    params: { doctorId: doctorId, ...data }
  })
}
export function delPharmacy(warehouseId, doctorId) {
  return request({
    url: `/pharmacy/warehouse/${warehouseId}/${doctorId}`,
    method: 'delete'
  })
}
export function querySearchKey(data) {
  return request({
    url: '/pharmacy/warehouse/select',
    method: 'get',
    params: data
  })
}
export function setConfig(query) {
  return request({
    url: '/user/doctor/recomflag/update' + query,
    method: 'post'
  })
}

// 脱敏
export function getDecrypt(data) {
  return request({
    url: '/user/common/decrypt',
    method: 'post',
    data
  })
}

// 医生签名文件旋转
export function rotate(data, doctorId) {
  return request({
    url: `/user/doctor/rotate/seal/${doctorId}`,
    method: 'post',
    data
  })
}
