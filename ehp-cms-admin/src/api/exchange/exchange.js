import request from '@/utils/request'

function orderlist(data) {
  return request({
    url: '/oms/return/order/list',
    method: 'get',
    params: data
  })
}

function getRetreatDetail(id) {
  return request({
    url: '/oms/return/order/detail/' + id,
    method: 'get'
  })
}

function getChangeDetail(id) {
  return request({
    url: '/oms/return/order/exchange/' + id,
    method: 'get'
  })
}

function getRepairDetail(id) {
  return request({
    url: '/oms/return/order/supplementary/' + id,
    method: 'get'
  })
}

function saveRetreat(data) {
  return request({
    url: '/oms/return/order/save',
    method: 'post',
    data
  })
}

function saveChange(data) {
  return request({
    url: '/oms/return/order/exchange/save',
    method: 'post',
    data
  })
}

function saveRepair(data) {
  return request({
    url: '/oms/return/order/supplementary/save',
    method: 'post',
    data
  })
}

function orderSubmit(data) {
  return request({
    url: '/oms/return/order/submit',
    method: 'post',
    data
  })
}

function orderInvalid(data) {
  return request({
    url: '/oms/return/order/invalid',
    method: 'post',
    data
  })
}

function orderReset(data) {
  return request({
    url: '/oms/return/order/reset',
    method: 'post',
    data
  })
}

function orderAudit(data) {
  return request({
    url: '/oms/return/order/audit',
    method: 'post',
    data
  })
}

function orderConfirm(data) {
  return request({
    url: '/oms/return/order/confirm',
    method: 'post',
    data
  })
}

function logistics() {
  return request({
    url: '/oms/return/order/logistics/company',
    method: 'get'
  })
}

export default {
  orderlist,
  getRetreatDetail,
  getChangeDetail,
  getRepairDetail,
  saveRetreat,
  saveChange,
  saveRepair,
  orderSubmit,
  orderInvalid,
  orderReset,
  orderAudit,
  orderConfirm,
  logistics
}
