import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/sys/role/list',
    method: 'get',
    params: data
  })
}
export function getRoleList() {
  return request({
    url: '/sys/role/select',
    method: 'get'
  })
}

export function get(data) {
  return request({
    url: '/sys/role/info/' + data,
    method: 'get'
  })
}

export function del(data) {
  return request({
    url: '/sys/role/delete/',
    method: 'post',
    data
  })
}

export function create(data) {
  return request({
    url: 'sys/role/save',
    method: 'post',
    data
  })
}

export function update(data) {
  return request({
    url: 'sys/role/update',
    method: 'post',
    data
  })
}

