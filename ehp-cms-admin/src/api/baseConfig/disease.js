import request from '@/utils/request'

// 疾病管理列表
export function getList(data) {
  return request({
    url: '/base/disease',
    method: 'get',
    params: data
  })
}

// 疾病管理-新增编辑疾病
export function editDisease(data) {
  return request({
    url: '/base/disease',
    method: 'post',
    params: data
  })
}

// 疾病管理-删除疾病
export function deleteDisease(id) {
  return request({
    url: '/base/disease?id=' + id,
    method: 'delete'
  })
}
