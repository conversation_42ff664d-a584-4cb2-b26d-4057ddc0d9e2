import request from '@/utils/request'

// 合作医院列表
export function getHospList(data) {
  return request({
    url: '/base/partner/hospital',
    method: 'get',
    params: data
  })
}

// 合作医院-新增编辑
export function editHospital(data) {
  return request({
    url: '/base/partner/hospital',
    method: 'post',
    data: data
  })
}

// 合作医院-删除
export function deleteHospital(hospitalId) {
  return request({
    url: '/base/partner/hospital?hospitalId=' + hospitalId,
    method: 'delete'
  })
}

// 文件上传
export function upload(data) {
  return request({
    url: '/storage',
    method: 'post',
    data
  })
}
