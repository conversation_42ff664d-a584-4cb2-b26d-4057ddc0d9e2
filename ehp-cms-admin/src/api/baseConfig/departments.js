import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/base/department/list',
    method: 'get',
    params: data
  })
}

export function getListTree(data) {
  return request({
    url: '/base/department/tree',
    method: 'get',
    params: data
  })
}

export function addDepartment(data) {
  return request({
    url: '/base/department/save',
    method: 'post',
    data
  })
}

export function updateHospital(data, id) {
  return request({
    url: '/user/hospital/' + id,
    method: 'put',
    data
  })
}

export function changedepartmentStatus(data) {
  return request({
    url: `/base/department/departmentStatus/${data.id}/${data.status}`,
    method: 'post'
  })
}

export function getDicts(data) {
  return request({
    url: '/base/standard/dicts',
    method: 'get',
    params: data
  })
}
