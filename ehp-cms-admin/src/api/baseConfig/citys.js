import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/base/city/list',
    method: 'get',
    params: data
  })
}

export function addCity(data) {
  return request({
    url: '/base/city/save',
    method: 'post',
    data
  })
}

export function changeCityStatus(data) {
  return request({
    url: `/base/city/cityStatus/${data.id}/${data.status}`,
    method: 'post'
  })
}
