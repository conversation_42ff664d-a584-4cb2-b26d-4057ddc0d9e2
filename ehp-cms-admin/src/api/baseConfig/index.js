import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/base/hospital/list',
    method: 'get',
    params: data
  })
}

export function addHospital(data) {
  return request({
    url: '/base/hospital/save',
    method: 'post',
    data
  })
}

export function updateHospital(data, id) {
  return request({
    url: '/base/hospital/' + id,
    method: 'put',
    data
  })
}
export function changehospitalStatus(data) {
  return request({
    url: `/base/hospital/hospitalStatus/${data.id}/${data.status}`,
    method: 'post'
  })
}
