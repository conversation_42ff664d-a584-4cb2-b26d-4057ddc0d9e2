import request from '@/utils/request'

export function getList(data) {
  return request({
    url: '/base/agreement/list',
    method: 'get',
    params: data
  })
}

export function addArticle(data) {
  return request({
    url: '/base/agreement/save',
    method: 'post',
    data
  })
}

export function updateArticle(data) {
  return request({
    url: '/base/agreement/update',
    method: 'post',
    data
  })
}
export function getDetail(id) {
  return request({
    url: `/base/agreement/detail/${id}`,
    method: 'get'
  })
}

export function deleteArticle(id) {
  return request({
    url: `/base/agreement/${id}`,
    method: 'DELETE'
  })
}
export default {
  getList,
  addArticle,
  updateArticle,
  deleteArticle,
  getDetail
}
