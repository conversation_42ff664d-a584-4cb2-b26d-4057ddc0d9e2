<template>
  <div ref="editor" class="text" />
</template>

<script>
import E from 'wangeditor'
import xss from 'xss'
import { upload } from '@/utils/upload'
export default {
  props: {
    textValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      editor: false,
      isInit: false
    }
  },
  watch: {
    textValue: function(val, val2) {
      if (!this.isInit) {
        this.isInit = true
        // this.editor.txt.html(this.textValue)
      }

    }
  },
  mounted() {
    this.editor = new E(this.$refs.editor)
    // 自定义菜单配置
    this.editor.config.zIndex = 1
    // 文件上传
    this.editor.config.customUploadImg = function(files, insert) {
      // files 是 input 中选中的文件列表
      // insert 是获取图片 url 后，插入到编辑器的方法
      files.forEach(image => {
        upload(image).then(res => {
          console.log(res, 'insert')
          insert(res)
        })
      })
    }
    this.editor.config.onchange = (html) => {
      const safeHtml = xss(html)
      // console.log('处理过 xss 攻击的 html', safeHtml)
      this.$emit('update:textValue', safeHtml)
    //   this.textValue = html
    }
    //隐藏插入网络图片
    this.editor.config.showLinkImg = false
    this.editor.config.excludeMenus = [
      'emoticon',
      'video'
    ]
    this.editor.create()
    // 初始化数据
    this.editor.txt.html(xss(this.textValue))
  },
  methods: {
    setValue(html) {
      const safeHtml = xss(html)
      this.editor.txt.html(safeHtml)
    }
  }
}
</script>

<style>
.w-e-text-container {
  height: 400px !important; /*!important是重点，因为原div是行内样式设置的高度300px*/
}
</style>
