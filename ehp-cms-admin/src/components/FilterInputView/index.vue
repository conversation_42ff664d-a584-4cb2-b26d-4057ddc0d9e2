<template>
  <div class="">
    <el-form-item
      :label="label"
      :prop="prop"
    >
      <div class="form-item-box">
        <el-input
          v-model="valueSliceComputed"
          readonly
        />
        <i
          class="el-icon-view"
          @click.stop="isLook = !isLook"
        ></i>
      </div>
    </el-form-item>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    label: {
      type: String,
      default: ''
    },
    prop: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
    // isLook: {
    //   type: Boolean,
    //   default: false
    // }
  },
  data() {
    return {
      isLook: false
    }
  },
  computed: {
    valueSliceComputed() {
      return this.isLook ? this.value : this.filterReplacepos(this.value)
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    filterReplacepos(text) {
      var mystr
      if (!text) {
        return ''
      }
      if (this.type === 'phone') {
        mystr = text.slice(0, 3) + '****' + text.slice(text.length - 4)
      }
      if (this.type === 'idCard') {
        mystr = text.slice(0, 4) + '****' + text.slice(text.length - 4)
      }
      if (this.type === 'email') {
        mystr = text.slice(0, 5) + '****' + text.slice(text.length - 3)
      }
      if (this.type === 'name') {
        mystr = '*' + text.substring(text.length - 1)
      }
      return mystr
    }
  }
}
</script>
<style scoped>
.form-item-box {
  display: flex;
  align-items: center;
}
.el-icon-view {
  margin-left: 10px;
}
</style>
