<template>
  <div>
    <el-select v-model="currentField.defaultValue" placeholder="请选择">
      <el-option
        v-for="item in currentField.options"
        :key="item.optionValue"
        :value="item.optionValue"
        :label="item.optionValue"
      />
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'SelectList',
  props: ['currentField'],
  data() {
    return {
      // value: ""
    }
  }
}
</script>
