<template>
  <div>
    <div class="demo-image__preview">
      <!--el-image
		style="width: 100px; height: 100px"
		:src="url"
		:preview-src-list="srcList">
		</el-image-->
      <div class="imgdiv"><i class="el-icon-plus" /></div>
      <div class="imgtips">选择图片（最多{{ currentField.imglength }}张），单张图片最大4M</div>
    </div>
  </div>
</template>
<style>
.imgdiv{width:80px;height:80px;border:1px dashed #ccc;background-color:#fff;display:flex;justify-content:center;align-items:center;}
.imgdiv i{font-size:30px;}
.imgtips{font-size:12px;margin-top:10px;}
</style>
<script>
export default {
  name: 'EImage',
  props: ['currentField']
}
</script>
