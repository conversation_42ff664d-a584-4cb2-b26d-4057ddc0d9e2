<template>
  <div>
    <el-checkbox-group v-model="currentField.defaultValue" class="">
      <div v-for="(item, index) in currentField.options" :key="index">
        <el-checkbox
          :label="item.optionValue"
        >
          {{ item.optionLabel }}
        </el-checkbox>
      </div>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  name: 'Checkbox',
  props: ['currentField'],
  data() {
    return {
      // checkList: [0]
    }
  }
}
</script>
