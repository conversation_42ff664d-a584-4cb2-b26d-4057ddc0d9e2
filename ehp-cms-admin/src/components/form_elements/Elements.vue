<template>
  <div>

    <div class="filedtitle">添加问题
      <el-tooltip class="item" effect="dark" content="选择合适的题目类型，点击添加到问卷底部，或拖拽到希望添加的位置" placement="right"><i class="el-icon-warning-outline" />
      </el-tooltip>
    </div>

    <div class="el-tabsfiled">
      <draggable
        :list="fields"
        :clone="clone"
        class="dragArea"
        :options="dropElementOptions"
        @start="onStart"
      >
        <div
          v-for="(field, index) in fields"
          :key="index"
          class="inputbox"
          :class="{ 'is-disabled': checkStopDragCondition(field) }"
          @click="createForm(field)"
        >
          <span class="iconfont" :class="field.icon" /><span>{{ field.text }}</span>&nbsp;<el-tooltip v-if="field.tooltip" class="item" effect="dark" :content="field.tooltip" placement="right"><i class="el-icon-warning-outline" /></el-tooltip>
        </div>
      </draggable>
    </div>
  </div>
</template>
<style>
.el-tabsfiled{padding:10px 10px 10px 0;}
.filedtitle{font-size:14px;padding:10px 10px 0 0;}
.inputbox{cursor:move;background: #f4f6fc;border: 1px solid #f4f6fc;font-size:12px;display:flex;align-items:center;margin-bottom:10px;padding:5px 0 5px 10px;}
.inputbox .iconfont{margin-right:5px;}
</style>
<script>
import { mapGetters } from 'vuex'
import { FormBuilder } from '@/components/form_elements/formbuilder'
import draggable from 'vuedraggable'

export default {
  name: 'Elements',
  // store: ['forms', 'activeForm'],
  components: { draggable },
  data() {
    return {
      // forms:window.vm.$store.state.formbuilder.forms,
      // activeForm:window.vm.$store.state.formbuilder.activeForm,
      fields: FormBuilder.$data.fields,
      dropElementOptions: FormBuilder.$data.dropElementOptions
    }
  },
  computed: {
    ...mapGetters(['forms', 'activeForm'])
  },
  methods: {
    createForm(field) {
      console.log('createForm', field, window.vm.$store.state.formbuilder.activeForm, this)
      var index = 0
      for (var i = 0; i < window.vm.$store.state.formbuilder.forms.length; i++) {
        if (window.vm.$store.state.formbuilder.forms[i] === window.vm.$store.state.formbuilder.activeForm) {
          // console.log(i,'===window.vm.$store.state.formbuilder.activeForm')
          index = i
        }
      }
      window.vm.$store.state.formbuilder.forms.splice(index + 1, 0, this.clone(field))
      // window.vm.$store.state.formbuilder.forms.push(this.clone(field));
    },
    clone(field) {
      var newField = {
        fieldType: field.name,
        inputname: 'input_' + new Date().getTime(),
        label: field.text,
        text: field.text
        // isRequired:field.isRequired,
        // helpBlockText:field.helpBlockText,
        // hasOptions:field.hasOptions,
        // isUnique: field.isUnique,
      }
      if (field.name !== 'SectionBreak') {
        newField.isRequired = field.isRequired
        newField.helpBlockText = field.helpBlockText
      }
      // var newField  = {
      //   fieldType: field.name,
      //   label:field.text,
      //   isUnique: field.isUnique,
      //   inputname: 'input_' + new Date().getTime()
      // }
      // if (field.name === 'TextInput') {
      // newField['isTitle'] = false
      // newField ["isTitle"] = true;
      // for(var i=0;i<this.forms.length;i++){
      //   if(this.forms[i].fieldType ==== 'TextInput' && this.forms[i].isTitle){
      //     newField ["isTitle"] = false;
      //     console.log(this.forms[i])
      //     continue;
      //   }
      // }
      // }
      // Show placeholder
      if (field.name === 'TextInput' || field.name === 'LongTextInput') {
        newField['isPlaceholderVisible'] = true
        newField['placeholder'] = ''
      }

      // Decide whether display label, required field, helpblock
      // if (field.group === "form"){
      // newField ["label"] = "单选题";
      // newField ["isHelpBlockVisible"] =  false;
      // newField ["helpBlockText"] = '此属性用于指定对该字段进行一些附加说明，一般用来指导填写者输入。';
      // newField ["isRequired"] = false;
      // }

      // if (field.group === "button"){
      //   newField ["buttonText"] = "提交您的表格";
      // }

      // if (field.name === "TextEditor"){
      //   newField ["fieldText"] = "开始打字...";
      // }

      if (field.name === 'SectionBreak') {
        newField['label'] = '说明'
        newField['breakText'] = '请在右侧面板添加段落说明信息'
      }

      if (field.name === 'eImage') {
        newField['imglength'] = 1
      }

      // Add dummy options for loading the radio/checkbox
      if (field.name === 'RadioButton' || field.name === 'RadioButtonOther' || field.name === 'Checkbox' || field.name === 'CheckboxOther' || field.name === 'SelectList') {
        newField['options'] = [
          { optionLabel: '选项1', optionValue: this.randomString() },
          { optionLabel: '选项2', optionValue: this.randomString() }
        ]
      }
      if (field.name === 'RadioButton' || field.name === 'RadioButtonOther' || field.name === 'SelectList') {
        newField.defaultValue = ''
      }
      if (field.name === 'RadioButtonOther') {
        newField.defaultValue = {
          value: ''
        }
      }
      if (field.name === 'CheckboxOther') {
        newField.defaultValue = {
          value: []
        }
      }
      if (field.name === 'GenderButton') {
        newField['options'] = [
          { optionLabel: '男', optionValue: 1 },
          { optionLabel: '女', optionValue: 2 }
        ]
      }
      if (field.name === 'GenderButton') {
        newField.defaultValue = ''
      }
      if (field.name === 'Checkbox') {
        newField.defaultValue = []
      }
      console.log('formbuilder/setActiveForm', newField)
      window.vm.$store.dispatch('formbuilder/setActiveForm', newField)
      return newField
    },
    onStart() {
      console.log('start liao')
    },
    checkStopDragCondition(field) {
      var form = this.forms
      var formArray = []

      for (var key in form) {
        formArray.push(form[key]['fieldType'])
      }

      // Check if the fieldname exist in formArray
      // And when the field.isUnique too
      return this._includes(formArray, field.name) && field.isUnique
      // return field.isUnique;
    },
    _includes(arr, val) {
      var testStr = ',' + arr.join(',') + ','
      return testStr.indexOf(',' + val + ',') !== -1
    },
    randomString(e) {
      e = e || 4
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      var a = t.length
      var n = ''
      for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    }
  }
}
</script>

<style lang="scss" scoped>
  .button__sidebar {
    width: 100%;
    margin-bottom: 10px;

    .is-disabled & {
      opacity: 0.4;
    }
  }

  // Display this ghost in <main> only
  .wrapper--forms .sortable__ghost {
    position: relative;
    width: 100%;
    border-bottom: 2px solid black;
    margin-top: 2px;
    margin-bottom: 2px;

    [type="button"] {
      display: none;
    }

    &:before {
      content: "Drag it here";
      background-color: black;
      color: white;
      position: absolute;
      left: 50%;
      font-size: 10px;
      border-radius: 10px;
      line-height: 15px;
      padding: 0 10px;
      top: -6px;
      transform: translateX(-50%);
    }
  }
</style>
