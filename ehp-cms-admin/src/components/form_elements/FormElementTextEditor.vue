<template>
  <div>
    <!-- <tinyEditor v-model="currentField.inputname" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import tinyEditor from '@/components/tinymce/tinymce'
export default {
  name: 'TextEditor',
  components: {
    // tinyEditor
  },
  props: ['currentField'],
  data() {
    return {
      date: ''
    }
  },
  computed: {
    ...mapGetters([
      'imagesUploadApi',
      'baseApi'
    ])
  },
  mounted() {
    // var editor = new E(this.$refs.editor)
    // // 自定义菜单配置
    // editor.config.zIndex = 1
    // // 文件上传
    // editor.config.customUploadImg = function(files, insert) {
    //   // files 是 input 中选中的文件列表
    //   // insert 是获取图片 url 后，插入到编辑器的方法
    //   files.forEach(image => {
    //     uploadOss(image).then(res => {
    //       // const data = res.data
    //       // const url = _this.baseApi + '/file/' + data.type + '/' + data.realName
    //       // console.log(res.res, signatureUrl(image.name))
    //       signatureUrl(image.name).then(res => {
    //         console.log(res)
    //         insert(res)
    //       })
    //       // insert(res.res.requestUrls[0])
    //     })
    //   })
    // }
    // editor.config.onchange = (html) => {
    //   // this.currentField.inputname = html
    // }
    // editor.create()
  }
}
</script>
