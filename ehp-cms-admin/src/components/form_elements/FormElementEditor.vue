<template>
  <div>
    <medium-editor
      :text="currentField.fieldText"
      :options="options"
      custom-tag="div"
      @edit="processEditOperation"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import MediumEditor from 'vue2-medium-editor'

export default {
  name: 'TextEditor',
  props: ['currentField'],
  // store: ['activeForm'],
  data() {
    return {
      // activeForm:vm.$store.state.formbuilder.activeForm,
      options: {
        toolbar: {
          buttons: ['bold', 'italic', 'underline', 'h1', 'h2', 'anchor', 'image']
        },
        extensions: {
          'imageDragging': {} // Disable drag and drop image
        }
      }
    }
  },
  computed: {
    ...mapGetters(['activeForm'])
    // activeForm: {
    //   get() {
    //     return this.$store.state.formbuilder.activeForm
    //   },
    //   set(val) {
    //     this.$store.state.formbuilder.activeForm.fieldText = val;
    //   }
    // }
  },
  components: {
    MediumEditor
  },
  methods: {
    // Update the field Text so it show the updated text evytime user reposition the element
    processEditOperation: function(operation) {
      this.activeForm.fieldText = operation.api.origElements.innerHTML
    }
  }
}
</script>
