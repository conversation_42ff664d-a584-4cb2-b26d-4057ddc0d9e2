<template>
  <div>
    <div v-for="(item,index) in currentField.options" :key="index">
      <el-radio
        v-model="currentField.defaultValue.value"
        :label="item.optionValue"
      >
        {{ item.optionLabel }}
      </el-radio>
      <el-input
        v-if="item.optionOther"
        :placeholder="currentField.isPlaceholderVisible ? currentField.placeholder : ''"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'RadioButtonOther',
  props: ['currentField'],
  data() {
    return {
      // radio: '1'
    }
  }
}
</script>
