<template>
  <div>
    <el-checkbox-group v-model="currentField.defaultValue.value">
      <div v-for="(item, index) in currentField.options" :key="index">
        <el-checkbox
          :label="item.optionValue"
        >
          {{ item.optionLabel }}
        </el-checkbox>
        <el-input
          v-if="item.optionOther"
          :placeholder="currentField.isPlaceholderVisible ? currentField.placeholder : ''"
        />
      </div>
    </el-checkbox-group>
  </div>
</template>

<script>
export default {
  name: 'CheckboxOther',
  props: ['currentField'],
  data() {
    return {
      // checkList: [0]
    }
  }
}
</script>
