<template>
  <div>
    <div class="protitle">题目编辑</div>
    <div v-show="activeForm.hasOwnProperty('label')" class="textTitle">{{ activeForm.label }}</div>
    <div class="el-tabs__inner">
      <el-form
        ref="fieldProperties"
        :model="fieldProperties"
        :rules="rules"
        :label-position="labelPosition"
      >

        <el-form-item
          v-show="activeForm.hasOwnProperty('text')"
        >
          <label for="">问题&nbsp;<el-tooltip class="item" effect="dark" content="此属性用于告诉填写者应该在该字段中输入什么样的内容。" placement="bottom-end"><i class="el-icon-warning-outline">&nbsp;(<span style="color:#f00;">*</span>)</i>
          </el-tooltip></label>

          <el-input v-model="activeForm.text" @blur="changeValue">{{ activeForm.text }}</el-input>
        </el-form-item>

        <el-form-item
          v-show="activeForm.hasOwnProperty('breakText')"
          label="描述文本"
        >
          <el-input v-model="activeForm.breakText">
            {{ activeForm.breakText }}
          </el-input>
        </el-form-item>
        <el-form-item v-show="activeForm.hasOwnProperty('helpBlockText')" label="提示">
          <div class="helptips">此属性用于指定对该字段进行一些附加说明，一般用来指导填写者输入。</div>
          <!-- <el-switch v-model="activeForm.isHelpBlockVisible"></el-switch> -->
          <el-input
            v-model="activeForm.helpBlockText"
            type="textarea"
          />
        </el-form-item>
        <div v-show="activeForm.hasOwnProperty('isRequired')" class="el-form-item el-form-item--mini" style="display:flex;padding-top:3px;margin-bottom:0;">
          <label class="el-form-item__label">是否必填？</label>
          <div class="el-form-item__content"><el-switch v-model="activeForm.isRequired" /></div>
        </div>
        <!--el-form-item label="是否必填？"
						v-show="activeForm.hasOwnProperty('isRequired')">
			<el-switch v-model="activeForm.isRequired"></el-switch>
		</el-form-item-->

        <el-form-item v-show="activeForm.options" v-if="activeForm.fieldType==='RadioButton' || activeForm.fieldType==='RadioButtonOther' || activeForm.fieldType==='SelectList' || activeForm.fieldType==='Checkbox' || activeForm.fieldType==='CheckboxOther'" label="选择">
          <!-- <el-checkbox-group v-model="activeForm.defaultValue"> -->
          <ul>
            <li
              v-for="(item, index) in activeForm.options"
              :key="index"
              class="properties__optionslist"
            >
              <el-row :gutter="5">
                <!--el-col v-if="activeForm.fieldType==='RadioButton' || activeForm.fieldType==='SelectList'" style="overflow:hidden;" :span="2">
                  <el-radio v-model="activeForm.defaultValue" :label="item.optionValue" @click.native.prevent="clickitem(item.optionValue)" />
                </el-col>
                <el-col v-else-if="activeForm.fieldType==='RadioButtonOther'" style="overflow:hidden;" :span="2">
                  <el-radio v-model="activeForm.defaultValue.value" :label="item.optionValue" @click.native.prevent="clickitemOther(item.optionValue)" />
                </el-col>
                <el-col v-else-if="activeForm.fieldType==='Checkbox'" style="overflow:hidden;" :span="2">
                  <el-checkbox v-model="activeForm.defaultValue" :label="item.optionValue" />
                </el-col>
                <el-col v-else-if="activeForm.fieldType==='CheckboxOther'" style="overflow:hidden;" :span="2">
                  <el-checkbox v-model="activeForm.defaultValue.value" :label="item.optionValue" />
                </el-col-->
                <el-col v-if="activeForm.fieldType==='RadioButton' || activeForm.fieldType==='SelectList'" :span="18">
                  <el-input v-if="item.optionLabel===activeForm.defaultValue" v-model="item.optionLabel">{{ item.optionLabel }}</el-input>
                  <el-input v-else v-model="item.optionLabel">{{ item.optionLabel }}</el-input>
                </el-col>
                <el-col v-else-if="activeForm.fieldType==='RadioButtonOther'" :span="18">
                  <el-input v-if="item.optionLabel===activeForm.defaultValue.value" v-model="item.optionLabel">{{ item.optionLabel }}</el-input>
                  <el-input v-else v-model="item.optionLabel">{{ item.optionLabel }}</el-input>
                </el-col>
                <el-col v-else-if="activeForm.fieldType==='Checkbox'" :span="18">
                  <el-input v-model="item.optionLabel" @focus="focusInput" @input="checkInput">{{ item.optionLabel }}</el-input>
                  <!-- <el-input v-else v-model="item.optionLabel">{{item.optionLabel}}</el-input> -->
                </el-col>
                <el-col v-else-if="activeForm.fieldType==='CheckboxOther'" :span="18">
                  <el-input v-model="item.optionLabel" @focus="focusInputOther" @input="checkInputOther">{{ item.optionLabel }}</el-input>
                  <!-- <el-input v-else v-model="item.optionLabel">{{item.optionLabel}}</el-input> -->
                </el-col>
                <el-col :span="4">
                  <el-button
                    v-show="activeForm.options.length > 1"
                    @click="deleteOption(activeForm.options, index)"
                  >
                    <i class="el-icon-delete" />
                  </el-button>
                </el-col>
              </el-row>
            </li>
          </ul>
          <!-- </el-checkbox-group> -->
          <el-button type="text" @click="addOption(activeForm.options)">
            <i class="el-icon-plus" />
            添加项
          </el-button>
          <el-button v-if="(activeForm.fieldType==='RadioButtonOther' || activeForm.fieldType==='CheckboxOther') && !activeForm.optionOther" type="text" @click="addOptionOther(activeForm.options)">
            <i class="el-icon-plus" />
            其他项
          </el-button>
        </el-form-item>

        <!-- Show only when 'isPlacehodlerVisible' key exist -->
        <el-form-item
          v-show="activeForm.hasOwnProperty('isPlaceholderVisible')"
          label="占位符"
        >
          <!-- <el-switch v-model="activeForm.isPlaceholderVisible"></el-switch> -->
          <div class="helptips">设置后，此值将作为默认值显示在该字段的输入框中。如果不需要设置默认值，请将此处留空。</div>
          <el-input
            v-model="activeForm.placeholder"
          >
            {{ activeForm.placeholder }}
          </el-input>
        </el-form-item>

        <el-form-item
          v-show="activeForm.hasOwnProperty('buttonText')"
          label="按钮文本"
        >
          <el-input v-model="activeForm.buttonText">
            {{ activeForm.buttonText }}
          </el-input>
        </el-form-item>

        <el-form-item
          v-show="activeForm.hasOwnProperty('fieldText')"
          label="代码视图"
        >
          <el-input
            v-model="activeForm.fieldText"
            type="textarea"
            :rows="10"
          >
            {{ activeForm.fieldText }}
          </el-input>
        </el-form-item>
        <el-form-item
          v-show="activeForm.fieldType === 'eImage'"
          label="最大图片数量"
        >
          <el-select v-model="activeForm.imglength">
            <el-option
              v-for="item in imgoptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>

      </el-form>
    </div>
  </div>
</template>
<style scoped>
ul,li{padding:0;margin:0;}
li{list-style:none;}
.protitle{font-size:14px;padding:10px 10px 0;}
.el-tabs__inner{padding:10px;}
.helptips{font-size:12px;color:#606266;line-height:20px;padding-bottom:2px;}
.el-form-item--mini.el-form-item{margin-bottom:5px;}
.el-form--label-top .el-form-item__label{padding-bottom:5px;}
.textTitle{text-align:center;margin:10px 10px 0;background-color:rgba(236,245,255,.3);font-size:14px;color:#606266;line-height:30px;}
.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner{box-shadow:none;}
</style>
<script>
import { mapGetters } from 'vuex'
export default {
  name: 'Properties',
  // store: ['activeForm'], // Get the form data from Home
  data() {
    return {
      // activeForm:vm.$store.state.formbuilder.activeForm,
      labelPosition: 'top',
      fieldProperties: {},
      rules: {},
      imgoptions: [1, 2, 3, 4, 5],
      changeInputFlag: false,
      changeInputText: ''
    }
  },
  computed: {
    ...mapGetters(['activeForm'])
  },
  methods: {
    deleteOption(option, index) {
      if (this.activeForm.fieldType === 'RadioButton' || this.activeForm.fieldType === 'SelectList') {
        if (option[index].optionValue === this.activeForm.defaultValue) {
          this.activeForm.defaultValue = ''
        }
      } else if (this.activeForm.fieldType === 'Checkbox') {
        this.activeForm.defaultValue.forEach(function(item, cindex, arr) {
          if (item === option[index].optionValue) {
            arr.splice(cindex, 1)
          }
        })
      } else if (this.activeForm.fieldType === 'RadioButtonOther') {
        this.activeForm.defaultValue.value = ''
      } else if (this.activeForm.fieldType === 'CheckboxOther') {
        this.activeForm.defaultValue.value.forEach(function(item, cindex, arr) {
          if (item === option[index].optionValue) {
            arr.splice(cindex, 1)
          }
        })
      }
      if (option[index].hasOwnProperty('optionOther')) {
        delete this.activeForm['optionOther']
      }
      this.$delete(option, index)
    },
    changeValue() {
      if (this.activeForm.text === '') {
        this.activeForm.text = this.activeForm.label
      }
    },
    addOption(option) {
      const count = option.length + 1

      console.log(option, this.activeForm)
      option.push({
        optionLabel: '选项 ' + count,
        optionValue: this.randomString()
      })
    },
    addOptionOther(option) {
      option.push({
        optionLabel: '其他',
        optionOther: true,
        // optionText: '',
        optionValue: this.randomString()
      })
      this.activeForm.optionOther = true
    },

    /* radioInput(val){
this.activeForm.defaultValue = val
},*/

    checkInput(val) {
      // console.log('checkInput',val,this.activeForm.defaultValue,this.activeForm.options)
      this.activeForm.defaultValue[this.changeInputFlag] = val
    },

    focusInput(val) {
      // console.log(val.target.value,this.activeForm.defaultValue.indexOf(val.target.value))
      if (this.activeForm.defaultValue.indexOf(val.target.value) !== -1) {
        this.changeInputFlag = this.activeForm.defaultValue.indexOf(val.target.value)
        this.changeInputText = val.target.value
      } else {
        this.changeInputFlag = false
      }
    },
    checkInputOther(val) {
      // console.log('checkInput',val,this.activeForm.defaultValue,this.activeForm.options)
      this.activeForm.defaultValue[this.changeInputFlag].value = val
    },

    focusInputOther(val) {
      // console.log(val.target.value,this.activeForm.defaultValue.indexOf(val.target.value))
      if (this.activeForm.defaultValue.value.indexOf(val.target.value) !== -1) {
        this.changeInputFlag = this.activeForm.defaultValue.value.indexOf(val.target.value)
        this.changeInputText = val.target.value
      } else {
        this.changeInputFlag = false
      }
    },
    clickitem(e) {
      console.log(e)
      e === this.activeForm.defaultValue ? this.activeForm.defaultValue = '' : this.activeForm.defaultValue = e
    },
    clickitemOther(e) {
      console.log(e)
      e === this.activeForm.defaultValue.value ? this.activeForm.defaultValue.value = '' : this.activeForm.defaultValue.value = e
    },
    randomString(e) {
      e = e || 4
      var t = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      var a = t.length
      var n = ''
      for (var i = 0; i < e; i++) n += t.charAt(Math.floor(Math.random() * a))
      return n
    }
  }
}
</script>

<style lang="scss" scoped>
	.properties__optionslist {
		margin-bottom: 5px;
	}
</style>
