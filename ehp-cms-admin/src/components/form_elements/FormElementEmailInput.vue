<template>
  <div class="EmailInput">
    <div class="inputbox">
      <svg-icon class="icon" icon-class="email-ly" />
      <el-input
        style="flex:1"
        :placeholder="currentField.isPlaceholderVisible ? currentField.placeholder : ''"
      />
      <div />
    </div>
  </div>
</template>
<style scoped>
.inputbox{
  border:1px solid #dcdfe6;
  box-sizing: border-box;
  border-radius: 4px;
  vertical-align: middle;
  display: flex;
  justify-items: center;
  width: 220px;
  padding:0 10px;
  background-color: #ffffff;
}
.inputbox .icon{margin-right:5px;}
.EmailInput /deep/ .el-input__inner{
  border:none;
  padding:0;
  /* width: auto; */
}
.EmailInput /deep/ .el-input, .el-textarea{
  min-width: 180px;
}
</style>
<script>
export default {
  name: 'EmailInput',
  props: ['currentField']
}
</script>
