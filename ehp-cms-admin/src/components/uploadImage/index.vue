<template>
  <div class="">
    <el-upload
      ref="upload"
      :action="actionUrl"
      :limit="limit"
      :file-list="value"
      :headers="headers"
      :list-type="listType"
      :on-success="handleSuccess"
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :on-error="onUploadError"
      :before-upload="beforeUpload"
      :accept="accept"
      :on-exceed="handleExceed"
      :class="{hide:readOnly}"
    >
      <div v-if="!readOnly" slot="tip" class="el-upload__tip">
        <span v-if="sizeTips">{{ sizeTips }}</span>
        只能上传{{ accept }}文件且小于{{ fileSize }}KB
      </div>
      <i class="el-icon-plus"></i>
      <div v-if="readOnly" slot="file" slot-scope="{ file }">
        <el-image class="el-upload-list__item-thumbnail avatar" fit="cover" :src="file.url" />
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-delete"
            @click="handlePreview(file)"
          >
            <i class="el-icon-zoom-in"></i>
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="handleDownload(file)"
          >
            <i class="el-icon-download"></i>
          </span>
        </span>
      </div>
    </el-upload>
    <el-dialog append-to-body :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import { getToken, getTokenName } from '@/utils/auth'
import Helper from '@/utils/helper'
export default {
  components: {},
  props: {
    action: {
      type: String,
      default: ''
    },
    limit: {
      type: Number,
      default: 1
    },
    value: {
      type: Array,
      default: () => []
    },
    listType: {
      type: String,
      default: 'picture-card'
    },
    // accept: {
    //   type: String,
    //   default: 'image/png image/jpeg, image/jpg'
    // },
    fileSize: {
      type: Number,
      default: 300
    },
    readOnly: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String,
      default: 'image/png, image/gif, image/jpeg, image/jpg'
    },
    sizeTips: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      headers: {},
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  computed: {
    actionUrl: function() {
      return process.env.VUE_APP_BASE_API + this.action
    }
  },
  created() {
    this.headers[getTokenName()] = getToken()
  },
  mounted() {

  },
  methods: {
    handleSuccess(file, fileList) {
      if (file.data) {
        this.$message.success(file.msg)
        this.$emit('success', fileList)
      } else {
        this.$message.error(file.msg)
      }
    },
    handlePreview(file, fileList) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleRemove(file, fileList) {
      this.$emit('remove', fileList)
    },
    handleDownload(file) {
      Helper.downloadIamge(file.url)
    },
    beforeUpload(file) {
      const isSize = file.size / 1024 < this.fileSize
      console.log(file.size / 1024, isSize, 'isSize')
      if (!isSize) {
        this.$message.error(`上传图片大小不能超过 ${this.fileSize}KB!`)
        return false
      }
    },
    onUploadError(err) {
    },
    handleExceed() {
      this.$message.warning(`图片数量最多为${this.limit}张`)
    }
  }
}
</script>
<style scoped>
/deep/.el-upload-list__item.is-ready {
  display: none;
}
/deep/ .el-upload-list__item {
  transition: none !important;
}

.uploadImage ::v-deep .hide .el-upload--picture-card {
  display: none;
}
.avatar {
    width: 148px;
    height: 148px;
    display: block;
  }
</style>
