<template>
  <div class="">
    <div class="form-item-box">
      <a
        v-if="link"
        class="el-link el-link--primary"
        @click="handleOpenView"
      >
        <span class="el-link--inner">{{ current === index && key === prop && isLook ? row[prop] : filterReplacepos(row[prop]) }}</span>
      </a>
      <span
        v-else
        class="el-link--inner"
      >{{ current === index && key === prop && isLook ? row[prop] : filterReplacepos(row[prop]) }}</span>
      <i
        class="el-icon-view"
        @click.stop="handleLook(index,prop)"
      ></i>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    prop: {
      type: String,
      default: ''
    },
    row: {
      type: null,
      default: null
    },
    type: {
      type: String,
      default: ''
    },
    index: {
      type: Number,
      default: 0
    },
    link: {
      type: Boolean,
      default: false
    },
    list: {
      type: null,
      default: false
    }
  },
  data() {
    return {
      key: '',
      current: 0,
      isLook: false
    }
  },
  computed: {},
  watch: {
    // 监听list数据变化分页初始化 isLook
    list: {
      handler() {
        this.isLook = false
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    handleOpenView() {
      this.$emit('openview', this.row)
    },
    handleLook(index, key) {
      if (this.current !== index && this.isLook) {
        this.isLook = false
      }
      this.key = key
      this.current = index
      this.isLook = !this.isLook ? true : false
    },
    filterReplacepos(text) {
      var mystr
      if (!text) {
        return ''
      }
      if (this.type === 'phone') {
        mystr = text.slice(0, 3) + '****' + text.slice(text.length - 4)
      }
      if (this.type === 'idCard') {
        mystr = text.slice(0, 4) + '****' + text.slice(text.length - 4)
      }
      if (this.type === 'email') {
        mystr = text.slice(0, 5) + '****' + text.slice(text.length - 3)
      }
      if (this.type === 'name') {
        mystr = '*' + text.substring(text.length - 1)
      }
      return mystr
    }
  }
}
</script>
<style scoped>
.form-item-box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.el-icon-view {
  margin-left: 10px;
}
</style>
