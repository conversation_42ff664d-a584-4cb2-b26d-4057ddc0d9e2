<template>
  <Editor v-model="content" :init="editorInit" />
</template>

<script>
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver/theme'
import Editor from '@tinymce/tinymce-vue'
// 引入编辑器功能插件
import 'tinymce/plugins/image'
import 'tinymce/plugins/link'
import 'tinymce/plugins/code'
import 'tinymce/plugins/table'
import 'tinymce/plugins/contextmenu'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/colorpicker'
import 'tinymce/plugins/textcolor'
import 'tinymce/plugins/textcolor'
import 'tinymce/icons/default'
import { uploadOss } from '@/utils/upload'
import { signatureUrl } from '@/utils/ali-oss'
export default {
  name: 'Tinymce',
  components: {
    Editor
  },
  model: {
    event: 'change'
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    editorSetting: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      content: '',
      editorInit: {
        content_css: '/static/tinymce/skins/content/default/empty.css',
        language_url: '/static/tinymce/zh_CN.js',
        language: 'zh_CN',
        skin_url: '/static/tinymce/skins/ui/oxide',
        height: 380,
        plugins: 'image colorpicker textcolor wordcount contextmenu',
        toolbar:
          'bold italic underline strikethrough | fontsizeselect | forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist | outdent indent blockquote | undo redo | link unlink image code | removeformat',
        fontsize_formats: '8px 10px 12px 14px 18px 24px 36px',
        branding: false,
        images_upload_handler: this.handleImgUpload,
        charLimit: 100
      }
    }
  },
  watch: {
    value: function(val) {
      this.content = this.value
    },
    content: function(val) {
      this.handleChange()
    }
  },
  created() {
    Object.assign(this.editorInit, this.editorSetting)
  },
  mounted() {
    tinymce.init({})
  },
  methods: {
    async handleImgUpload(blobInfo, success, failure) {
      const file = blobInfo.blob()
      uploadOss(file).then(() => {
        signatureUrl(file.name).then(res => {
          const reg = new RegExp(/\?OSSAccessKeyId.*$/g, '')
          const url = res.replace(reg, '')
          console.log(url)
          success(url)
        })
      })
    },
    handleChange() {
      const { content } = this
      this.$emit('change', content)
    }
  }
}
</script>
