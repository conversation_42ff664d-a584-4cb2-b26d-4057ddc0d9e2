<template>
  <div>
    <div v-if="isDisabled === 'see'">
      {{
        currentField[currentField.inputname]
          ? currentField[currentField.inputname]
          : "请选择"
      }}
    </div>
    <div v-else @click="pickerOpen">
      <div v-if="currentField[currentField.inputname]" @click="pickerOpen">
        <template v-for="(item, index) in currentField.options">
          <div
            v-if="item.optionValue === currentField[currentField.inputname]"
            :key="index"
          >
            {{ item.optionLabel }}
          </div>
        </template>
      </div>
      <div v-else @click="pickerOpen">请选择</div>
    </div>
    <!--el-select v-model="value" placeholder="Select">
      <el-option
        v-for="item in currentField.options"
        :key="item.optionValue"
        :value="item.optionValue"
        :label="item.optionValue">
      </el-option>
    </el-select-->
    <van-popup v-model="datetimeVisable" position="bottom">
      <van-picker
        show-toolbar
        :columns="currentField.options"
        value-key="optionLabel"
        :default-index="defaultIndex"
        @confirm="pickerConfirm"
        @cancel="pickerCanel"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'SelectList',
  props: ['currentField', 'isDisabled', 'formData'],
  data() {
    return {
      datetimeVisable: false,
      defaultIndex: 0
      // value: ""
    }
  },
  watch: {
    currentField() {
      console.log(
        'watch-SelectList currentField',
        this.currentField,
        this.formData
      )
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      } else if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData == null
      ) {
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.currentField.defaultValue
        )
      }
    }
  },
  created() {
    console.log('SelectList-create', this.currentField)
    // debugger
    if (this.formData && this.formData[this.currentField.inputname]) {
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    } else if (this.formData == null) {
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.currentField.defaultValue
      )
    }
    for (let i = 0; i < this.currentField.options.length; i++) {
      if (
        this.currentField.options[i].optionValue ===
        this.currentField[this.currentField.inputname]
      ) {
        this.defaultIndex = i
      }
    }
  },
  methods: {
    pickerOpen() {
      this.datetimeVisable = true
    },
    pickerCanel() {
      this.datetimeVisable = false
    },
    pickerConfirm(val) {
      this.datetimeVisable = false
      console.log(val)
      // this.currentField.value = this.Common.dateFormat('yyyy-MM-dd',val);
      this.$set(
        this.currentField,
        this.currentField.inputname,
        val.optionValue
      )
      if (val) {
        this.$set(this.currentField, 'isShow', false)
      } else {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
