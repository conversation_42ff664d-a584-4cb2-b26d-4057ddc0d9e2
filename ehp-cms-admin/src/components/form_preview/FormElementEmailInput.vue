<template>
  <div class="EmailInput" style="display: flex;align-items: center;">
    <!-- <van-icon name="envelop-o" /> -->
    <van-field
      v-model="currentField[currentField.inputname]"
      :disabled="isDisabled === 'see'"
      class="field"
      :placeholder="
        currentField.isPlaceholderVisible ? currentField.placeholder : ''
      "
      @blur="blurText"
    />
  </div>
</template>
<style scoped>
.EmailInput {
 border:1px solid #eee;border-radius:5px;padding: 20px;position: relative;
}
.EmailInput /deep/ .field {
  border: none;
}

</style>
<script>
import { validEmail } from '@/utils/validate'
export default {
  name: 'EmailInput',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-EmailInput1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create EmailInput1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  },
  methods: {
    blurText(event) {
      console.log(event, event.target.value)
      if (event.target.value !== '' && validEmail(event.target.value)) {
        this.$set(this.currentField, 'isShow2', false)
      } else if (event.target.value !== '') {
        this.$set(this.currentField, 'isShow2', true)
      } else if (event.target.value === '') {
        this.$set(this.currentField, 'isShow2', false)
      }
      if (event.target.value) {
        this.$set(this.currentField, 'isShow', false)
      } else {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
