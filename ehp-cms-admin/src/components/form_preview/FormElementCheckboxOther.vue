<template>
  <div>
    <van-checkbox-group
      v-model="currentField[currentField.inputname].value"
      :disabled="isDisabled === 'see'"
      @change="onChange"
    >
      <div v-for="(item, index) in currentField.options" :key="index">
        <van-checkbox :name="item.optionValue">{{
          item.optionLabel
        }}</van-checkbox>
        <!-- <van-field
          v-if="
            item.optionOther &&
              currentField[currentField.inputname].value.includes(
                item.optionValue
              )
          "
          v-model="currentField[currentField.inputname].otherValue"
          class="field"
          :disabled="isDisabled === 'see'"
        /> -->
      </div>
    </van-checkbox-group>
  </div>
</template>

<style scoped>
.van-checkbox__label {
  font-size: 30px;
}
</style>
<script>
export default {
  name: 'CheckboxOther',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-CheckboxOther1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      } else if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData == null
      ) {
        console.log('watch-CheckboxOther2', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.currentField.defaultValue
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create CheckboxOther1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    } else if (this.formData == null) {
      console.log(
        'formedit-create CheckboxOther2',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.currentField.defaultValue
      )
    }
  },
  methods: {
    onChange(val) {
      console.log(val)
      if (val.length > 0) {
        this.$set(this.currentField, 'isShow', false)
      } else if (this.currentField.isRequired && val.length === 0) {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
