<template>
  <div style="display: flex;align-items: center;">
    <van-field
      v-model="currentField[currentField.inputname]"
      :disabled="isDisabled === 'see'"
      class="field"
      :placeholder="
        currentField.isPlaceholderVisible ? currentField.placeholder : ''
      "
    />
  </div>
</template>
<style>
.field {
  padding: 5px;
  border:1px solid #eee;
	border-radius:5px
}
.field textarea {
  padding: 5px;
}
</style>
<script>
export default {
  name: 'TextInput',
  props: ['currentField', 'isDisabled', 'formData'],
  data() {
    return {
      show: false,
      isSay: false,
      isApp: false,
      time: 0
    }
  },
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-TextInput1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create TextInput1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  }
}
</script>
