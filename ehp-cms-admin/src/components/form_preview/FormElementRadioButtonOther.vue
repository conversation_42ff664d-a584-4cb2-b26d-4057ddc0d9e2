<template>
  <div>
    <van-radio-group
      v-model="currentField[currentField.inputname].value"
      :disabled="isDisabled === 'see'"
      @change="onChange"
    >
      <template v-for="(item, index) in currentField.options">
        <van-radio :key="index" :name="item.optionValue">{{
          item.optionLabel
        }}</van-radio>
        <van-field
          v-if="
            item.optionOther &&
              currentField[currentField.inputname].value === item.optionValue
          "
          :key="index + '_text'"
          v-model="currentField[currentField.inputname].otherValue"
          class="field"
          :disabled="isDisabled === 'see'"
        />
      </template>
    </van-radio-group>
  </div>
</template>

<style scoped>
.van-radio__label {
  font-size: 30px;
}
</style>
<script>
export default {
  name: 'RadioButtonOther',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log(
          'watch-RadioButtonOther1',
          this.currentField,
          this.formData
        )
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      } else if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData == null
      ) {
        console.log(
          'watch-RadioButtonOther2',
          this.currentField,
          this.formData
        )
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.currentField.defaultValue
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create RadioButtonOther1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    } else if (this.formData == null) {
      console.log(
        'formedit-create RadioButtonOther2',
        this.currentField,
        this.formData
      )
      this.$set(this.currentField, this.currentField.inputname, {
        value: this.currentField.defaultValue.value,
        text: ''
      })
    }
  },
  methods: {
    onChange(val) {
      console.log(val)
      if (val) {
        this.$set(this.currentField, 'isShow', false)
      }
    }
  }
}
</script>
