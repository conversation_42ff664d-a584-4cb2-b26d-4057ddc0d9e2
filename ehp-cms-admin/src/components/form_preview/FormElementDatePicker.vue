<template>
  <div>
    <div v-if="isDisabled === 'see'" class="datetime isDisabled font30">
      {{
        currentField[currentField.inputname]
          ? currentField[currentField.inputname]
          : "请选择"
      }}
    </div>
    <div v-else class="datetime font30" @click="pickerOpen">
      {{
        currentField[currentField.inputname]
          ? currentField[currentField.inputname]
          : "请选择"
      }}
    </div>
    <van-popup v-model="datetimeVisable" position="bottom">
      <van-datetime-picker
        v-model="currentDate"
        type="date"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="pickerConfirm"
        @cancel="pickerCanel"
      />
    </van-popup>
    <!--mt-datetime-picker
      ref="picker"
      type="date"
      @confirm="handleConfirm"
      placeholder="Select date and time">
    </mt-datetime-picker-->
  </div>
</template>
<style scoped>
.datetime{border:1px solid #eee;border-radius:5px;padding: 20px;position: relative;}
.datetime-grey{position: absolute;right: 20px;}
.isDisabled{color: #969799;}
</style>

<script>
export default {
  name: 'DatePicker',
  props: ['currentField', 'isDisabled', 'formData'],
  data() {
    return {
      datetimeVisable: false,
      minDate: new Date(1900, 0, 1),
      maxDate: new Date(2050, 10, 1),
      currentDate: new Date()
    }
  },
  watch: {
    currentField() {
      if (this.formData && this.formData[this.currentField.inputname]) {
        console.log('watch-DatePicker1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create DatePicker1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  },
  methods: {
    openPicker() {
      this.$refs.picker.open()
    },
    pickerOpen() {
      this.datetimeVisable = true
    },
    pickerCanel() {
      this.datetimeVisable = false
    },
    pickerConfirm(val) {
      this.datetimeVisable = false
      console.log(val, this.Common.dateFormat('yyyy-MM-dd', val))
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.Common.dateFormat('yyyy-MM-dd', val)
      )
      if (val) {
        this.$set(this.currentField, 'isShow', false)
      } else {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
