<template>
  <div class="PhoneInput" style="display: flex;align-items: center;">
    <!-- <van-icon name="phone-o" /> -->
    <van-field
      v-model="currentField[currentField.inputname]"
      :disabled="isDisabled === 'see'"
      class="field"
      maxlength="11"
      type="number"
      :placeholder="
        currentField.isPlaceholderVisible ? currentField.placeholder : ''
      "
      @blur="blurText"
    />
  </div>
</template>
<style scoped>
.PhoneInput {
  display: flex;
  align-items: center;
	border: 1px solid #eee;
	border-radius: 5px;
	padding: 0 10px;
}
.PhoneInput /deep/ .field {
  border: none;
}
</style>
<script>
import { isvalidPhone } from '@/utils/validate'
export default {
  name: 'PhoneInput',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-PhoneInput', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create PhoneInput',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  },
  methods: {
    blurText(event) {
      console.log(event, event.target.value)
      if (event.target.value !== '' && isvalidPhone(event.target.value)) {
        this.$set(this.currentField, 'isShow2', false)
      } else if (event.target.value !== '') {
        this.$set(this.currentField, 'isShow2', true)
      } else if (event.target.value === '') {
        this.$set(this.currentField, 'isShow2', false)
      }
      if (event.target.value) {
        this.$set(this.currentField, 'isShow', false)
      } else {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
