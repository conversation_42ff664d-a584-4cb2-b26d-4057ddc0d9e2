<template>
  <div>
    <van-rate
      v-model="currentField[currentField.inputname]"
      :readonly="isDisabled === 'see'"
    />
  </div>
</template>

<script>
export default {
  name: 'Rating',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-Rating1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log('formedit-create Rating1', this.currentField, this.formData)
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  }
}
</script>
