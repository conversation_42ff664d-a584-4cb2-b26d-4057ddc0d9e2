<template>
  <div>
    <div
      v-if="isDisabled === 'see'"
      v-html="currentField[currentField.inputname]"
    />
    <tinyEditor v-if="isDisabled !== 'see'" ref="tinyEditor" v-model="content" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import { upload } from '@/utils/upload'
// import tinyEditor from '@/components/tinymce/tinymce'
// import E from 'wangeditor'
export default {
  name: 'TextEditor',
  components: {
    // tinyEditor
  },
  props: {
    currentField: {
      type: [String, Number],
      default: ''
    },
    isDisabled: {
      type: [String, Number, Boolean],
      default: ''
    },
    formData: {
      type: [Object],
      default: () => {}
    }
  },
  data() {
    return {
      date: '',
      content: ''
    }
  },
  computed: {
    ...mapGetters(['imagesUploadApi', 'baseApi'])
  },
  created() {
    console.log('formedit-create TextEditor', this.currentField, this.formData)
    if (this.formData && this.formData[this.currentField.inputname]) {
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  },
  mounted() {
    const self = this
    const { isDisabled, currentField } = self
    if (isDisabled !== 'see') {
      // var weditor = new E(this.$refs.weditor)
      // // 自定义菜单配置
      // weditor.config.zIndex = 10
      // // 文件上传
      // weditor.config.customUploadImg = function(files, insert) {
      //   // files 是 input 中选中的文件列表
      //   // insert 是获取图片 url 后，插入到编辑器的方法
      //   files.forEach(image => {
      //     // upload(_this.imagesUploadApi, image).then(res => {
      //     //   const data = res.data
      //     //   const url =
      //     //     _this.baseApi + '/file/' + data.type + '/' + data.realName
      //     //   insert(url)
      //     // })
      //   })
      // }
      // weditor.config.onchange = html => {
      //   this.currentField[this.currentField.inputname] = html
      // }
      // weditor.create()
      // 初始化数据
      if (isDisabled === 'edit') {
        this.content = currentField[currentField.inputname]
        // weditor.txt.html(this.currentField[this.currentField.inputname])
      }
    }
  }
}
</script>
