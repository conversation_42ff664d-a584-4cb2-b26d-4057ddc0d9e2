import Vue from 'vue'

import RadioButton from '@/components/form_preview/FormElementRadioButton'
import Checkbox from '@/components/form_preview/FormElementCheckbox'
import TextInput from '@/components/form_preview/FormElementTextInput'
import LongTextInput from '@/components/form_preview/FormElementLongTextInput'
import SectionBreak from '@/components/form_preview/FormElementSectionBreak'
import DatePicker from '@/components/form_preview/FormElementDatePicker'
import DatetimePicker from '@/components/form_preview/FormElementDatetimePicker'
import eImage from '@/components/form_preview/FormElementeImage'

import SelectList from '@/components/form_preview/FormElementSelectList'

import RadioButtonOther from '@/components/form_preview/FormElementRadioButtonOther'
import CheckboxOther from '@/components/form_preview/FormElementCheckboxOther'
import NameInput from '@/components/form_preview/FormElementNameInput'
import Rating from '@/components/form_preview/FormElementRating'
import EmailInput from '@/components/form_preview/FormElementEmailInput'
import PhoneInput from '@/components/form_preview/FormElementPhoneInput'
import Gender<PERSON>utton from '@/components/form_preview/FormElementGenderButton'
import TextEditor from '@/components/form_preview/FormElementTextEditor'

export const ViewBuilder = new Vue({
  components: {
    RadioButton,
    Checkbox,
    TextInput,
    LongTextInput,
    SectionBreak,
    DatePicker,
    DatetimePicker,
    eImage,
    SelectList,
    RadioButtonOther,
    CheckboxOther,
    NameInput,
    Rating,
    EmailInput,
    PhoneInput,
    GenderButton,
    TextEditor
  }
})
