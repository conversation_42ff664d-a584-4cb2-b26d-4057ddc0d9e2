<template>
  <div class="NameInput" style="display: flex;align-items: center;">
    <!-- <van-icon name="user-o" /> -->
    <van-field
      v-model="currentField[currentField.inputname]"
      :disabled="isDisabled === 'see'"
      class="field"
      :placeholder="
        currentField.isPlaceholderVisible ? currentField.placeholder : ''
      "
      @blur="blurText"
    />
  </div>
</template>
<style scoped>
.NameInput {
  display: flex;
  align-items: center;
  border: 1px solid #eee;
  padding: 0 10px;
}
.NameInput /deep/ .field {
  border: none;
}
</style>
<script>
export default {
  name: 'NameInput',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-NameInput1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create NameInput1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  },
  methods: {
    blurText(event) {
      console.log(event, event.target.value)
      if (event.target.value) {
        this.$set(this.currentField, 'isShow', false)
      } else {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
