<!--form表单设置预览使用-->
<template>
  <div>
    <div class="formmain">
      <div v-if="questionnaireExplain" v-html="questionnaireExplain" />
      <div class="tc pdt_10 pdb_10 font44">{{ formtitle }}</div>
      <!-- <div class="pdb_10 font34">{{ describe }}</div> -->
      <!-- The form elements starts (on the right) -->
      <div
        v-for="(form, index) in forms"
        :key="index"
        :ref="'form_'+index"
        v-bind="form"
        class="form__group"
      >

        <div class="pdb_20">
          <div v-show="form.hasOwnProperty('text')" class="font32 pdb_10"><span v-if="form.isRequired" style="color:#f00;">*</span>{{ form.text }}</div>
          <!-- <small v-show="form.helpBlockText" class="font28 pdt_10">{{ form.helpBlockText }}</small> -->

          <component
            :is="form.fieldType"
            :current-field="form"
            :is-disabled="isDisabled"
            :form-data="formData"
            class="form__field pdt_10 pdb_10"
          />
          <!-- <div v-if="form.isRequired && form.isShow" style="color:#f00;">请填写此项</div> -->
        </div>

      </div>
      <!--div><pre>{{ formDrawing }}</pre></div-->
      <!-- <button @click="submit">提交</button> -->
      <!-- <van-button type="primary" block>提交</van-button> -->
    </div>
  </div>
</template>
<style scoped src="./form.css">
</style>
<script>
// import { mapGetters } from 'vuex'
import { ViewBuilder } from '@/components/form_preview/formbuilder'

export default {
  name: 'Prevform',
  components: ViewBuilder.$options.components,
  // props: ['forms', 'formtitle', 'describe', 'isDisabled'],
  props: {
    forms: {
      type: Array,
      default: () => { return [] }
    },
    formData: {
      type: Object,
      default: () => { return {} }
    },
    formtitle: {
      type: String,
      default: ''
    },
    questionnaireExplain: {
      type: String,
      default: ''
    },
    // describe: {
    //   type: String,
    //   default: ''
    // },
    isDisabled: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  // computed: {
  //   ...mapGetters(['forms', 'activeForm'])
  // },
  created() {

  },
  methods: {

  }
}
</script>
