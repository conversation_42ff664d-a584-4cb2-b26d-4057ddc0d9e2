<template>
  <div style="display: flex;align-items: center;">
    <van-field
      v-model="currentField[currentField.inputname]"
      class="field"
      :disabled="isDisabled === 'see'"
      type="textarea"
      :placeholder="
        currentField.isPlaceholderVisible ? currentField.placeholder : ''
      "
    />
  </div>
</template>
<style scoped>
.field {
  padding: 5px;
}
.field input {
  padding: 5px;
}
</style>
<script>
export default {
  name: 'LongTextInput',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-LongTextInput1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create LongTextInput1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    }
  }
}
</script>
