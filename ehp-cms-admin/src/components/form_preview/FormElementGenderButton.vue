<template>
  <div>
    <van-radio-group
      v-model="currentField[currentField.inputname]"
      :disabled="isDisabled === 'see'"
      @change="onChange"
    >
      <van-radio
        v-for="(item, index) in currentField.options"
        :key="index"
        :name="item.optionValue"
      >{{ item.optionLabel }}</van-radio>
    </van-radio-group>
  </div>
</template>

<style scoped>
.van-radio__label {
  font-size: 30px;
}
</style>
<script>
export default {
  name: 'GenderButton',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-GenderButton1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      } else if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData == null
      ) {
        console.log('watch-GenderButton2', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.currentField.defaultValue
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create GenderButton1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    } else if (this.formData == null) {
      console.log(
        'formedit-create GenderButton2',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.currentField.defaultValue
      )
    }
  },
  methods: {
    onChange(val) {
      console.log(val)
    }
  }
}
</script>
