<template>
  <!-- <div>
    <van-uploader
      v-if="isDisabled === 'see'"
      v-model="uploadImgs"
      :deletable="false"
      disabled
      @preview="handlePreview"
    />
    <van-uploader
      v-else
      v-model="uploadImgs"
      :max-size="4194304"
      :max-count="currentField.imglength"
      @preview="handlePreview"
      @oversize="oversize"
    />
    <div>选择图片（最多{{ currentField.imglength }}张），单张图片最大4M</div>
  </div> -->
  <div class="uploader">
    <el-dialog
      :visible.sync="dialogVisible"
      append-to-body
    >
      <img
        width="100%"
        :src="dialogImageUrl"
        alt
      />
    </el-dialog>
    <div v-if="isDisabled === 'see'" class="uploader-content">
      <img
        v-for="(item,index) in uploadImgs"
        :key="index"
        class="uploader-img"
        :src="item.url"
        @click="handlePreview(item,index)"
      >
      <van-uploader
        :deletable="false"
        disabled
        multiple
        camera
      >
      </van-uploader>
    </div>
    <div v-else>
      <van-uploader
        multiple
        camera
        :max-size="4194304"
        :max-count="currentField.imglength"
        @oversize="oversize"
      >
      </van-uploader>
    </div>
    <div>选择图片（最多{{ currentField.imglength }}张），单张图片最大4M</div>

  </div>

</template>

<script>
// import API from "@/api/form";
export default {
  name: 'EImage',
  props: ['currentField', 'uploadFile', 'isDisabled', 'formData'],
  data() {
    return {
      uploadImgs: [],
      disMaxLen: 0,
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  watch: {
    currentField() {
      this.uploadImgs = []
      if (this.formData && this.formData[this.currentField.inputname]) {
        console.log('watch-eImage1', this.currentField, this.formData)
        this.disMaxLen = this.formData[this.currentField.inputname].length
        this.uploadImgs = []
        for (var i = 0; i < this.disMaxLen; i++) {
          this.uploadImgs.push({
            url: this.formData[this.currentField.inputname][i]
          })
        }
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      }
    }
  },
  created() {
    this.uploadImgs = []
    if (this.isDisabled && this.currentField[this.currentField.inputname]) {
      console.log('formedit-create eImage1', this.currentField, this.formData)
      this.disMaxLen = this.currentField[this.currentField.inputname].length
      this.uploadImgs = []
      for (var i = 0; i < this.disMaxLen; i++) {
        this.uploadImgs.push({
          url: this.currentField[this.currentField.inputname][i]
        })
      }
    } else {
      console.log('formedit-create eImage2', this.currentField, this.formData)
      this.$set(this.currentField, this.currentField.inputname, [])
    }
  },
  methods: {
    // 返回布尔值
    oversize(file) {
      this.$toast('文件大小超过限制')
      console.log(file)
    },
    afterRead(file) {
      // var _this = this
      // 此时可以自行将文件上传至服务器
      // console.log(
      //   'afterRead',
      //   this.uploadImgs,
      //   _this.currentField[_this.currentField.inputname],
      //   file
      // )
      // const fileData = new FormData()
      // fileData.append('file', file.file)
      // _this.uploadImgs[_this.uploadImgs.length - 1].status = 'uploading'
      // _this.uploadImgs[_this.uploadImgs.length - 1].message = '上传中...'
      // var uploadFileUrl = ''
      // if (this.uploadFile.uploadFileUrl) {
      //   var uploadFileUrl = this.uploadFile.uploadFileUrl
      // }
      // API.uploadImg(fileData, this.uploadFile.uploadFileUrl).then(
      //   res => {
      //     console.log('uploadImg', res)
      //     if (res.code == 0) {
      //       _this.uploadImgs[_this.uploadImgs.length - 1].content = res.data[0]
      //       _this.uploadImgs[_this.uploadImgs.length - 1].status = 'done'
      //       _this.uploadImgs[_this.uploadImgs.length - 1].message = '上传成功'
      //       _this.currentField[_this.currentField.inputname].push(res.data[0])
      //       console.log(
      //         _this.currentField[_this.currentField.inputname],
      //         _this.currentField
      //       )
      //       _this.$set(this.currentField, 'isShow', false)
      //     } else {
      //       _this.uploadImgs[_this.uploadImgs.length - 1].status = 'failed'
      //       _this.uploadImgs[_this.uploadImgs.length - 1].message = '上传失败'
      //     }
      //   },
      //   error => {
      //     _this.uploadImgs[_this.uploadImgs.length - 1].status = 'failed'
      //     _this.uploadImgs[_this.uploadImgs.length - 1].message = '上传失败'
      //   }
      // )
    },
    imgdelete(e, file) {
      this.currentField[this.currentField.inputname].splice(file.index, 1)
      this.uploadImgs.splice(file.index, 1)
    },
    handlePreview(item, index) {
      this.dialogImageUrl = item.url
      this.dialogVisible = true
    }
  }
}
</script>
<style>
.uploader-img{
	width: 80px;
	height: 80px;
	margin-right: 10px;
}
.uploader-content{
	display: flex;
}
</style>
