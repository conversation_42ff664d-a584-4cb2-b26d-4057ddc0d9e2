<template>
  <div>
    <van-checkbox-group
      v-model="currentField[currentField.inputname]"
      :disabled="isDisabled === 'see'"
      @change="onChange"
    >
      <van-checkbox
        v-for="(item, index) in currentField.options"
        :key="index"
        :name="item.optionValue"
      >{{ item.optionLabel }}</van-checkbox>
    </van-checkbox-group>
  </div>
</template>

<style scoped>
.van-checkbox__label {
  font-size: 30px;
}
</style>
<script>
export default {
  name: 'Checkbox',
  props: ['currentField', 'isDisabled', 'formData'],
  watch: {
    currentField() {
      if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData &&
        this.formData[this.currentField.inputname]
      ) {
        console.log('watch-Checkbox1', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.formData[this.currentField.inputname]
        )
      } else if (
        !this.currentField.hasOwnProperty('isShow') &&
        this.formData == null
      ) {
        console.log('watch-Checkbox2', this.currentField, this.formData)
        this.$set(
          this.currentField,
          this.currentField.inputname,
          this.currentField.defaultValue
        )
      }
    }
  },
  created() {
    if (this.formData && this.formData[this.currentField.inputname]) {
      console.log(
        'formedit-create Checkbox1',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.formData[this.currentField.inputname]
      )
    } else if (this.formData == null) {
      console.log(
        'formedit-create Checkbox2',
        this.currentField,
        this.formData
      )
      this.$set(
        this.currentField,
        this.currentField.inputname,
        this.currentField.defaultValue
      )
    }
  },
  methods: {
    onChange(val) {
      console.log(val, this.currentField)
      if (val.length > 0) {
        this.$set(this.currentField, 'isShow', false)
      } else if (this.currentField.isRequired && val.length === 0) {
        this.$set(this.currentField, 'isShow', true)
      }
    }
  }
}
</script>
