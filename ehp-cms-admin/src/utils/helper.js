
export function downloadIamge(imgSrc) {
  const xhr = new XMLHttpRequest()
  xhr.open('GET', imgSrc, true)
  xhr.responseType = 'blob'
  xhr.onload = function() {
    const url = window.URL.createObjectURL(xhr.response)
    const a = document.createElement('a')
    a.href = url
    const suffix = getSuffix(imgSrc)
    a.download = new Date().getTime() + '.' + suffix
    a.click()
  }
  xhr.send()
}

export function getSuffix(url) {
  const index = url.lastIndexOf('.')
  const suffix = url.substr(index + 1)
  return suffix || 'png'
}
export default {
  downloadIamge,
  getSuffix
}
