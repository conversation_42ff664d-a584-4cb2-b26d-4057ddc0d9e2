import JSEncrypt from 'jsencrypt/bin/jsencrypt'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey =
  'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAl9qVBvCFEzcOk6zRyduv\n' +
  '/AxqI9O0zfiyJle10BkYpi1M4j6l1xHJ1N2+H6NZxrC8Ex5hLN5WngACW0tfpYsB\n' +
  'je+WiDSmENHyjp2hEPN0oNm7Vui1YsukWFeMbYbDKfBwyYnpnXDrYTtRUuiS4y8k\n' +
  '2KgIOEnysSgOuqFykDAvif8qrpn3T2ETQcAR0iTq3oG4x0McguZ7GHGBVrfUGXug\n' +
  'yzQtzGYd2P9lLCwfXtnkZtOjJgBLVw9vl69//5ym2KspwSTSER4uI2GvL2bVhsln\n' +
  'wJuPkslvB7/rHJyYN9ZGNiQVIka9daldr+pqJSN5Mq41xgbYGyjS+7O4ry0jqqek\n' +
  'yQIDAQAB'

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对需要加密的数据进行加密
}
