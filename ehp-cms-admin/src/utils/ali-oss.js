// 引入ali-oss
// import {
//   getJsonLocal
// } from './common'

import axios from 'axios'
// import {
//   baseUrl
// } from '@/api/http.js'
import { getToken } from '@/utils/auth'
// const OSS = require('ali-oss')
export let client
export let progress // 上传进度

function getAliToken() {
  const $aliToken = axios.create({
    withCredentials: true,
    headers: {
      'Authorization': getToken(),
      'Content-Type': 'application/json'
    }
  })
  // 后端的接口，用来获取临时凭证
  $aliToken.get(process.env.VUE_APP_BASE_API + 'file/secret').then((res) => {
    if (res.status === 200) {
      /**
         *  [accessKeyId] {String}：通过阿里云控制台创建的 或者 通过后端接口获取 AccessKey。
         *  [accessKeySecret] {String}：通过阿里云控制台创建的或者 通过后端接口获取AccessSecret。
         *  [bucket] {String}：通过控制台或PutBucket创建或者 通过后端接口获取bucket。
         *  [region] {String}：bucket所在的区域， 默认oss-cn-hangzhou。
         */
      // client = new OSS({
      //   region: 'oss-cn-shenzhen',
      //   secure: true, // secure: 配合region使用，如果指定了secure为true，则使用HTTPS访问
      //   accessKeyId: res.data.AccessKeyId,
      //   accessKeySecret: res.data.AccessKeySecret,
      //   stsToken: res.data.SecurityToken,
      //   bucket: 'cline'
      // })
      client = new OSS({
        region: res.data.data.ossRegionId,
        secure: true, //* 这句话很重要！！！！！！！！！
        accessKeyId: res.data.data.accessKey,
        accessKeySecret: res.data.data.secretKey,
        bucket: res.data.data.bucketName
      })
    } else {
      this.$message.error('获取阿里云临时token失败')
    }
  })
}
getAliToken()

// export const client = new OSS({
//   region: 'oss-cn-beijing',
//   secure: true, //* 这句话很重要！！！！！！！！！
//   accessKeyId: 'LTAI4G7wTgp8r4u3rLyZenup',
//   accessKeySecret: '******************************',
//   bucket: 'university-file'
// })

/**
   *  上传文件，大小不能超过5GB
   * @param {string} ObjName OSS的储存路径和文件名字
   * @param {string} fileUrl 本地文件
   * @retruns Promise
   */
// ObjName为文件名字,可以只写名字，就直接储存在 bucket 的根路径，如需放在文件夹下面直接在文件名前面加上文件夹名称
export const put = async(ObjName, fileUrl) => {
  try {
    const result = await client.multipartUpload(`${ObjName}`, fileUrl, {
      progress: function(p) {
        progress = p * 100
      }
    })
    return result
  } catch (e) {
    console.log(e)
  }
}
// 上传成功之后，转换真实的地址
// 后台只需要传入文件名，回显时候同样也只是返回文件名，说做数据迁移管理时候不麻烦，反正意思就是上传出现问题都和他们无关，so，没办法，所以需要自己根据返回文件名进行转码，生成所需要的url

export const signatureUrl = async(ObjName) => {
  try {
    const result = await client.signatureUrl(`${ObjName}`)
    return result
  } catch (e) {
    console.log(e)
  }
}

export const getUrl = async(ObjName, file) => {
  try {
    const result = await client.get(`${ObjName}`, file)
    return result
  } catch (e) {
    console.log(e)
  }
}

// 随机UUID
export const getFileNameUUID = () => {
  function rx() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
  }
  return `${+new Date()}_${rx()}${rx()}`
}
