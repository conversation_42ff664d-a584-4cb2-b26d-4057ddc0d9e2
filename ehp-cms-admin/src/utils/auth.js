import Cookies from 'js-cookie'

const TokenName = 'tokenName'
const TokenKey = 'tokenValue'
import store from '@/store'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function removeTokenName() {
  return Cookies.remove(TokenName)
}

export function getTokenName() {
  return Cookies.get(TokenName)
}

export function setTokenName(tokenName) {
  return Cookies.set(TokenName, tokenName)
}
/**
 * @description 权限检测函数
 * @param permissionRoles {Array<string>} 规则，例子数据：['admin','editor']
 * @param defaultPermission {boolean} 默认权限
 * @return {boolean} 权限校验是否通过
 */
export function checkPermission(permissionRoles = [], defaultPermission = false) {
  let hasPermission = false
  // const roles = store.getters && store.getters.roles
  const userPermissions = store.getters && store.getters.permissions
  // 数据类型检测
  if (!Array.isArray(permissionRoles)) {
    console.error(`checkPermission 权限参数类型错误，例子：['admin','editor']`)
    return hasPermission
  }
  // 当没有权限时候，使用默认规则
  if (permissionRoles.length === 0) {
    return defaultPermission
  }
  hasPermission = userPermissions.some(role => {
    return permissionRoles.includes(role)
  })

  return hasPermission
}
