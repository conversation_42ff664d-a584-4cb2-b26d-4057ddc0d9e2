import { uploadImage } from '@/api/common'
import { client/*, put, signatureUrl, getFileNameUUID, progress*/ } from '@/utils/ali-oss'
export function upload(file) {
  var data = new FormData()
  data.append('file', file)
  return uploadImage(data)
}

export function uploadOss(file) {
  let obj = file.name
  const index = obj.lastIndexOf('.')
  obj = obj.substring(index, obj.length)
  // const objName = getFileNameUUID() + file.name
  const objName = file.name
  return client.multipartUpload(`${objName}`, file)
}

