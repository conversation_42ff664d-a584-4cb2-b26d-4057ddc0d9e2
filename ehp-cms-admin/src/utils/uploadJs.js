import { getAuth } from '@/api/uploadAuth'

export let myUploader

export const acceptVideo = [
  'avi',
  'mov',
  'mp4',
  'flv',
  'rmvb',
  'dat',
  'dvr',
  'vcd',
  'svcd',
  'vob',
  'dvd',
  'dvtr',
  'dvr',
  'bbc',
  'evd',
  'wmv',
  '3gp'
]

const mapAcceptVideo = (val, arr) => {
  const obj = {}
  arr.forEach((key) => {
    obj[key] = val
  })
  return obj
}
export const actions = {
  ...mapAcceptVideo('0', acceptVideo),
  'mp4': '0',
  'mov': '0',
  'pdf': 2,
  'docx': 3,
  'doc': 3,
  'wps': 3,
  'ppt': 4,
  'pptx': 4,
  'txt': 5,
  'xls': 6,
  'xlsx': 6,
  'png': 9,
  'jpeg': 9,
  'jpg': 9,
  'default': ''
}
export const types = {
  ...mapAcceptVideo('mp4', acceptVideo),
  'doc': 'docx',
  'xls': 'xlsx',
  'ppt': 'pptx'
}

let uploadAuth, uploadAddress, videoId

const init = () => {
  console.log('到这里了 init 中。。。。')
  if (!myUploader) {
    /* eslint no-undef: "off" */
    myUploader = new AliyunUpload.Vod({
      // 分片大小默认1M
      partSize: 1048576,
      // 并行上传分片个数，默认5
      parallel: 5,
      // 网络原因失败时，重新上传次数，默认为3
      retryCount: 3,
      // 网络原因失败时，重新上传间隔时间，默认为2秒
      retryDuration: 2,
      // 文件上传失败
      onUploadFailed: (uploadInfo, code, message) => console.log('上传失败', code),
      // 文件上传完成
      onUploadSucceed: (uploadInfo) => console.log('上传完成'),
      // STS临时账号会过期，过期时触发函数
      onUploadTokenExpired: () => {
        if (isVodMode()) {
          // 实现时，从新获取UploadAuth
          myUploader.resumeUploadWithAuth(uploadAuth)
        }
      },
      // 开始上传
      onUploadstarted: (uploadInfo) => {
        if (isVodMode()) {
          // 如果这个文件没有上传异常 如果videoId有值，根据videoId刷新上传凭证
          if (!uploadInfo.videoId) {
            myUploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress, videoId)
          } else {
            // 实际环境中调用点播的刷新上传凭证接口，获取凭证
            // https://help.aliyun.com/document_detail/55408.html?spm=a2c4g.11186623.6.630.BoYYcY
            // 获取上传凭证后，调用setUploadAuthAndAddress
            myUploader.setUploadAuthAndAddress(uploadInfo, uploadAuth, uploadAddress)
          }
        }
      }
    })
  }
}
init()

export const getUploadAuth = async(param, callback, mediaType) => {
  try {
    const allUploadInfo = await getAuth({
      type: mediaType || 0, // 0:其他 1：素材库
      fileName: param.file.name,
      title: param.file.name
    })
    uploadAuth = allUploadInfo.auth
    uploadAddress = allUploadInfo.address
    videoId = allUploadInfo.videoId
    if (isVodMode()) {
      const userData = '{"Vod":{}}'
      // 点播上传。每次上传都是独立的OSS object，所以添加文件时，不需要设置OSS的属性
      myUploader.addFile(param.file, null, null, null, userData)
    }
    start()
    callback && callback(videoId)
  } catch (err) {
    throw (new Error(err))
  }
}
function isVodMode() {
  return (uploadAuth && uploadAuth.length > 0)
}
function start() {
  console.log('开始上传了')
  myUploader.startUpload()
}
