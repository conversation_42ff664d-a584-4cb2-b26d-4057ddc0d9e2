<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        class="filter-item"
        placeholder="医生ID"
        style="width: 150px"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        class="filter-item"
        placeholder="医生姓名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        class="filter-item"
        placeholder="手机号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.status"
        placeholder="认证状态"
        class="filter-item"
        style="width: 120px"
        type="doctor_status"
        @change="handleFilter"
      />
      <DictSelect
        v-if="checkPer(['env.hainan'])"
        v-model="listQuery.recordPlatformStatus"
        placeholder="备案状态"
        class="filter-item"
        style="width: 120px"
        type="doctor_record_platform_status"
        @change="handleFilter"
      />
      <DictSelect
        v-else
        v-model="listQuery.recordStatus"
        placeholder="备案状态"
        class="filter-item"
        style="width: 120px"
        type="doctor_record_status"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        :use-default="false"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
    </div>

    <el-table
      :key="tableKey"
      ref="table"
      :data="list"
      fit
      highlight-current-row
      style="width:100%"
    >
      <el-table-column
        label="ID"
        prop="id"
        width="90px"
        align="center"
      />
      <el-table-column
        label="姓名"
        width="100px"
        align="center"
      >
        <template slot-scope="{row}">
          <DoctorDetails
            :ref="row.id"
            :doctor-id="row.id"
            :doctor-name="row.name"
            :handle-filter="getList"
          />
          <!-- <FilterTableColumn
            prop="name"
            type="name"
            :link="true"
            :row="row"
            :index="$index"
            :list="list"
            @openview="onOpenView"
          /> -->
        </template>
      </el-table-column>
      <el-table-column label="手机" prop="phone" width="150px" align="center" />
      <!-- <el-table-column
        label="手机"
        width="120px"
        align="center"
      >
        <template slot-scope="{row,$index}">
          <FilterTableColumn
            prop="phone"
            type="phone"
            :row="row"
            :list="list"
            :index="$index"
          />
        </template>
      </el-table-column> -->
      <el-table-column
        label="执业医院"
        prop="hospitalName"
        width="180px"
        align="center"
      />
      <el-table-column
        label="科室"
        prop="departmentName"
        width="130px"
        align="center"
      />

      <el-table-column
        label="认证状态"
        prop="statusDescribe"
        align="center"
        width="150px"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.status === 0"
            size="small"
            @click="openDoctor(row.id,1)"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status === 1"
            type="info"
            size="small"
            @click="openDoctor(row.id,1)"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status === 2"
            size="small"
            type="success"
            @click="openDoctor(row.id,1)"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status === 3"
            size="small"
            type="danger"
            @click="openDoctor(row.id,1)"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status === 4"
            size="small"
            @click="openDoctor(row.id,1)"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status === 5"
            size="small"
            @click="openDoctor(row.id,1)"
          >{{ row.statusDescribe }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="备案状态"
        prop="recordStatusDescribe"
        align="center"
        width="150px"
      >
        <template slot-scope="{row}">
          <div v-if="checkPer(['env.hainan'])">
            <el-tag
              v-if="!row.recordPlatformStatus"
              @click="openDoctor(row.id,1)"
            >{{ row.recordPlatformStatus === null ? '未备案' : row.recordPlatformStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordPlatformStatus === 1"
              type="info"
              @click="openDoctor(row.id,1)"
            >{{ row.recordPlatformStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordPlatformStatus === 2"
              type="success"
              @click="openDoctor(row.id,1)"
            >{{ row.recordPlatformStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordPlatformStatus === 3"
              type="danger"
              @click="openDoctor(row.id,1)"
            >{{ row.recordPlatformStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordPlatformStatus === 4"
              type="info"
              @click="openDoctor(row.id,1)"
            >{{ row.recordPlatformStatusDescribe }}</el-tag>
          </div>
          <div v-else>
            <el-tag
              v-if="row.recordStatus === 0"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordStatus === 1"
              type="info"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordStatus === 2"
              type="success"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordStatus === 3"
              type="danger"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordStatus === 4"
              type="info"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
          </div>

        </template>
      </el-table-column>
      <el-table-column
        label="注册时间"
        prop="createdAt"
        width="155px"
        align="center"
      />
      <el-table-column
        label="账号类型"
        prop="recordStatusDescribe"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.accountType === 0"
            type="success"
          >正式</el-tag>
          <el-tag v-else>测试</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        prop="createdAt"
        width="200px"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.accountStatus === 0"
            v-permission="['user:doctor:switch']"
            size="mini"
            type="danger"
            @click="doctorOperation(scope.row.id,1,scope.$index)"
          >停用</el-button>
          <el-button
            v-else
            v-permission="['user:doctor:switch']"
            type="primary"
            size="mini"
            @click="doctorOperation(scope.row.id,0,scope.$index)"
          >启用</el-button>
          <el-button
            v-if="scope.row.accountType === 0"
            v-permission="['user:doctor:test']"
            size="mini"
            type="danger"
            @click="doctorSetTest(scope.row.id,1,scope.$index)"
          >标记测试</el-button>
          <el-button
            v-else
            v-permission="['user:doctor:test']"
            type="primary"
            size="mini"
            @click="doctorSetTest(scope.row.id,0,scope.$index)"
          >取消测试</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      title="备案审核"
      :visible.sync="dialogRecordStatusInfoVisible"
      append-to-body
    >
      <el-form
        ref="userRecordStatusDataForm"
        :model="userRecordStatus"
        :rules="recordStatusRules"
        label-position="right"
        label-width="150px"
        style="width:90%"
      >
        <el-form-item
          label="审核原因"
          prop="failureReason"
        >
          <el-input
            v-model="userRecordStatus.failureReason"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogRecordStatusInfoVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleRecordStatusData()"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<style scoped>
.mgt {
  margin-top: 10px;
}
</style>
<script>
import {
  getList,
  doctorOperation,
  doctorSetTest,
  recordAudit
} from '@/api/user/doctor'
import waves from '@/directive/waves' // Waves directive
import DoctorDetails from './components/doctorDetails'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
// import FilterTableColumn from '@/components/FilterTableColumn/index'
export default {
  name: 'Userdoctor',
  directives: { waves },
  filters: {},
  components: {
    DoctorDetails,
    DictSelect,
    DatePicker
    // FilterTableColumn
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        id: '',
        name: '',
        phone: '',
        status: '',
        recordStatus: ''
      },
      textMap: {
        update: '更新',
        setting: '设置门店'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      userRecordStatus: {
        failureReason: ''
      },
      dialogRecordStatusInfoVisible: false,
      recordStatusRules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'blur' }
        ],
        failureReason: [
          {
            validator: (rule, value, callback) => {
              if (
                this.userRecordStatus.status === 3 &&
                this.userRecordStatus.failureReason === ''
              ) {
                callback(new Error('请填写审核原因'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  /* 监听table数据对象 */
  watch: {
    list(val) {
      this.doLayout()
    }
  },
  created() {
    // this.handleFilter()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
        this.$refs['tableColumn']
        console.log(this.$refs['tableColumn'], 398)
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.phone = ''
      this.listQuery.status = ''
      this.listQuery.recordStatus = ''
      this.listQuery.recordPlatformStatus = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    //停用启用弹出层
    doctorOperation(id, status, index) {
      console.log(status)
      if (status === 0) {
        this.$confirm('是否启用医生？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, 0, index)
        })
      } else {
        this.$confirm('是否停用医生？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, 1, index)
        })
      }
    },
    //弹出层确认事件
    sureStatus(id, status, index) {
      doctorOperation(id, status).then((response) => {
        this.getList()
      })
    },
    //停用启用弹出层
    doctorSetTest(id, status, index) {
      console.log(status)
      if (status === 0) {
        this.$confirm('是否标记为正式医生？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureSetTest(id, 0, index)
        })
      } else {
        this.$confirm('是否标记为测试医生？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureSetTest(id, 1, index)
        })
      }
    },
    //弹出层确认事件
    sureSetTest(id, status, index) {
      doctorSetTest(id, status).then((response) => {
        this.getList()
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    editDoctor(doctorId) {
      this.$router.push({
        path: './doctoredit/' + doctorId
      })
    },
    record(doctorId) {
      this.$router.push({
        path: './recordDr/' + doctorId
      })
    },
    openDoctor(id, index) {
      const that = this
      that.$refs[id].openTabs('tab_' + index)
    },
    handleRecordStatusSuccess(doctorId) {
      this.userRecordStatus = {
        failureReason: '',
        doctorId: doctorId,
        status: 2
      }
      recordAudit(this.userRecordStatus).then(() => {
        this.dialogRecordStatusInfoVisible = false
        this.getList()
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
      })
    },
    handleRecordStatus(status, doctorId) {
      this.dialogRecordStatusInfoVisible = true
      this.$nextTick(() => {
        this.$refs['userRecordStatusDataForm'].clearValidate()
        this.userRecordStatus = {
          failureReason: '',
          doctorId: doctorId,
          status: status
        }
      })
    },
    handleRecordStatusData() {
      this.$refs['userRecordStatusDataForm'].validate((valid) => {
        if (valid) {
          this.$confirm('此操作将审核用户状态, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            recordAudit(this.userRecordStatus).then(() => {
              this.dialogRecordStatusInfoVisible = false
              this.getList()
              this.$message({
                type: 'success',
                message: '操作成功!'
              })
            })
          })
        }
      })
    },
    // 监听子组件方法
    onOpenView(data) {
      this.openDoctor(data.id, 1)
    },
    /* 重新渲染table组件 */
    doLayout() {
      const that = this
      this.$nextTick(() => {
        that.$refs.table.doLayout()
      })
    }
  }
}
</script>
