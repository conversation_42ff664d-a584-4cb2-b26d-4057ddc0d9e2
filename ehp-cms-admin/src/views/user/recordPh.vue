<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :model="recordInfo"
      :rules="rules"
      :inline="true"
      label-position="left"
    >
      <el-row>
        <el-col :span="24">
          <el-form-item label="签约医疗机构编码">
            <span>{{ recordInfo.hospitalInfo.orgCode }}</span>
          </el-form-item>
          <el-form-item label="签约医疗机构名称">
            <span>{{ recordInfo.hospitalInfo.orgName }}</span>
          </el-form-item>
          <el-form-item label="机构登记号">
            <span>{{ recordInfo.hospitalInfo.orgRegNo }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="机构地址">
            <span>{{ recordInfo.hospitalInfo.orgAddress }}</span>
          </el-form-item>
          <el-form-item label="机构地址邮编">
            <span>{{ recordInfo.hospitalInfo.orgPostalCode }}</span>
          </el-form-item>
          <el-form-item label="单位电话">
            <span>{{ recordInfo.hospitalInfo.orgTel }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="机构内人员编码" prop="userId">
            <span>{{ recordInfo.userBaseInfo.userId }}</span>
          </el-form-item>
          <el-form-item label="人员类型" prop="userType">
            <span>{{ recordInfo.userBaseInfo.userTypeDescribe }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="姓名" prop="userBaseInfo.name">
            <el-input
              v-model="recordInfo.userBaseInfo.name"
              placeholder="填写后不可修改"
              :disabled="pharmacistId"
            />
          </el-form-item>
          <el-form-item label="身份证号" prop="userBaseInfo.idCardNum">
            <el-input
              v-model="recordInfo.userBaseInfo.idCardNum"
              clearable
              placeholder="填写后不可修改"
              :disabled="pharmacistId"
            />
          </el-form-item>
          <el-form-item label="性别">
            <el-input v-model="recordInfo.userBaseInfo.gender" disabled />
          </el-form-item>
          <el-form-item label="出生日期">
            <el-input v-model="recordInfo.userBaseInfo.birthday" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="民族" prop="userInfo.nationCode">
            <el-cascader
              v-model="recordInfo.userInfo.nationCode"
              :options="standardData"
              :props="standardProps"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="家庭地址" prop="userInfo.cityId">
            <el-cascader
              v-model="recordInfo.userInfo.cityId"
              :options="cityData"
              :props="cityProps"
              :show-all-levels="false"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="家庭地址邮编" prop="userInfo.zipCode">
            <el-input
              v-model="recordInfo.userInfo.zipCode"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="职称" prop="userBaseInfo.titleId">
            <DictSelect
              v-model="recordInfo.userBaseInfo.titleId"
              placeholder="请选择"
              type="pharmacist_title"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="联系手机号" prop="userBaseInfo.phone">
            <el-input
              v-model="recordInfo.userBaseInfo.phone"
              clearable
              placeholder="填写后不可修改"
              :disabled="pharmacistId"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="健康状况" prop="userInfo.health">
            <DictSelect
              v-model="recordInfo.userInfo.health"
              placeholder="请选择"
              type="health"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
            <!-- <el-input v-model="recordInfo.userInfo.health" clearable /> -->
          </el-form-item>
          <el-form-item label="学历" prop="userInfo.education">
            <DictSelect
              v-model="recordInfo.userInfo.education"
              placeholder="请选择"
              type="education"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-form-item
          label="业务水平考核机构或组织名称、考核培训时间及结果"
          prop="userInfo.appraisal"
        >
          <el-input
            v-model="recordInfo.userInfo.appraisal"
            type="textarea"
            :rows="5"
            style="width:600px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item
          label="何时何地因何种原因受过何种处罚或处分"
          prop="userInfo.punish"
        >
          <el-input
            v-model="recordInfo.userInfo.punish"
            type="textarea"
            :rows="5"
            style="width:600px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="其它要说明的问题" prop="userInfo.other">
          <el-input
            v-model="recordInfo.userInfo.other"
            type="textarea"
            :rows="5"
            style="width:600px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>
      <span>
        <i style="color: red;">*</i>个人工作经历列表
      </span>
      <el-button
        v-if="
          recordInfo.userBaseInfo.recordStatus != 1 &&
            recordInfo.userBaseInfo.recordStatus != 2
        "
        v-waves
        icon="el-icon-plus"
        type="primary"
        circle
        @click="handleCreate"
      />
      <el-divider />
      <el-table :data="recordInfo.workInfos" border style="width: 100%">
        <el-table-column prop="startDate" label="开始时间" width="180" align="center" />
        <el-table-column prop="endDate" label="结束时间" width="180" align="center" />
        <el-table-column prop="employer" label="单位名称" align="center" />
        <el-table-column prop="title" label="技术职务" align="center" />
        <el-table-column prop="certifier" label="证明人" width="180" align="center" />
        <el-table-column
          v-if="
            recordInfo.userBaseInfo.recordStatus != 1 &&
              recordInfo.userBaseInfo.recordStatus != 2
          "
          label="操作"
          prop="createdAt"
          width="200px"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleUpdate(scope.$index,scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.$index,recordInfo.workInfos)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <br />
      <el-row>
        <el-col :span="24">
          <el-form-item label="执业药师注册证号" prop="userBaseInfo.pracNum">
            <el-input
              v-model="recordInfo.userBaseInfo.pracNum"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
            <div style="font-size:12px;color:#999">注：若无执业药师注册证可填写资格证号</div>
          </el-form-item>
          <el-form-item label="执业药师注册证取得时间" prop="userInfo.pracIssue">
            <el-date-picker
              v-model="recordInfo.userInfo.pracIssue"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
            <div style="font-size:12px;color:#999">注：若无执业药师注册证可填写资格证取得时间</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="资格证号" prop="userBaseInfo.certNum">
            <el-input
              v-model="recordInfo.userBaseInfo.certNum"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
              clearable
            />
          </el-form-item>
          <el-form-item label="资格证取得时间" prop="userInfo.certIssue">
            <el-date-picker
              v-model="recordInfo.userInfo.certIssue"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="药师执业级别" prop="userInfo.pracLevel">
            <el-select v-model="recordInfo.userInfo.pracLevel" :disabled="recordInfo.userBaseInfo.recordStatus == 1 || recordInfo.userBaseInfo.recordStatus == 2" placeholder="请选择">
              <el-option
                label="执业药师"
                value="执业药师"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="药师执业类别" prop="userInfo.pracType">
            <!-- <el-select
              v-model="recordInfo.userInfo.pracType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in pracTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select> -->
            <el-input
              v-model="recordInfo.userInfo.pracType"
              placeholder="药师执业类别"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="药师执业范围" prop="userInfo.pracScopeApproval">
            <el-input
              v-model="recordInfo.userInfo.pracScopeApproval"
              placeholder="xx专业"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item
            label="执业有效期开始时间"
            prop="userInfo.pracStartDate"
          >
            <el-date-picker
              v-model="recordInfo.userInfo.pracStartDate"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="执业有效期结束时间" prop="userInfo.pracEndDate">
            <el-date-picker
              v-model="recordInfo.userInfo.pracEndDate"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="申请拟执业医疗机构意见时间"
            prop="userInfo.applyDate"
          >
            <el-date-picker
              v-model="recordInfo.userInfo.applyDate"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-form-item
          label="申请拟执业医疗机构意见"
          prop="userInfo.auditOpinion"
        >
          <el-input
            v-model="recordInfo.userInfo.auditOpinion"
            type="textarea"
            :rows="5"
            style="width:650px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="申请拟执业医疗机构-电子章" prop="electronic">
          <el-upload
            :action="uploadPath + '3'"
            :headers="headers"
            disabled
            :show-file-list="false"
            accept="image/png, image/gif, image/jpeg, image/jpg"
          >
            <img
              v-if="recordInfo.userBaseInfo.electronic"
              :src="recordInfo.userBaseInfo.electronic"
              class="uploadimg"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="药师身份证文件" prop="idCardUrls">
          <el-upload
            list-type="picture-card"
            :action="uploadPath +'4'"
            :headers="headers"
            :file-list="idCardUrls"
            :limit="2"
            :on-success="handleIdCardUrlsSuccess"
            :on-remove="handleRemoveIdCardUrls"
            :on-preview="handlePreview"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || idCardUrls.length ==2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" style="width: 148px; height: 148px" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'idCardUrls')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || idCardUrls.length ==2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="互联网医院聘任合同照片" prop="workContracts">
          <el-upload
            list-type="picture-card"
            :action="uploadPath + '8'"
            :headers="headers"
            :limit="5"
            :file-list="recordInfo.workContracts"
            :on-success="handleWorkContractsSuccess"
            :on-remove="handleRemoveWorkContracts"
            :on-preview="handlePreview"
            accept="image/png, image/jpg"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || recordInfo.workContracts.length ==5
            "
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" style="width: 148px; height: 148px" :src="file.url" fit="cover" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'workContracts')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || recordInfo.workContracts.length ==5
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb
            </div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="执业药师注册证" prop="pracUrls">
          <el-upload
            list-type="picture-card"
            :action="uploadPath + '3'"
            :headers="headers"
            :file-list="pracUrls"
            :limit="1"
            :on-exceed="handleExceed"
            :on-success="handlePracUrlsSuccess"
            :on-remove="handleRemovePracUrls"
            :on-preview="handlePreview"
            accept="image/png, image/jpg"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || pracUrls.length ==1
            "
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" style="width: 148px; height: 148px" :src="file.url" fit="cover" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'pracUrls')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || pracUrls.length ==1
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb，  <span>若无执业药师注册证可上传资格证文件</span>
            </div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="药师资格证文件" prop="certUrls">
          <el-upload
            list-type="picture-card"
            :action="uploadPath + '6'"
            :headers="headers"
            :file-list="certUrls"
            :limit="1"
            :on-exceed="handleExceed"
            :on-success="handleCertUrlsSuccess"
            :on-remove="handleRemoveCertUrls"
            :on-preview="handlePreview"
            accept="image/png, image/jpg"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || certUrls.length ==1
            "
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" style="width: 148px; height: 148px" :src="file.url" fit="cover" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'certUrls')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || certUrls.length ==1
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb
            </div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="药师认证照片" prop="userBaseInfo.headUrl">
          <el-upload
            :limit="1"
            list-type="picture-card"
            :action="uploadPath + '1'"
            :headers="headers"
            :on-success="handleHeadUrlSuccess"
            :on-preview="handlePreview"
            :on-exceed="handleExceed"
            :file-list="headUrl"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || headUrl.length ==1
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" style="width: 148px; height: 148px" :src="file.url" fit="cover" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'headUrl')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || headUrl.length ==1
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">
              只能上传jpg/png文件，且不超过500kb
            </div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="毕业证照片" prop="userInfo.graduateCertUrl">
          <el-upload
            :limit="1"
            list-type="picture-card"
            :action="uploadPath + '9'"
            :headers="headers"
            :on-success="handlegraduateCertUrlSuccess"
            :file-list="graduateCertUrl"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || graduateCertUrl.length ==1
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'graduateCertUrl')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || graduateCertUrl.length ==1
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="学位证照片" prop="userInfo.degreeCertUrl">
          <el-upload
            :limit="1"
            list-type="picture-card"
            :action="uploadPath + '10'"
            :headers="headers"
            :on-success="handledegreeCertUrlSuccess"
            :file-list="degreeCertUrl"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2 || degreeCertUrl.length ==1
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'degreeCertUrl')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2 || degreeCertUrl.length ==1
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
    </el-form>
    <div style="text-align:center;">
      <el-button
        v-if="
          recordInfo.userBaseInfo.recordStatus == null ||
            recordInfo.userBaseInfo.recordStatus === 0 ||
            recordInfo.userBaseInfo.recordStatus === 3 ||
            recordInfo.userBaseInfo.recordStatus === 4
        "
        type="primary"
        @click="save(0)"
      >保存</el-button>
      <el-button
        v-if="
          recordInfo.userBaseInfo.accountType == 0 &&
            (recordInfo.userBaseInfo.recordStatus == null ||
              recordInfo.userBaseInfo.recordStatus === 0 ||
              recordInfo.userBaseInfo.recordStatus === 3 ||
              recordInfo.userBaseInfo.recordStatus === 4)
        "
        type="primary"
        @click="save(1)"
      >保存并提交</el-button>
      <el-button
        v-if="
          recordInfo.userBaseInfo.recordStatus == null ||
            recordInfo.userBaseInfo.recordStatus === 0 ||
            recordInfo.userBaseInfo.recordStatus === 3 ||
            recordInfo.userBaseInfo.recordStatus === 4
        "
        type="primary"
        @click="goBack"
      >取消</el-button>
    </div>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      top="2vh"
      width="500px"
    >
      <el-form
        ref="workInfosDataForm"
        :model="workInfo"
        :rules="workInfoRules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="workInfo.startDate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="workInfo.endDate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="单位名称" prop="employer">
          <el-input v-model="workInfo.employer" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item label="技术职务" prop="title">
          <el-input v-model="workInfo.title" placeholder="请输入技术职务" />
        </el-form-item>
        <el-form-item label="证明人" prop="certifier">
          <el-input v-model="workInfo.certifier" placeholder="请输入证明人" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">取消</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus === 'create' ? createData() : updateData()"
        >确定</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>
<style scoped>
/* .el-form-item__label{
      font-size: 12px
  } */

.uploadimg {
  width: 148px;
  height: 148px;
}
.item {
  margin-bottom: 18px;
}

.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
</style>
<script>
import {
  getRecordInfo,
  getCityList,
  getStandardList,
  updateRecordInfo
} from '@/api/user/pharmacist'
import DictSelect from '@/components/DictSelect'
import waves from '@/directive/waves' // Waves directive
import { getToken, getTokenName } from '@/utils/auth'
import { customFormat } from '@/utils/date'
import { Loading } from 'element-ui'
export default {
  name: '',
  directives: { waves },
  components: {
    DictSelect
  },
  data() {
    return {
      pharmacistId: null,
      fileList: [],
      recordInfo: {
        hospitalInfo: {},
        userBaseInfo: {},
        userInfo: {},
        workInfos: [],
        workContracts: []
      },
      cityData: null,
      cityProps: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      standardData: null,
      standardProps: {
        value: 'code',
        emitPath: false,
        label: 'name'
      },
      workInfo: {},
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      idCardUrls: [],
      pracUrls: [],
      certUrls: [],
      agree: false,
      uploadPath: '',
      headers: {},
      rules: {
        'userBaseInfo.idCardNum': [
          {
            validator: (rule, value, callback) => {
              if (value === undefined) {
                callback(new Error('请输入身份证号'))
              } else {
                const idLength = value.length
                if (idLength === 15 || idLength === 18) {
                  callback()
                } else {
                  callback(new Error('身份证号码不正确'))
                }
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        'userBaseInfo.name': [
          { required: true, message: '请填写姓名', trigger: 'blur' }
        ],
        'userBaseInfo.titleId': [
          { required: true, message: '请选择职称', trigger: 'blur' }
        ],
        'userInfo.health': [
          { required: true, message: '请输入健康状况', trigger: 'blur' }
        ],
        'userInfo.nationCode': [
          { required: true, message: '请选择民族', trigger: 'blur' }
        ],
        'userInfo.cityId': [
          { required: true, message: '请选择家庭地址', trigger: 'blur' }
        ],
        'userInfo.education': [
          { required: true, message: '请选择学历', trigger: 'blur' }
        ],
        'userInfo.zipCode': [
          { required: true, message: '请输入邮编', trigger: 'blur' }
        ],
        'userInfo.certIssue': [
          { required: true, message: '请选择资格证取得时间', trigger: 'blur' }
        ],
        'userBaseInfo.certNum': [
          { required: true, message: '请输入资格证号', trigger: 'blur' }
        ],
        'userBaseInfo.pracNum': [
          { required: true, message: '请输入注册证号', trigger: 'blur' }
        ],
        'userInfo.pracLevel': [
          { required: true, message: '请选择药师执业级别', trigger: 'blur' }
        ],
        'userInfo.pracScopeApproval': [
          { required: true, message: '请选择药师执业范围', trigger: 'blur' }
        ],
        'userInfo.pracType': [
          { required: true, message: '请选择药师执业类别', trigger: 'blur' }
        ],
        'userInfo.pracIssue': [
          { required: true, message: '请选择执业证取得时间', trigger: 'blur' }
        ],
        'userBaseInfo.phone': [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ],
        'userInfo.appraisal': [
          {
            required: true,
            message: '请输入业务水平考核机构或组织名称、考核培训时间及结果',
            trigger: 'blur'
          }
        ],
        'userInfo.auditOpinion': [
          {
            required: true,
            message: '请输入申请拟执业医疗机构意见',
            trigger: 'blur'
          }
        ],
        'userInfo.pracEndDate': [
          {
            required: true,
            message: '请选择执业有效期结束时间',
            trigger: 'blur'
          }
        ],
        'userInfo.pracStartDate': [
          {
            required: true,
            message: '请选择执业有效期开始时间',
            trigger: 'blur'
          }
        ],
        'userInfo.applyDate': [
          {
            required: true,
            message: '请选择申请拟执业医疗机构意见时间',
            trigger: 'blur'
          }
        ],
        workContracts: [
          {
            validator: (rule, value, callback) => {
              value = this.recordInfo.workContracts
              callback()
              // if (value != null && value.length > 0) {
              //   callback()
              // } else {
              //   callback('请上传互联网医院聘任合同照片')
              // }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        idCardUrls: [
          {
            validator: (rule, value, callback) => {
              value = this.idCardUrls
              if (value != null && value.length === 2) {
                callback()
              } else {
                callback('请上传医师身份证文件正反面')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        pracUrls: [
          {
            validator: (rule, value, callback) => {
              value = this.pracUrls
              if (value != null && value.length > 0) {
                callback()
              } else {
                callback('请上传药师执业证文件')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        certUrls: [
          {
            validator: (rule, value, callback) => {
              value = this.certUrls
              if (value != null && value.length > 0) {
                callback()
              } else {
                callback('请上传药师资格证文件')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        'userInfo.punish': [
          {
            required: true,
            message: '请输入何时何地因何种原因受过何种处罚或处分',
            trigger: 'blur'
          }
        ],
        'userInfo.other': [
          {
            required: true,
            message: '请输入其它要说明的问题',
            trigger: 'blur'
          }
        ],
        'userBaseInfo.headUrl': [
          {
            validator: (rule, value, callback) => {
              if (value != null) {
                callback()
              } else {
                callback('请上传医师认证照片')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        'userInfo.graduateCertUrl': [
          {
            required: true,
            message: '请上传毕业证照片',
            trigger: 'blur'
          }
        ]
      },
      workInfoRules: {
        startDate: [
          { required: true, message: '请选择开始时间', trigger: 'blur' }
        ],
        endDate: [
          { required: true, message: '请选择结束时间', trigger: 'blur' }
        ],
        employer: [
          { required: true, message: '请输入单位名称', trigger: 'blur' }
        ],
        title: [{ required: true, message: '请输入技术职务', trigger: 'blur' }],
        certifier: [
          { required: true, message: '请输入证明人', trigger: 'blur' }
        ]
      },
      index: 0,
      pracTypeOptions: [],
      headUrl: [],
      dialogImageUrl: '',
      dialogVisible: false,
      graduateCertUrl: [],
      degreeCertUrl: []
    }
  },
  watch: {
    'recordInfo.userBaseInfo.idCardNum': function(newVal, oldVal) {
      const iden = this.recordInfo.userBaseInfo.idCardNum
      const idLength = iden.length
      if (idLength === 15 || idLength === 18) {
        let sex = null
        let birth = null
        if (idLength === 18) {
          sex = iden.substring(16, 17)
          birth =
            iden.substring(6, 10) +
            '-' +
            iden.substring(10, 12) +
            '-' +
            iden.substring(12, 14)
        }
        if (idLength === 15) {
          sex = iden.substring(13, 14)
          birth =
            '19' +
            iden.substring(6, 8) +
            '-' +
            iden.substring(8, 10) +
            '-' +
            iden.substring(10, 12)
        }

        if (sex % 2 === 0) {
          sex = '女'
        } else {
          sex = '男'
        }
        this.recordInfo.userBaseInfo.gender = sex
        this.recordInfo.userBaseInfo.birthday = birth
      } else {
        this.recordInfo.userBaseInfo.gender = ''
        this.recordInfo.userBaseInfo.birthday = ''
      }
    }
  },
  created() {
    if (this.$route.params.pharmacistId) {
      this.pharmacistId = this.$route.params.pharmacistId
    }
    this.uploadPath = process.env.VUE_APP_BASE_API + '/user/pharmacis/upload/'
    this.getRecordInfo()
    this.headers[getTokenName()] = getToken()
  },
  methods: {
    // 获取数据
    getRecordInfo() {
      getRecordInfo(this.pharmacistId).then(response => {
        if (response.hospitalInfo) {
          this.recordInfo.hospitalInfo = response.hospitalInfo
        }
        if (response.userBaseInfo) {
          this.recordInfo.userBaseInfo = response.userBaseInfo
        }
        if (response.userInfo) {
          this.recordInfo.userInfo = response.userInfo
        }
        if (response.workInfos) {
          this.recordInfo.workInfos = response.workInfos
        }
        if (response.workContracts) {
          this.recordInfo.workContracts = response.workContracts
        }
        for (var i in response.userBaseInfo.idCardUrls) {
          this.idCardUrls.push({ url: response.userBaseInfo.idCardUrls[i] })
        }
        for (var p in response.userBaseInfo.pracUrls) {
          this.pracUrls.push({ url: response.userBaseInfo.pracUrls[p] })
        }
        for (var c in response.userBaseInfo.certUrls) {
          this.certUrls.push({ url: response.userBaseInfo.certUrls[c] })
        }
        if (response.userBaseInfo.headUrl) {
          this.headUrl.push({ url: response.userBaseInfo.headUrl })
        }
        if (response.userInfo.graduateCertUrl) {
          this.graduateCertUrl.push({ url: response.userInfo.graduateCertUrl })
        }
        if (response.userInfo.degreeCertUrl) {
          this.degreeCertUrl.push({ url: response.userInfo.degreeCertUrl })
        }
        this.recordInfo.userInfo.pracLevel = this.recordInfo.userInfo.pracLevel ? this.recordInfo.userInfo.pracLevel : '执业药师'
        this.recordInfo.userInfo.pracType = this.recordInfo.userInfo.pracType ? this.recordInfo.userInfo.pracType : '无'
        this.recordInfo.userInfo.pracScopeApproval = this.recordInfo.userInfo.pracScopeApproval ? this.recordInfo.userInfo.pracScopeApproval : '无'
        this.recordInfo.userInfo.applyDate = this.recordInfo.userInfo.applyDate ? this.recordInfo.userInfo.applyDate : customFormat(new Date().getTime(), 'yyyy-MM-dd')
      })
      getCityList().then(response => {
        this.cityData = response
      })
      getStandardList().then(response => {
        this.standardData = response
      })
    },
    save(action) {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (this.recordInfo.workInfos.length == 0) {
            this.$message.error('请完善个人工作经历')
            return false
          }
          this.recordInfo.action = action
          const loadingInstance = Loading.service({ fullscreen: true })
          updateRecordInfo(this.recordInfo).then(() => {
            this.dialogFormVisible = false
            this.$router.push({
              path: '/user/pharmacist'
            })
          })
            .finally(() => {
              loadingInstance.close()
            })
        } else {
          this.$message.error('请完药师信息')
        }
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.workInfo = {}
        this.$refs['workInfosDataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['workInfosDataForm'].validate(valid => {
        if (valid) {
          this.recordInfo.workInfos.push(Object.assign({}, this.workInfo))
          this.dialogFormVisible = false
        }
      })
    },
    handleUpdate(index, row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.index = index
      this.$nextTick(() => {
        this.workInfo = Object.assign({}, row)
        this.$refs['workInfosDataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['workInfosDataForm'].validate(valid => {
        if (valid) {
          this.recordInfo.workInfos.splice(
            this.index,
            1,
            Object.assign({}, this.workInfo)
          )
          this.dialogFormVisible = false
        }
      })
    },
    handleDelete(index, rows) {
      this.$confirm('此操作将删除该工作经历, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rows.splice(index, 1)
      })
    },

    handleRemoveCertUrls(file, fileList) {
      this.recordInfo.userBaseInfo.certUrls = []
      for (var i in fileList) {
        this.recordInfo.userBaseInfo.certUrls.push(fileList[i].url)
      }
      this.certUrls = fileList
    },
    handleCertUrlsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.certUrls.push({ url: response.data })
        this.recordInfo.userBaseInfo.certUrls = []
        for (var i in this.certUrls) {
          this.recordInfo.userBaseInfo.certUrls.push(this.certUrls[i].url)
        }
      }
    },
    handleHeadUrlSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.headUrl.push({ url: response.data })
        this.recordInfo.userBaseInfo.headUrl = response.data // URL.createObjectURL(file.raw)
        console.log(this.headUrl, this.recordInfo.userBaseInfo.headUrl)
      }
    },
    handleRemoveWorkContracts(file, fileList) {
      this.recordInfo.workContracts = fileList
      this.workContracts = fileList
    },
    handleWorkContractsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.recordInfo.workContracts.push({ url: response.data })
      }
    },
    handleIdCardUrlsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.idCardUrls.push({ url: response.data })
        this.recordInfo.userBaseInfo.idCardUrls = []
        for (var i in this.idCardUrls) {
          this.recordInfo.userBaseInfo.idCardUrls.push(this.idCardUrls[i].url)
        }
      }
    },
    handleRemoveIdCardUrls(file, fileList) {
      this.recordInfo.userBaseInfo.idCardUrls = []
      for (var i in fileList) {
        this.recordInfo.userBaseInfo.idCardUrls.push(fileList[i].url)
      }
      this.idCardUrls = fileList
    },
    handleRemovePracUrls(file, fileList) {
      this.recordInfo.userBaseInfo.pracUrls = []
      for (var i in fileList) {
        this.recordInfo.userBaseInfo.pracUrls.push(fileList[i].url)
      }
      this.pracUrls = fileList
    },
    handlePracUrlsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.pracUrls.push({ url: response.data })
        this.recordInfo.userBaseInfo.pracUrls = []
        for (var i in this.pracUrls) {
          this.recordInfo.userBaseInfo.pracUrls.push(this.pracUrls[i].url)
        }
      }
    },
    handlegraduateCertUrlSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.graduateCertUrl.push({ url: response.data })
        this.recordInfo.userInfo.graduateCertUrl = response.data // URL.createObjectURL(file.raw)
      }
    },
    handledegreeCertUrlSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.degreeCertUrl.push({ url: response.data })
        this.recordInfo.userInfo.degreeCertUrl = response.data // URL.createObjectURL(file.raw)
      }
    },
    handleExceed(files, fileList) {
      this.$message.error('文件数量超出上传限制！')
    },
    handlePreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    goBack() {
      this.$router.push({
        path: '/user/pharmacist'
      })
    },
    handleRemoveUrls(file, name) {
      this.$confirm('此操作将删除信息照片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (name === 'graduateCertUrl' || name === 'degreeCertUrl') {
          this[name].splice(0, 1)
          this.recordInfo.userInfo[name] = ''
        }
        if (name === 'workContracts') {
          const index = this.recordInfo.workContracts.findIndex(
            i => i.url === file.url
          )
          this.recordInfo.workContracts.splice(index, 1)
          console.log(this.workContracts, this.recordInfo.workContracts)
        } else {
          const index = this[name].findIndex(i => i.url === file.url)
          this[name].splice(index, 1)
          if (name == 'headUrl') {
            this.recordInfo.userBaseInfo.headUrl = ''
          }
        }
      })
    }
  }
}
</script>
