<template>
  <div>
    <el-dialog title="患者详情" :visible.sync="dialogInfoVisible" width="80%" top="2vh">
      <el-tabs style="min-height: 300px;" type="card">
        <el-tab-pane label="基本信息">
          <el-form ref="dataForm" :model="patient" label-position="right" label-width="100px">
            <!-- 所属业务员 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属业务员:" prop="salesmanName">
                  <div style="display: flex;">{{ patient.baseInfo.salesmanName || '-' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="ID" prop="id">
                  <el-input v-model="patient.baseInfo.id" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="patient.baseInfo.name" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="头像" prop="headUrl">
                  <el-image :src="headUrl">
                    <div slot="error" class="image-slot">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </el-image>
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="昵称" prop="nickName">
                  <el-input v-model="patient.baseInfo.nickName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="城市" prop="cityId">
                  <el-input v-model="patient.baseInfo.cityName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="手机" prop="type">
                  <el-input v-model="patient.baseInfo.phone" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-input v-model="patient.baseInfo.genderDescribe" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <!--  <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="详细地址">
                  <el-input v-model="patient.baseInfo.address" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
           <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="紧急联系人姓名">
                  <el-input v-model="patient.baseInfo.contactName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="紧急联系人电话">
                  <el-input v-model="patient.baseInfo.contactPhone" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上级医生ID" prop="recomDoctor">
                  <el-input v-model="patient.baseInfo.recomDoctor" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上级医生姓名" prop="recomDoctorName">
                  <el-input v-model="patient.baseInfo.recomDoctorName" readonly />
                </el-form-item>
              </el-col>
              <el-col :span="12"></el-col>
            </el-row> -->
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="关注医生">
          <el-table :data="patient.focusOnDoctors" fit style="width: 100%">
            <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
            <el-table-column label="姓名" prop="name" width="100px" align="center" />
            <el-table-column label="头像" prop="headUrl" width="80px" align="center">
              <template slot-scope="{row}">
                <el-image :src="row.headUrl">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </template>
            </el-table-column>
            <el-table-column label="类型" prop="typeDescribe" width="80px" align="center" />
            <el-table-column label="手机" prop="phone" width="110px" align="center" />
            <el-table-column label="执业医院" prop="hospitalName" align="center" />
            <el-table-column label="科室" prop="departmentName" width="130px" align="center" />
            <el-table-column label="医院所在地" prop="hospitalCityName" width="105px" align="center" />
            <el-table-column label="账户状态" prop="accountStatusDescribe" width="80px" align="center" />
            <el-table-column label="认证状态" prop="statusDescribe" align="center" width="110px" />
            <el-table-column label="备案状态" prop="recordStatusDescribe" align="center" width="100px" />
            <el-table-column label="注册时间" prop="createdAt" width="140px" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="关注咨询师">
          <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="id" label="ID" align="center"></el-table-column>
            <el-table-column prop="workNumber" label="工号" align="center"></el-table-column>
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="positionName" label="岗位" align="center"></el-table-column>
            <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
            <el-table-column prop="bindTime" label="关注时间" align="center"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="历史订单">
          <el-table :data="patient.orderList" highlight-current-row fit style="width: 100%">
            <el-table-column label="订单号" prop="orderSn" width="180px" align="center" />
            <el-table-column label="商品总价" prop="totalAmount" width="80px" align="center" />
            <el-table-column label="邮费" prop="freight" width="80px" align="center" />
            <el-table-column label="优惠券" prop="couponPay" width="80px" align="center" />
            <el-table-column label="实付" prop="realPay" width="80px" align="center" />
            <el-table-column label="订单状态" prop="orderStatusDescribe" width="80px" align="center" />
            <el-table-column label="支付状态" prop="payStatusDescribe" width="80px" align="center" />
            <el-table-column label="订单时间" prop="createdAt" width="140px" align="center" />
            <el-table-column label="支付时间" prop="payTime" width="140px" align="center" />
            <el-table-column label="发货时间" prop="sendTime" width="140px" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="就诊人成员">
          <el-table :data="inquirerData" highlight-current-row fit style="width: 100%">
            <el-table-column label="姓名" width="120px" prop="name" align="center" />
            <el-table-column label="证件号码" width="160px" prop="idCard" align="center" />
            <el-table-column label="与患者关系" prop="relationName" align="center" />
            <el-table-column label="性别" prop="gender" align="center">
              <template slot-scope="{row}">
                {{ genderStr[row.gender] }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="图片" prop="gender" align="center">
              <template slot-scope="{row}">
                <span v-if="row.birthImage" style="color:#1890ff;" @click="showImage(row.birthImage)">查看</span>
              </template>
            </el-table-column> -->
            <el-table-column label="年龄" width="100px" prop="age" align="center">
              <template slot-scope="{row}">
                {{ !row.ageYear ? '': row.age }}
              </template>
            </el-table-column>
            <el-table-column label="现居住地址" prop="address" width="180px" align="center" />
            <el-table-column label="紧急联系人" width="120px" prop="contactName" align="center" />
            <el-table-column label="紧急联系人手机号" width="120px" prop="contactPhone" align="center" />
            <el-table-column label="婚姻状况" width="80px" align="center">
              <template slot-scope="{row}">
                <el-tag v-if="row.maritalStatus === 1">已婚</el-tag>
                <el-tag v-else-if="row.maritalStatus === 0">未婚</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="监护人姓名" width="120px" prop="guardianName" align="center" />
            <el-table-column label="监护人身份证" width="160px" prop="guardianIdCard" align="center" />
            <el-table-column label="监护人手机号" width="120px" prop="guardianPhone" align="center" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="咨询记录">
          <el-table
            :data="patient.consultSessionList"
            highlight-current-row
            fit
            style="width: 100%"
          >
            <el-table-column align="center" label="医生ID" prop="doctorId" />
            <el-table-column align="center" label="医生名称" prop="doctorName" />
            <el-table-column align="center" label="就诊人姓名" prop="inquirerName" />
            <el-table-column align="center" label="与陪诊人关系" width="100px" prop="inquirerRelationName" />
            <el-table-column align="center" label="会话ID" prop="sessionId" />
            <el-table-column align="center" label="开始时间" width="155px" prop="startTime" />
            <el-table-column align="center" label="结束时间" width="155px" prop="endTime" />
            <el-table-column align="center" label="金额" width="80px" prop="cost" />
            <el-table-column align="center" label="会话关系" prop="relationDescribe" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="病历档案">
          <div><el-select v-model="listQuery.inquirerId" clearable placeholder="请选择" @change="handleFilter">
            <el-option
              v-for="item in inquirerData"
              :key="item.inquirerId"
              :label="item.name"
              :value="item.inquirerId"
            >
            </el-option>
          </el-select></div>
          <el-table :data="caseList" highlight-current-row fit style="width: 100%">
            <el-table-column align="center" label="医生姓名" width="100px" prop="doctorName" />
            <el-table-column align="center" label="就诊医院" prop="hospitalName" />
            <el-table-column align="center" label="就诊科室" width="100px" prop="departmentName" />
            <el-table-column align="center" label="时间" width="155px" prop="finishTimeDate" />
            <el-table-column label="操作" prop="createdAt" width="300px" align="center">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="viewRecords(scope.row.id,1,scope.$index)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="getCaseList"
          />
        </el-tab-pane>
        <el-tab-pane label="诊前问卷">
          <el-table :data="formList" highlight-current-row fit style="width: 100%">
            <el-table-column align="center" label="问卷名称" width="" prop="formName" />
            <el-table-column align="center" label="发起时间" width="200px" prop="createdAt" />
            <el-table-column align="center" label="提交时间" width="200px" prop="submitAt" />
            <el-table-column align="center" label="咨询师姓名" width="200px" prop="counselorName" />
            <el-table-column label="操作" prop="createdAt" width="300px" align="center">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  @click="handleFollow(scope.row)"
                >查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total>0"
            :total="total"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="getFormListFun"
          />
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-link
      v-waves
      type="primary"
      :underline="false"
      @click="handlePatientDetailsInfo"
    >{{ patientName || patientNickName }}</el-link>
    <el-dialog title="图片" :visible.sync="dialogVisible">
      <div class="dialog-content">
        <el-image :src="imgUrl" fit="fit"></el-image>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog title="操作日志" :visible.sync="pdfDialogVisible" class="pdf-dialog">
      <div class="dialog-content">
        <iframe ref="pdfIframe" :src="pdfsrc" width="100%" height="800px" style="border:0;"></iframe>
        <!-- <pdf class="pdfbox" :src="pdfsrc"></pdf> -->
        <h2>操作记录</h2>
        <el-table :data="caseLogList" highlight-current-row fit style="width: 100%">
          <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
          <el-table-column align="center" label="用户操作">
            <template slot-scope="{row}">
              {{ types[row.type] }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作人" width="180px" prop="operator" />
          <el-table-column align="center" label="操作时间" width="155px" prop="createdAt" />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pdfDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      id="iframeDialog"
      :close-on-click-modal="false"
      title="预览"
      :visible.sync="dialogPreForm"
      width="400"
      top="5px"
    >
      <div id="completeDiv">
        <prevform
          :forms="forms"
          :is-disabled="isDisabled"
          :formtitle="formtitle"
          :describe="describe"
          :form-data="formsData"
        />
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.el-dialog {
  text-align: left;
}
.pdf-dialog .dialog-content  {
  height:100%;
}
/* .el-form-item__content .el-input__inner {
  width: 250px;
}
.el-form-item__content .el-textarea__inner {
  width: 250px;
}
.el-form-item__content .el-image {
  width: 250px;
  height: 250px;
} */
</style>
<script>
import {
  get,
  getCaseList,
  getInquirerList,
  getCaseLogList,
  setCaseLogList,
  getFormList,
  getFollowDetail,
  getCounselorsDetail
} from '@/api/user/patient'
import waves from '@/directive/waves' // Waves directive
import Prevform from '@/components/form_preview/prevform'
export default {
  name: 'PatientDetails',
  directives: { waves },
  components: {
    Prevform
  },
  props: {
    patientId: {
      type: [String, Number],
      required: false,
      default: ''
    },
    patientName: {
      type: String,
      required: false,
      default: ''
    },
    patientNickName: {
      type: String,
      required: false,
      default: ''
    }
  },
  data() {
    return {
      dialogInfoVisible: false,
      dialogPreForm: false,
      tableData: [],
      patient: {
        baseInfo: {},
        focusOnDoctors: [],
        orderList: []
      },
      headUrl: '',
      caseList: [],
      total: 0,
      inquirerData: [],
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      genderStr: ['女', '男', '未知'],
      dialogVisible: false,
      imgUrl: '',
      pdfsrc: '',
      pdfDialogVisible: false,
      caseLogList: [],
      caseLogTotal: 0,
      caseLogListQuery: {
        pageNo: 1,
        pageSize: 10
      },
      types: ['', '下载', '打印'],
      recomId: 0,
      formList: [],
      isDisabled: 'see',
      forms: [],
      isTitle: null,
      formtitle: '',
      describe: '',
      formsData: {}
    }
  },
  methods: {
    handleFollow({ counselorFollowId }) {
      getFollowDetail(counselorFollowId).then((res) => {
        this.formtitle = res.formName
        this.forms = res.designForm.formDrawing
        this.formsData = res.designForm.formData[res.designForm.lists[0].dataId]
        this.dialogPreForm = true
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.patient = {
          baseInfo: {},
          focusOnDoctors: [],
          consultList: [],
          orderList: []
        }
        this.tableData = []
        this.headUrl = ''
      })
    },
    handlePatientDetailsInfo() {
      this.resetTemp()
      this.dialogInfoVisible = true
      get(this.patientId).then(response => {
        this.patient = response
        this.headUrl = response.baseInfo.headUrl
      })
      this.getCaseList()
      this.getInquirerLists()
      this.getFormListFun()
      this.getCounselorsList()
    },
    getCounselorsList() {
      getCounselorsDetail(this.patientId).then((res) => {
        this.tableData = res
      })
    },
    getFormListFun() {
      this.listQuery.patientId = this.patientId
      getFormList(this.listQuery).then((res) => {
        console.log(res)
        this.formList = res.list
        this.total = res.totalCount
      })
    },
    getCaseList() {
      this.listQuery.patientId = this.patientId
      getCaseList(this.listQuery).then(response => {
        this.caseList = response.result
        this.total = response.totalCount
      })
    },
    getInquirerLists() {
      // this.listQuery.patientId = this.patientId
      getInquirerList({ patientId: this.patientId }).then(response => {
        console.log(response)
        this.inquirerData = response
      })
    },
    handleFilter() {
      console.log('handleFilter')
      this.getCaseList()
    },
    showImage(url) {
      this.imgUrl = url
      this.dialogVisible = true
    },
    getCaseLogList() {
      getCaseLogList(this.recomId, this.caseLogListQuery).then(response => {
        if (response.pdfUrl === '') {
          this.$message({
            message: '没有病历档案！',
            type: 'error'
          })
          return
        }
        this.caseLogList = response.operatorList.list
        this.caseLogTotal = response.totalCount
        this.pdfDialogVisible = true
        //response.pdfUrl
        this.pdfsrc = '../../plugin/pdf/web/viewer.html?file=' + encodeURIComponent(response.pdfUrl)
        this.$nextTick(() => {
          const iframe = this.$refs.pdfIframe
          iframe.onload = () => {
            const secondaryPrint = iframe.contentWindow.document.querySelector('#secondaryPrint')
            secondaryPrint.addEventListener('click', () => {
              console.log('===secondaryPrint===')
              this.setCaseLogList(2)
            })
            const secondaryDownload = iframe.contentWindow.document.querySelector('#secondaryDownload')
            secondaryDownload.addEventListener('click', () => {
              console.log('===secondaryDownload===')
              this.setCaseLogList(1)
            })
            const print = iframe.contentWindow.document.querySelector('#print')
            print.addEventListener('click', () => {
              console.log('===print===')
              this.setCaseLogList(2)
            })
            const download = iframe.contentWindow.document.querySelector('#download')
            download.addEventListener('click', () => {
              console.log('===download===')
              this.setCaseLogList(1)
            })
            console.log('onload')
          }
          console.log(iframe)
        })
      })
    },
    viewRecords(id) {
      this.recomId = id
      this.getCaseLogList()

    },
    setCaseLogList(type) {
      const params = {}
      params.recordId = this.recomId
      params.type = type
      setCaseLogList(params)
    }
  }
}
</script>
