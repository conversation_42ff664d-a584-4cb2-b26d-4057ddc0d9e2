<template>
  <div class="app-container">
    <el-row :gutter="12">
      <el-col :span="12">
        <el-card shadow="always">
          <el-col
            v-if="recordInfo.userBaseInfo.doctorRecordStatus === 0"
            :span="12"
            class="item"
          >备案状态：未申请备案</el-col>
          <el-col
            v-if="recordInfo.userBaseInfo.doctorRecordStatus === 1"
            :span="12"
            class="item"
          >备案状态：备案审核中</el-col>
          <el-col
            v-if="recordInfo.userBaseInfo.doctorRecordStatus === 2"
            :span="12"
            class="item"
          >备案状态：备案成功</el-col>
          <el-col
            v-if="recordInfo.userBaseInfo.doctorRecordStatus === 3"
            :span="12"
            class="item"
          >备案状态：备案失败</el-col>
        </el-card>
      </el-col>
    </el-row>
    <el-form ref="dataForm" :model="recordInfo" :rules="rules" :inline="true" label-position="left">
      <el-row v-if="recordInfo.hospitalInfo.orgCode">
        <el-col :span="24">
          <el-form-item label="签约医疗机构编码">
            <span>{{ recordInfo.hospitalInfo.orgCode }}</span>
          </el-form-item>
          <el-form-item label="签约医疗机构名称">
            <span>{{ recordInfo.hospitalInfo.orgName }}</span>
          </el-form-item>
          <el-form-item label="机构登记号">
            <span>{{ recordInfo.hospitalInfo.orgRegNo }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row v-if="recordInfo.hospitalInfo.orgCode">
        <el-col :span="24">
          <el-form-item label="机构地址">
            <span>{{ recordInfo.hospitalInfo.orgAddress }}</span>
          </el-form-item>
          <el-form-item label="机构地址邮编">
            <span>{{ recordInfo.hospitalInfo.orgPostalCode }}</span>
          </el-form-item>
          <el-form-item label="单位电话">
            <span>{{ recordInfo.hospitalInfo.orgTel }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="机构内人员编码" prop="userId">
            <span>{{ recordInfo.userBaseInfo.userId }}</span>
          </el-form-item>
          <el-form-item label="人员类型" prop="userType">
            <span>{{ recordInfo.userBaseInfo.userTypeDescribe }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="姓名">
            <el-input v-model="recordInfo.userBaseInfo.name" disabled />
          </el-form-item>
          <el-form-item label="身份证号" prop="userBaseInfo.idCardNum">
            <el-input
              v-model="recordInfo.userBaseInfo.idCardNum"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="性别">
            <el-input v-model="recordInfo.userBaseInfo.gender" disabled />
          </el-form-item>
          <el-form-item label="出生日期">
            <el-input v-model="recordInfo.userBaseInfo.birthday" disabled />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="民族" prop="userInfo.nationCode">
            <el-cascader
              v-model="recordInfo.userInfo.nationCode"
              :options="standardData"
              :props="standardProps"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="家庭地址" prop="userInfo.cityId">
            <el-cascader
              v-model="recordInfo.userInfo.cityId"
              :options="cityData"
              :props="cityProps"
              :show-all-levels="false"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="家庭地址邮编" prop="userInfo.zipCode">
            <el-input
              v-model="recordInfo.userInfo.zipCode"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="职称" prop="userBaseInfo.titleId">
            <DictSelect
              v-model="recordInfo.userBaseInfo.titleId"
              placeholder="请选择"
              type="doctor_title"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="联系手机号" prop="userBaseInfo.phone">
            <el-input
              v-model="recordInfo.userBaseInfo.phone"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="健康状况" prop="userInfo.health">
            <DictSelect
              v-model="recordInfo.userInfo.health"
              placeholder="请选择"
              type="health"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="学历" prop="userInfo.education">
            <DictSelect
              v-model="recordInfo.userInfo.education"
              placeholder="请选择"
              type="education"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-form-item label="业务水平考核机构或组织名称、考核培训时间及结果" prop="userInfo.appraisal">
          <el-input
            v-model="recordInfo.userInfo.appraisal"
            type="textarea"
            :rows="5"
            style="width:600px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="何时何地因何种原因受过何种处罚或处分" prop="userInfo.punish">
          <el-input
            v-model="recordInfo.userInfo.punish"
            type="textarea"
            :rows="5"
            style="width:600px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="其它要说明的问题" prop="userInfo.other">
          <el-input
            v-model="recordInfo.userInfo.other"
            type="textarea"
            :rows="5"
            style="width:600px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>
      <span>
        <i style="color: red;">*</i>个人工作经历列表
      </span>
      <el-button
        v-if="
          recordInfo.userBaseInfo.recordStatus != 1 &&
            recordInfo.userBaseInfo.recordStatus != 2
        "
        v-waves
        icon="el-icon-plus"
        type="primary"
        circle
        @click="handleCreate"
      />
      <el-divider />
      <el-table :data="recordInfo.workInfos" border style="width: 100%">
        <el-table-column prop="startDate" label="开始时间" width="180" align="center" />
        <el-table-column prop="endDate" label="结束时间" width="180" align="center" />
        <el-table-column prop="employer" label="单位名称" align="center" />
        <el-table-column prop="title" label="技术职务" align="center" />
        <el-table-column prop="certifier" label="证明人" width="180" align="center" />
        <el-table-column
          v-if="
            recordInfo.userBaseInfo.recordStatus != 1 &&
              recordInfo.userBaseInfo.recordStatus != 2
          "
          label="操作"
          prop="createdAt"
          width="200px"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-edit"
              @click="handleUpdate(scope.$index,scope.row)"
            >编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.$index,recordInfo.workInfos)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <br />
      <el-row>
        <el-col :span="24">
          <el-form-item label="医师第一执业机构编码" prop="userInfo.hospitalCode">
            <el-input
              v-model="recordInfo.userInfo.hospitalCode"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="医师第一执业机构名称" prop="userInfo.hospitalName">
            <el-input
              v-model="recordInfo.userInfo.hospitalName"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="执业证号" prop="userBaseInfo.pracNum">
            <el-input
              v-model="recordInfo.userBaseInfo.pracNum"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="执业证取得时间" prop="userInfo.pracIssue">
            <el-date-picker
              v-model="recordInfo.userInfo.pracIssue"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="资格证号" prop="userBaseInfo.certNum">
            <el-input
              v-model="recordInfo.userBaseInfo.certNum"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="资格证取得时间" prop="userInfo.certIssue">
            <el-date-picker
              v-model="recordInfo.userInfo.certIssue"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="医师执业级别" prop="userInfo.pracLevel">
            <DictSelect
              v-model="recordInfo.userInfo.pracLevel"
              placeholder="请选择"
              type="prac_level"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
          <el-form-item label="医师执业类别" prop="userInfo.pracType">
            <el-select v-model="recordInfo.userInfo.pracType" :disabled="recordInfo.userBaseInfo.recordStatus == 1 || recordInfo.userBaseInfo.recordStatus == 2" placeholder="请选择">
              <el-option
                v-for="item in pracTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <!-- <el-input
              v-model="recordInfo.userInfo.pracType"
              placeholder="临床、口腔、公卫、中医"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            /> -->
          </el-form-item>
          <el-form-item label="医师执业范围" prop="userInfo.pracScopeApproval">
            <el-input
              v-model="recordInfo.userInfo.pracScopeApproval"
              placeholder="xx专业"
              clearable
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="执业有效期开始时间" prop="userInfo.pracStartDate">
            <el-date-picker
              v-model="recordInfo.userInfo.pracStartDate"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="执业有效期结束时间" prop="userInfo.pracEndDate">
            <el-date-picker
              v-model="recordInfo.userInfo.pracEndDate"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="申请拟执业医疗机构意见时间" prop="userInfo.applyDate">
            <el-date-picker
              v-model="recordInfo.userInfo.applyDate"
              clearable
              type="date"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期"
              style="width:130px"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-form-item label="申请拟执业医疗机构意见" prop="userInfo.auditOpinion">
          <el-input
            v-model="recordInfo.userInfo.auditOpinion"
            type="textarea"
            :rows="5"
            style="width:650px"
            clearable
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
          />
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="申请拟执业医疗机构-电子章" prop="electronic">
          <el-upload :action="uploadPath +'3'" :headers="headers" disabled :show-file-list="false" accept="image/png, image/gif, image/jpeg, image/jpg">
            <img
              v-if="recordInfo.userBaseInfo.electronic"
              :src="recordInfo.userBaseInfo.electronic"
              class="uploadimg"
            />
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="互联网医院聘任合同照片" prop="workContracts">
          <el-upload
            list-type="picture-card"
            :action="uploadPath +'8'"
            :headers="headers"
            :file-list="recordInfo.workContracts"
            :on-success="handleWorkContractsSuccess"
            :on-remove="handleRemoveWorkContracts"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />

              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2 && file.uploadMark!=1"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'workContracts')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="医师数字签名留样" prop="sealImage">
          <el-upload
            :action="uploadPath +'7'"
            :headers="headers"
            disabled
            :show-file-list="false"
            :on-success="handleSealImageSuccess"
            accept="image/png, image/gif, image/jpeg, image/jpg"
          >
            <el-image
              v-if="recordInfo.userBaseInfo.sealImage"
              :src="recordInfo.userBaseInfo.sealImage"
              style="width: 200px; height: 100px"
              :fit="contain"
            ></el-image>
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="医师身份证文件" prop="idCardUrls">
          <el-upload
            list-type="picture-card"
            :action="uploadPath +'4'"
            :headers="headers"
            :file-list="idCardUrls"
            :limit="2"
            :on-exceed="handleExceed"
            :on-success="handleIdCardUrlsSuccess"
            :on-remove="handleRemoveIdCardUrls"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'idCardUrls')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="医师执业证文件" prop="pracUrls">
          <el-upload
            list-type="picture-card"
            :action="uploadPath +'3'"
            :headers="headers"
            :file-list="pracUrls"
            :limit="3"
            :on-exceed="handleExceed"
            :on-success="handlePracUrlsSuccess"
            :on-remove="handleRemovePracUrls"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'pracUrls')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="医师资格证文件" prop="certUrls">
          <el-upload
            list-type="picture-card"
            :action="uploadPath +'6'"
            :headers="headers"
            :file-list="certUrls"
            :limit="2"
            :on-exceed="handleExceed"
            :on-success="handleCertUrlsSuccess"
            :on-remove="handleRemoveCertUrls"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'certUrls')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="毕业证" prop="userBaseInfo.headUrl">
          <el-upload
            :limit="1"
            list-type="picture-card"
            :action="uploadPath + '1'"
            :headers="headers"
            :on-success="handleHeadUrlSuccess"
            :file-list="headUrl"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'headUrl')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="毕业证照片" prop="userInfo.graduateCertUrl">
          <el-upload
            :limit="1"
            list-type="picture-card"
            :action="uploadPath + '9'"
            :headers="headers"
            :on-success="handlegraduateCertUrlSuccess"
            :file-list="graduateCertUrl"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'graduateCertUrl')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item label="学位证照片" prop="userInfo.degreeCertUrl">
          <el-upload
            :limit="1"
            list-type="picture-card"
            :action="uploadPath + '10'"
            :headers="headers"
            :on-success="handledegreeCertUrlSuccess"
            :file-list="degreeCertUrl"
            :disabled="
              recordInfo.userBaseInfo.recordStatus == 1 ||
                recordInfo.userBaseInfo.recordStatus == 2
            "
            accept="image/png, image/jpg"
          >
            <div slot="file" slot-scope="{ file }">
              <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
              <span class="el-upload-list__item-actions">
                <span class="el-upload-list__item-preview" @click="handlePreview(file)">
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                  <i class="el-icon-download"></i>
                </span>
                <span
                  v-if="recordInfo.userBaseInfo.recordStatus != 2"
                  class="el-upload-list__item-delete"
                  @click="handleRemoveUrls(file, 'degreeCertUrl')"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
            <el-button
              size="small"
              type="primary"
              :disabled="
                recordInfo.userBaseInfo.recordStatus == 1 ||
                  recordInfo.userBaseInfo.recordStatus == 2
              "
            >点击上传</el-button>
            <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
          </el-upload>
        </el-form-item>
      </el-row>
      <!--      <el-row>-->
      <!--        <el-col :span="24">-->
      <!--          <el-form-item label="是否同意以上条款？" prop="agree">-->
      <!--            <el-switch-->
      <!--              v-model="agree"-->
      <!--              active-color="#13ce66"-->
      <!--              inactive-color="#ff4949"-->
      <!--              style="width:150px"-->
      <!--            />-->
      <!--          </el-form-item>-->
      <!--        </el-col>-->
      <!--      </el-row>-->
    </el-form>
    <div
      v-if="
        recordInfo.userBaseInfo.recordStatus !== 1 &&
          recordInfo.userBaseInfo.recordStatus !== 2
      "
      style="text-align:center;"
    >
      <el-button
        v-if="
          recordInfo.userBaseInfo.recordStatus == null ||
            recordInfo.userBaseInfo.recordStatus === 0 ||
            recordInfo.userBaseInfo.recordStatus === 3 ||
            recordInfo.userBaseInfo.recordStatus === 4
        "
        type="primary"
        @click="save(0)"
      >保存</el-button>
      <el-button
        v-if="
          recordInfo.userBaseInfo.accountType == 0 &&
            (recordInfo.userBaseInfo.recordStatus == null ||
              recordInfo.userBaseInfo.recordStatus === 0 ||
              recordInfo.userBaseInfo.recordStatus === 3 ||
              recordInfo.userBaseInfo.recordStatus === 4)
        "
        :disabled="recordInfo.userBaseInfo.status !== 1"
        type="primary"
        @click="save(1)"
      >保存并提交</el-button>
      <el-button
        v-if="
          recordInfo.userBaseInfo.accountType == 1 &&
            (recordInfo.userBaseInfo.doctorRecordStatus !== 2 &&
              recordInfo.userBaseInfo.doctorRecordStatus !== 3 &&
              recordInfo.userBaseInfo.doctorRecordStatus !== 0)
        "
        :disabled="recordInfo.userBaseInfo.status !== 1"
        type="primary"
        @click="handleRecordStatusSuccess()"
      >备案通过</el-button>
      <el-button
        v-if="
          recordInfo.userBaseInfo.doctorRecordStatus !== 2 &&
            recordInfo.userBaseInfo.doctorRecordStatus !== 3 &&
            recordInfo.userBaseInfo.doctorRecordStatus !== 0
        "
        type="primary"
        @click="handleRecordStatus(3)"
      >备案失败</el-button>
      <el-button type="primary" @click="goBack">取消</el-button>
    </div>
    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      top="2vh"
      width="500px"
    >
      <el-form
        ref="workInfosDataForm"
        :model="workInfo"
        :rules="workInfoRules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item label="开始时间" prop="startDate">
          <el-date-picker
            v-model="workInfo.startDate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker
            v-model="workInfo.endDate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="单位名称" prop="employer">
          <el-input v-model="workInfo.employer" placeholder="请输入单位名称" />
        </el-form-item>
        <el-form-item label="技术职务" prop="title">
          <DictSelect v-model="workInfo.title" placeholder="请选择" type="record_doctor_title" />
        </el-form-item>
        <el-form-item label="证明人" prop="certifier">
          <el-input v-model="workInfo.certifier" placeholder="请输入证明人" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">取消</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="备案审核" :visible.sync="dialogRecordStatusInfoVisible" append-to-body>
      <el-form
        ref="userRecordStatusDataForm"
        :model="userRecordStatus"
        :rules="recordStatusRules"
        label-position="right"
        label-width="150px"
        style="width:90%"
      >
        <el-form-item label="审核原因" prop="failureReason">
          <el-input
            v-model="userRecordStatus.failureReason"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogRecordStatusInfoVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleRecordStatusData()">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>
<style scoped>
/* .el-form-item__label{
    font-size: 12px
} */

.uploadimg {
  width: 148px;
  height: 148px;
}
.item {
  margin-bottom: 18px;
}

.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
}
</style>
<script>
import {
  getRecordInfo,
  getCityList,
  getStandardList,
  updateRecordInfo,
  authAudit
} from '@/api/user/doctor'
import DictSelect from '@/components/DictSelect'
import waves from '@/directive/waves' // Waves directive
import { getToken, getTokenName } from '@/utils/auth'
import { Loading } from 'element-ui'
import { customFormat } from '@/utils/date'
export default {
  name: '',
  directives: { waves },
  components: {
    DictSelect
  },
  data() {
    return {
      doctorId: null,
      fileList: [],
      recordInfo: {
        hospitalInfo: {},
        userBaseInfo: {},
        userInfo: {},
        workInfos: [],
        workContracts: []
      },
      workContracts: [],
      cityData: null,
      cityProps: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      standardData: null,
      standardProps: {
        value: 'code',
        emitPath: false,
        label: 'name'
      },
      workInfo: {},
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      idCardUrls: [],
      pracUrls: [],
      certUrls: [],
      agree: false,
      uploadPath: '',
      dialogRecordStatusInfoVisible: false,
      userRecordStatus: {
        failureReason: ''
      },
      recordStatusRules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'blur' }
        ],
        failureReason: [
          {
            validator: (rule, value, callback) => {
              if (
                this.userRecordStatus.status === 3 &&
                this.userRecordStatus.failureReason === ''
              ) {
                callback(new Error('请填写审核原因'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      },
      headers: {},
      rules: {
        'userBaseInfo.idCardNum': [
          {
            validator: (rule, value, callback) => {
              if (value === undefined) {
                callback(new Error('请输入身份证号'))
              } else {
                const idLength = value.length
                if (idLength === 15 || idLength === 18) {
                  callback()
                } else {
                  callback(new Error('身份证号码不正确'))
                }
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        'userBaseInfo.titleId': [
          { required: true, message: '请选择职称', trigger: 'blur' }
        ],
        'userInfo.health': [
          { required: true, message: '请输入健康状况', trigger: 'blur' }
        ],
        'userInfo.nationCode': [
          { required: true, message: '请选择民族', trigger: 'blur' }
        ],
        'userInfo.cityId': [
          { required: true, message: '请选择家庭地址', trigger: 'blur' }
        ],
        'userInfo.education': [
          { required: true, message: '请选择学历', trigger: 'blur' }
        ],
        'userInfo.zipCode': [
          { required: true, message: '请输入邮编', trigger: 'blur' }
        ],
        'userInfo.certIssue': [
          { required: true, message: '请选择资格证取得时间', trigger: 'blur' }
        ],
        'userBaseInfo.certNum': [
          { required: true, message: '请输入资格证号', trigger: 'blur' }
        ],
        'userBaseInfo.pracNum': [
          { required: true, message: '请输入职业证号', trigger: 'blur' }
        ],
        'userInfo.pracLevel': [
          { required: true, message: '请选择医师执业级别', trigger: 'blur' }
        ],
        'userInfo.pracScopeApproval': [
          { required: true, message: '请选择医师执业范围', trigger: 'blur' }
        ],
        'userInfo.pracType': [
          { required: true, message: '请选择医师执业类别', trigger: 'blur' }
        ],
        'userInfo.pracIssue': [
          { required: true, message: '请选择执业证取得时间', trigger: 'blur' }
        ],
        'userBaseInfo.phone': [
          { required: true, message: '请输入联系电话', trigger: 'blur' }
        ],
        'userInfo.appraisal': [
          {
            required: true,
            message: '请输入业务水平考核机构或组织名称、考核培训时间及结果',
            trigger: 'blur'
          }
        ],
        'userInfo.auditOpinion': [
          {
            required: true,
            message: '请输入申请拟执业医疗机构意见',
            trigger: 'blur'
          }
        ],
        'userInfo.pracEndDate': [
          {
            required: true,
            message: '请选择执业有效期结束时间',
            trigger: 'blur'
          }
        ],
        'userInfo.pracStartDate': [
          {
            required: true,
            message: '请选择执业有效期开始时间',
            trigger: 'blur'
          }
        ],
        'userInfo.applyDate': [
          {
            required: true,
            message: '请选择申请拟执业医疗机构意见时间',
            trigger: 'blur'
          }
        ],
        'userInfo.hospitalCode': [
          {
            required: true,
            message: '请输入医师第一执业机构编码',
            trigger: 'blur'
          }
        ],
        'userInfo.hospitalName': [
          {
            required: true,
            message: '请输入医师第一执业机构名称',
            trigger: 'blur'
          }
        ],
        'userBaseInfo.headUrl': [
          {
            validator: (rule, value, callback) => {
              if (value != null) {
                callback()
              } else {
                callback('请上传医师认证照片')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        workContracts: [
          {
            validator: (rule, value, callback) => {
              value = this.recordInfo.workContracts
              if (value != null && value.length > 0) {
                callback()
              } else {
                callback('请上传互联网医院聘任合同照片')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        // 'sealImage': [{
        //   validator: (rule, value, callback) => {
        //     value = this.recordInfo.userBaseInfo.sealImage
        //     if (value != null) {
        //       callback()
        //     } else {
        //       callback('请上传医师数字签名留样')
        //     }
        //   },
        //   required: true,
        //   trigger: 'blur'
        // }],
        idCardUrls: [
          {
            validator: (rule, value, callback) => {
              value = this.idCardUrls
              if (value != null && value.length === 2) {
                callback()
              } else {
                callback('请上传医师身份证文件正反面')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        pracUrls: [
          {
            validator: (rule, value, callback) => {
              value = this.pracUrls
              if (value != null && value.length > 0) {
                callback()
              } else {
                callback('请上传医师执业证文件')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        certUrls: [
          {
            validator: (rule, value, callback) => {
              value = this.certUrls
              if (value != null && value.length > 0) {
                callback()
              } else {
                callback('请上传医师资格证文件')
              }
            },
            required: true,
            trigger: 'blur'
          }
        ],
        'userInfo.punish': [
          {
            required: true,
            message: '请输入何时何地因何种原因受过何种处罚或处分',
            trigger: 'blur'
          }
        ],
        'userInfo.other': [
          {
            required: true,
            message: '请输入其它要说明的问题',
            trigger: 'blur'
          }
        ],
        'userInfo.graduateCertUrl': [
          {
            required: true,
            message: '请上传毕业证照片',
            trigger: 'blur'
          }
        ]
      },
      workInfoRules: {
        startDate: [
          { required: true, message: '请选择开始时间', trigger: 'blur' }
        ],
        endDate: [
          { required: true, message: '请选择结束时间', trigger: 'blur' }
        ],
        employer: [
          { required: true, message: '请输入单位名称', trigger: 'blur' }
        ],
        title: [{ required: true, message: '请输入技术职务', trigger: 'blur' }],
        certifier: [
          { required: true, message: '请输入证明人', trigger: 'blur' }
        ]
      },
      index: 0,
      dialogImageUrl: '',
      dialogVisible: false,
      disabled: false,
      headUrl: [],
      // 临床、口腔、公卫、中医
      pracTypeOptions: [{
        value: '临床',
        label: '临床'
      },
      {
        value: '口腔',
        label: '口腔'
      },
      {
        value: '公卫',
        label: '公卫'
      },
      {
        value: '中医',
        label: '中医'
      }],
      graduateCertUrl: [],
      degreeCertUrl: []
    }
  },
  watch: {
    'recordInfo.userBaseInfo.idCardNum': function(newVal, oldVal) {
      const iden = this.recordInfo.userBaseInfo.idCardNum
      const idLength = iden.length
      if (idLength === 15 || idLength === 18) {
        let sex = null
        let birth = null
        if (idLength === 18) {
          sex = iden.substring(16, 17)
          birth =
            iden.substring(6, 10) +
            '-' +
            iden.substring(10, 12) +
            '-' +
            iden.substring(12, 14)
        }
        if (idLength === 15) {
          sex = iden.substring(13, 14)
          birth =
            '19' +
            iden.substring(6, 8) +
            '-' +
            iden.substring(8, 10) +
            '-' +
            iden.substring(10, 12)
        }

        if (sex % 2 === 0) {
          sex = '女'
        } else {
          sex = '男'
        }
        this.recordInfo.userBaseInfo.gender = sex
        this.recordInfo.userBaseInfo.birthday = birth
      } else {
        this.recordInfo.userBaseInfo.gender = ''
        this.recordInfo.userBaseInfo.birthday = ''
      }
    }
  },
  created() {
    if (this.$route.params.doctorId) {
      this.doctorId = this.$route.params.doctorId
      this.uploadPath =
        process.env.VUE_APP_BASE_API +
        '/user/doctor/upload/' +
        this.doctorId +
        '/'
      this.getRecordInfo()
    }
    this.headers[getTokenName()] = getToken()
  },
  methods: {
    // 获取数据
    getRecordInfo() {
      getRecordInfo(this.doctorId).then((response) => {
        this.recordInfo = response
        // 默认展示数据 applyDate pracLevel
        this.recordInfo.userInfo.applyDate = this.recordInfo.userInfo.applyDate ? this.recordInfo.userInfo.applyDate : customFormat(new Date().getTime(), 'yyyy-MM-dd')
        this.recordInfo.userInfo.pracLevel = this.recordInfo.userInfo.pracLevel ? this.recordInfo.userInfo.pracLevel : '执业医师'
        console.log(customFormat(new Date().getTime(), 'yyyy-MM-dd'), 1300)
        for (var i in response.userBaseInfo.idCardUrls) {
          this.idCardUrls.push({ url: response.userBaseInfo.idCardUrls[i] })
        }
        for (var p in response.userBaseInfo.pracUrls) {
          this.pracUrls.push({ url: response.userBaseInfo.pracUrls[p] })
        }
        for (var c in response.userBaseInfo.certUrls) {
          this.certUrls.push({ url: response.userBaseInfo.certUrls[c] })
        }
        if (response.userBaseInfo.headUrl) {
          this.headUrl.push({ url: response.userBaseInfo.headUrl })
        }
        if (response.userInfo.graduateCertUrl) {
          this.graduateCertUrl.push({ url: response.userInfo.graduateCertUrl })
        }
        if (response.userInfo.degreeCertUrl) {
          this.degreeCertUrl.push({ url: response.userInfo.degreeCertUrl })
        }
      })
      getCityList().then((response) => {
        this.cityData = response
      })
      getStandardList().then((response) => {
        this.standardData = response
      })
    },
    compareTime(date) {
      const now = new Date().getTime()
      const endDate = new Date(date).getTime()
      const long = 6 * 30 * 24 * 60 * 60 * 1000
      return endDate - now > long
    },
    save(action) {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.recordInfo.workInfos.length == 0) {
            this.$message.error('请完善个人工作经历')
            return false
          }
          this.recordInfo.action = action
          if (this.compareTime(this.recordInfo.userInfo.pracEndDate)) {
            const loadingInstance = Loading.service({ fullscreen: true })
            updateRecordInfo(this.recordInfo)
              .then(() => {
                this.dialogFormVisible = false
                this.$router.push({
                  path: '/user/doctor'
                })
              })
              .finally(() => {
                loadingInstance.close()
              })
          } else {
            this.$confirm(
              '当前执业有效期结束不足6个月，是否确定提交？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }
            ).then(() => {
              const loadingInstance = Loading.service({ fullscreen: true })
              updateRecordInfo(this.recordInfo)
                .then(() => {
                  this.dialogFormVisible = false
                  this.$router.push({
                    path: '/user/doctor'
                  })
                })
                .finally(() => {
                  loadingInstance.close()
                })
            })
          }
        } else {
          this.$message.error('请完善备案信息')
        }
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.workInfo = {}
        this.$refs['workInfosDataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['workInfosDataForm'].validate((valid) => {
        if (valid) {
          this.recordInfo.workInfos.push(Object.assign({}, this.workInfo))
          this.dialogFormVisible = false
        }
      })
    },
    handleUpdate(index, row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.index = index
      this.$nextTick(() => {
        this.workInfo = Object.assign({}, row)
        this.$refs['workInfosDataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['workInfosDataForm'].validate((valid) => {
        if (valid) {
          this.recordInfo.workInfos.splice(
            this.index,
            1,
            Object.assign({}, this.workInfo)
          )
          this.dialogFormVisible = false
        }
      })
    },
    handleDelete(index, rows) {
      this.$confirm('此操作将删除该工作经历, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        rows.splice(index, 1)
      })
    },
    handleRemoveIdCardUrls(file, fileList) {
      this.recordInfo.userBaseInfo.idCardUrls = []
      for (var i in fileList) {
        this.recordInfo.userBaseInfo.idCardUrls.push(fileList[i].url)
      }
      this.idCardUrls = fileList
    },
    handleIdCardUrlsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.idCardUrls.push({ url: response.data })
        this.recordInfo.userBaseInfo.idCardUrls = []
        for (var i in this.idCardUrls) {
          this.recordInfo.userBaseInfo.idCardUrls.push(this.idCardUrls[i].url)
        }
      }
    },

    handleRemoveCertUrls(file, fileList) {
      this.recordInfo.userBaseInfo.certUrls = []
      for (var i in fileList) {
        this.recordInfo.userBaseInfo.certUrls.push(fileList[i].url)
      }
      this.certUrls = fileList
    },
    handleCertUrlsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.certUrls.push({ url: response.data })
        this.recordInfo.userBaseInfo.certUrls = []
        for (var i in this.certUrls) {
          this.recordInfo.userBaseInfo.certUrls.push(this.certUrls[i].url)
        }
      }
    },
    handleRemovePracUrls(file, fileList) {
      this.recordInfo.userBaseInfo.pracUrls = []
      for (var i in fileList) {
        this.recordInfo.userBaseInfo.pracUrls.push(fileList[i].url)
      }
      this.pracUrls = fileList
    },
    handlePracUrlsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.pracUrls.push({ url: response.data })
        this.recordInfo.userBaseInfo.pracUrls = []
        for (var i in this.pracUrls) {
          this.recordInfo.userBaseInfo.pracUrls.push(this.pracUrls[i].url)
        }
      }
    },

    handleRemoveWorkContracts(file, fileList) {
      this.recordInfo.workContracts = fileList
      this.workContracts = fileList
    },
    handleWorkContractsSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.workContracts.push({ url: response.data })
        this.recordInfo.workContracts.push({ url: response.data })
        console.log(this.workContracts, this.recordInfo.workContracts)
      }
    },
    handleSealImageSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.recordInfo.userBaseInfo.sealImage = response.data // URL.createObjectURL(file.raw)
      }
    },
    handleHeadUrlSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.headUrl.push({ url: response.data })
        this.recordInfo.userBaseInfo.headUrl = response.data // URL.createObjectURL(file.raw)
        console.log(this.headUrl, this.recordInfo.userBaseInfo.headUrl)
      }
    },
    handlegraduateCertUrlSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.graduateCertUrl.push({ url: response.data })
        this.recordInfo.userInfo.graduateCertUrl = response.data // URL.createObjectURL(file.raw)
      }
    },
    handledegreeCertUrlSuccess(response, file, fileList) {
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.degreeCertUrl.push({ url: response.data })
        this.recordInfo.userInfo.degreeCertUrl = response.data // URL.createObjectURL(file.raw)
      }
    },
    handleExceed(files, fileList) {
      this.$message.error('文件数量超出上传限制！')
    },
    handleRecordStatus(status) {
      this.dialogRecordStatusInfoVisible = true
      this.$nextTick(() => {
        this.$refs['userRecordStatusDataForm'].clearValidate()
        this.userRecordStatus = {
          failureReason: '',
          doctorId: this.doctorId,
          status: status
        }
      })
    },
    handleRecordStatusSuccess() {
      this.userRecordStatus = {
        failureReason: '',
        doctorId: this.doctorId,
        status: 2
      }
      authAudit(this.userRecordStatus).then(() => {
        this.dialogRecordStatusInfoVisible = false
        this.goBack()
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
      })
    },
    handleRecordStatusData() {
      this.$refs['userRecordStatusDataForm'].validate((valid) => {
        if (valid) {
          this.$confirm('此操作将审核用户状态, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            authAudit(this.userRecordStatus).then(() => {
              this.dialogRecordStatusInfoVisible = false
              this.goBack()
              this.$message({
                type: 'success',
                message: '操作成功!'
              })
            })
          })
        }
      })
    },
    goBack() {
      this.$router.push({
        path: '/user/doctor'
      })
    },
    handleRemoveUrls(file, name) {
      this.$confirm('此操作将删除信息照片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (name === 'graduateCertUrl' || name === 'degreeCertUrl') {
          this[name].splice(0, 1)
          this.recordInfo.userInfo[name] = ''
        }
        if (name === 'workContracts') {
          const index = this.recordInfo.workContracts.findIndex(
            i => i.url === file.url
          )
          this.recordInfo.workContracts.splice(index, 1)
          console.log(this.workContracts, this.recordInfo.workContracts)
        } else {
          const index = this[name].findIndex(i => i.url === file.url)
          this[name].splice(index, 1)
          if (name == 'headUrl') {
            this.recordInfo.userBaseInfo.headUrl = ''
          }
        }
      })
    },
    handlePreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleDownload(file) {
      console.log(file)
      this.downloadIamge(file, new Date().getTime())
    },
    downloadIamge(imgsrc, name) {
      //下载图片地址和图片名
      var image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function() {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var url = canvas.toDataURL('image/png') //得到图片的base64编码数据
        var a = document.createElement('a') // 生成一个a元素
        var event = new MouseEvent('click') // 创建一个单击事件
        a.download = name || 'photo' // 设置图片名称
        a.href = url // 将生成的URL设置为a.href属性
        a.dispatchEvent(event) // 触发a的单击事件
      }
      image.src = imgsrc
    }
  }
}
</script>
