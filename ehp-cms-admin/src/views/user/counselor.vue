<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        placeholder="ID"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.workNumber"
        clearable
        placeholder="工号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="咨询师名称"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        placeholder="手机号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        v-waves
        type="primary"
        @click="handleAdd"
      >添加咨询师
      </el-button>
      <el-upload
        style="display: inline-block;"
        :action="uploadPath"
        :headers="headers"
        :on-success="handleExcelSuccess"
        :on-error="handleExcelError"
        :file-list="fileList"
        :limit="1"
        :show-file-list="false"
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      >
        <el-button
          type="primary"
          icon="el-icon-upload2"
        >批量导入咨询师</el-button>
        <div slot="tip" class="el-upload__tip"></div>
      </el-upload>
      <el-button
        v-waves
        class="mr10"
        icon="el-icon-download"
        @click="haneleDownExcel"
      >
        下载批量导入模版
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      ref="table"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
      <el-table-column
        label="工号"
        prop="workNumber"
        width="110px"
        align="center"
      />
      <el-table-column label="姓名" prop="name" width="110px" align="center" />
      <el-table-column
        label="岗位"
        prop="positionName"
        align="center"
        width="200"
      />
      <el-table-column
        label="手机号"
        prop="phone"
        align="center"
        width="200"
      />
      <el-table-column
        label="状态"
        prop="accountStatus"
        align="center"
        width="150px"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.accountStatus === 0"
            size="small"
          > 启用 </el-tag>
          <el-tag
            v-if="row.accountStatus === 1"
            size="danger"
          > 停用 </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        min-width="150"
        prop="createdAt"
        align="center"
      />
      <el-table-column label="操作" width="160" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(scope.row.id)"
          >编辑
          </el-button>
          <el-button
            v-if="scope.row.accountStatus===0"
            size="mini"
            type="danger"
            @click="handleStatus(scope.row.id,1)"
          >停用
          </el-button>
          <el-button
            v-if="scope.row.accountStatus===1"
            size="mini"
            type="primary"
            @click="handleStatus(scope.row.id,0)"
          >启用
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogTableVisible"
      width="50%"
    >
      <el-form
        ref="dataForm"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="咨询师姓名" prop="name">
              <el-input
                v-model="formData.name"
                placeholder="请输入咨询师姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工号" prop="workNumber">
              <el-input v-model="formData.workNumber" placeholder="请输入工号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="手机号" prop="phone">
              <el-input
                v-model="formData.phone"
                placeholder="请输入手机号"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号">
              <el-input v-model="formData.idcard" placeholder="请输入身份证号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="岗位">
              <el-input
                v-model="formData.positionName"
                placeholder="请输入岗位"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogTableVisible = false">取消</el-button>
        <el-button
          v-waves
          type="primary"
          @click="submit('dataForm')"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  get,
  save,
  Export,
  getList,
  editStatus
} from '@/api/user/counselor'
import waves from '@/directive/waves' // Waves directive
import { Message } from 'element-ui'
import { getToken, getTokenName } from '@/utils/auth'
import { Toast } from 'vant'
export default {
  name: 'Counselor',
  directives: { waves },
  filters: {},
  data() {
    return {
      dialogTableVisible: false,
      dialogTitle: '详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      rules: {
        name: [{ required: true, message: '请输入咨询师姓名', trigger: 'blur' }],
        workNumber: [{ required: true, message: '请输入工号', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }]
      },
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: [],
      formData: {}
    }
  },
  created() {
    this.uploadPath = process.env.VUE_APP_BASE_API + '/user/counselor/import'
    this.headers[getTokenName()] = getToken()
    this.getList()
    this.getTemplate()
  },
  methods: {
    handleStatus(id, status) {

      this.$confirm(status === 0 ? '是否启用咨询师？' : '是否停用咨询师', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        return editStatus(id, status)
      }).then(() => {
        Toast({
          message: '操作成功',
          icon: 'success'
        })
        this.getList()
      })

    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getTemplate() {
      Export().then(response => {
        this.tempPath = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.phone = ''
      this.listQuery.workNumber = ''
      this.listQuery.id = ''
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.formData = {}
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleAdd() {
      this.dialogTitle = '添加'
      this.dialogTableVisible = true
      this.resetTemp()
    },
    submit(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          save(this.formData)
            .then(res => {
              Message({
                message: this.formData.id ? '修改成功' : '添加成功',
                type: 'success',
                duration: 5 * 1000
              })
              this.getList()
            })
            .finally(() => {
              this.dialogTableVisible = false
            })
        }
      })
    },
    handleEdit(id) {
      this.dialogTitle = '编辑'
      get(id).then(response => {
        console.log(response, 304)
        this.formData = response
        this.dialogTableVisible = true
        console.log(response, '编辑')
      })
    },
    haneleDownExcel() {
      window.location.href = this.tempPath
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.getList()
        this.$message({
          message: '导入成功',
          type: 'success'
        })
        window.location.href = response.data
      }
    }
  }
}
</script>
<style scoped></style>
