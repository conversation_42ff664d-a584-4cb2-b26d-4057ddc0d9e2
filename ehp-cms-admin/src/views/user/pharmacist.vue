<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="药师姓名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        placeholder="药师手机号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.licenseNo"
        clearable
        placeholder="药师资格证编号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.cardNo"
        clearable
        placeholder="身份证"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.titleId"
        placeholder="药师职称"
        style="width: 120px"
        type="pharmacist_title"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button
        v-waves
        v-permission="['user:pharmacist:add']"
        type="primary"
        @click="addPharmacist"
      >添加药师
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      ref="table"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="ID"
        fixed
        prop="id"
        width="90px"
        align="center"
      />
      <el-table-column
        label="姓名"
        prop="name"
        width="100px"
        align="center"
      >
        <template slot-scope="{row}">
          <el-link
            v-waves
            type="primary"
            :underline="false"
            @click="pharmacistInfo(row.id)"
          >{{ row.name }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="性别"
        prop="genderDescribe"
        width="110px"
        align="center"
      />
      <el-table-column
        label="手机"
        prop="phone"
        width="110px"
        align="center"
      />
      <el-table-column
        label="药师资格证编号"
        prop="licenseNo"
        align="center"
        width="200"
      />
      <el-table-column
        label="身份证"
        prop="cardNo"
        align="center"
        width="200"
      />
      <el-table-column
        v-if="checkPer(['env.hainan'])"
        label="备案状态"
        prop="recordStatusDescribe"
        align="center"
        width="150px"
      >
        <template slot-scope="{row}">
          <div>
            <el-tag
              v-if="row.recordStatus == 0 || row.recordStatus == 1"
              @click="openDoctor(row.id,1)"
            >未备案 </el-tag>
            <el-tag
              v-if="row.recordStatus === 2"
              type="success"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordStatus === 3"
              type="danger"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
            <el-tag
              v-if="row.recordStatus === 4"
              type="info"
              @click="openDoctor(row.id,1)"
            >{{ row.recordStatusDescribe }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="电子签名状态"
        align="center"
        width="100"
      >
        <template slot-scope="{row}">{{ authStatusArr[row.authStatus] }}</template>
      </el-table-column>
      <el-table-column
        label="职称"
        prop="titleIdDescribe"
        width="130px"
        align="center"
      />
      <el-table-column
        label="注册时间"
        width="150"
        prop="createdAt"
        align="center"
      />
      <el-table-column
        label="操作"
        width="320"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.accountStatus == 0"
            v-permission="['user:pharmacis:status']"
            size="mini"
            type="danger"
            @click="changeStatus(scope.row.id,1)"
          >暂停审方</el-button>
          <el-button
            v-else
            v-permission="['user:pharmacis:status']"
            type="primary"
            size="mini"
            @click="changeStatus(scope.row.id,0)"
          >恢复审方</el-button>
          <el-button
            v-permission="['user:pharmacis:record']"
            type="primary"
            size="mini"
            @click="pharmacistInfo(scope.row.id)"
          >编辑信息
          </el-button>
          <el-button
            v-if="checkPer(['env.hainan']) && checkPer(['user:doctor:audit'])"
            type="primary"
            size="mini"
            @click="record(scope.row.id)"
          >完善备案信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogPharmacistTitle"
      :visible.sync="dialogTableVisible"
      width="80%"
      top="2vh"
    >
      <el-link type="warning" style="color: #f06454; margin-bottom: 20px;" disabled>重要提示：药师信息添加成功后，需进一步完善备案信息提交，备案审核通过后才能使用该账号</el-link>
      <el-form
        ref="dataForm"
        :model="pharmacist"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="药师姓名"
              prop="name"
            >
              <el-input
                v-model="pharmacist.name"
                maxlength="10"
                :readonly="pharmacist.authStatus==1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="身份证号"
              prop="cardNo"
            >
              <el-input
                v-model="pharmacist.cardNo"
                maxlength="18"
                :readonly="pharmacist.authStatus==1"
                @blur="parseCardId"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="性别"
              prop="sex"
            >
              <el-input
                v-model="pharmacist.sex"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="出生日期"
              prop="birth"
            >
              <el-input
                v-model="pharmacist.birth"
                readonly
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="级别"
              prop="titleId"
            >
              <DictSelect
                v-model="pharmacist.titleId"
                placeholder="药师职称"
                style="width: 120px"
                type="pharmacist_title"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="专业"
              prop="skill"
            >
              <el-input v-model="pharmacist.skill" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="批准日期"
              prop="licenseDate"
            >
              <el-date-picker
                v-model="pharmacist.licenseDate"
                type="date"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="药师资格证编号"
              prop="licenseNo"
            >
              <el-input v-model="pharmacist.licenseNo" maxlength="40" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="药师手机号"
              prop="phone"
            >
              <el-input v-model="pharmacist.phone" maxlength="11" :readonly="pharmacist.authStatus==1" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="民族"
              prop="ethnicity"
            >
              <el-select
                v-model="pharmacist.ethnicity"
                placeholder="请选择民族"
              >
                <el-option
                  v-for="item in nationList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执业药师类型" prop="drugType">
              <el-checkbox-group v-model="pharmacist.drugType">
                <el-checkbox v-for="(type) in drugTypes" :key="type.key" :data-type="type.key" :label="type.key" @change="(value)=>{drugTypeChage(value,type.key)}">{{ type.value }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审方类型" prop="reviewType">
              <el-checkbox-group v-model="pharmacist.reviewType">
                <el-checkbox v-for="(type) in reviewTypes" :key="type.key" :label="type.key">{{ type.value }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          v-if="pharmacist.sealCertNumber"
          :gutter="20"
        >
          <el-col :span="12">
            <el-form-item
              label="CA签名凭证:"
              prop="sealCertNumber"
            >
              <el-input
                v-model="pharmacist.sealCertNumber"
                type="textarea"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="CA签名图片:"
            >
              <el-image
                style="width:100px;height:100px"
                fit="fill"
                :src="pharmacist.sealImage"
              ></el-image>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item
              label="执业药师资格证"
              style="margin-bottom:10px;"
              prop="fileLicense"
            >
              <div
                class="box"
                style="float:left;margin-right:10px;"
              >
                <input
                  id="boxtx61"
                  ref="boxtx61"
                  name="boxtx61"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg, image/jpg"
                  @change="handleCertificateChange($event)"
                />
                <label for="boxtx61"></label>
                <div
                  v-if="pharmacist.licenseUrl"
                  class="img"
                >
                  <div>
                    <!-- <img
                      :src="pharmacist.licenseUrl"
                      style="width: 100%;"
                    /> -->
                    <el-image
                      ref="previewImg"
                      :src="pharmacist.licenseUrl"
                      :preview-src-list="[pharmacist.licenseUrl]"
                    >
                    </el-image>
                  </div>
                </div>
                <i
                  v-else
                  class="el-icon-circle-plus-outline"
                ></i>
                <div
                  v-if="pharmacist.licenseUrl"
                  class="downloadBox"
                >
                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.qualificationCertificate.urls[0],3)"> -->
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/download.png"
                    alt=""
                    @click.stop="handleDownload(pharmacist.licenseUrl)"
                  >
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/enlarge.png"
                    alt=""
                    @click.stop="previewPic('previewImg')"
                  >
                </div>

              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item
              label="身份证正面照片"
              style="margin-bottom:10px;"
              prop="fileFrontUrl"
            >
              <div
                class="box"
                style="float:left;margin-right:10px;"
              >
                <input
                  id="boxtx8"
                  ref="boxtx8"
                  name="boxtx8"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg, image/jpg"
                  @change="handleCertiDcardChange($event,1)"
                />
                <label for="boxtx8"></label>
                <div
                  v-if="pharmacist.frontUrl"
                  class="img"
                >
                  <div>
                    <!-- <img
                      :src="pharmacist.frontUrl"
                      style="width: 100%;"
                    /> -->
                    <el-image
                      ref="previewImgFront"
                      :src="pharmacist.frontUrl"
                      :preview-src-list="[pharmacist.frontUrl]"
                    >
                    </el-image>
                  </div>
                </div>
                <i
                  v-else
                  class="el-icon-circle-plus-outline"
                ></i>
                <div
                  v-if="pharmacist.frontUrl"
                  class="downloadBox"
                >
                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.qualificationCertificate.urls[0],3)"> -->
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/download.png"
                    alt=""
                    @click.stop="handleDownload(pharmacist.frontUrl)"
                  >
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/enlarge.png"
                    alt=""
                    @click.stop="previewPic('previewImgFront')"
                  >
                </div>

              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="身份证反面照片"
              style="margin-bottom:10px;"
              prop="fileBackUrl"
            >
              <div
                class="box"
                style="float:left;margin-right:10px;"
              >
                <input
                  id="boxtx9"
                  ref="boxtx9"
                  name="boxtx9"
                  class="imgfile"
                  type="file"
                  multiple="false"
                  accept="image/png, image/gif, image/jpeg, image/jpg"
                  @change="handleCertiDcardChange($event,2)"
                />
                <label for="boxtx9"></label>
                <div
                  v-if="pharmacist.backUrl"
                  class="img"
                >
                  <div>
                    <!-- <img
                      :src="pharmacist.backUrl"
                      style="width: 100%;"
                    /> -->
                    <el-image
                      ref="previewImgBack"
                      :src="pharmacist.backUrl"
                      :preview-src-list="[pharmacist.backUrl]"
                    >
                    </el-image>
                  </div>
                </div>
                <i
                  v-else
                  class="el-icon-circle-plus-outline"
                ></i>
                <div
                  v-if="pharmacist.backUrl"
                  class="downloadBox"
                >
                  <!-- <img style="width:30px;height:30px" src="/static/images/ocr.png" alt="" @click.stop="handleOCR(doctorData.qualificationCertificate.urls[0],3)"> -->
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/download.png"
                    alt=""
                    @click.stop="handleDownload(pharmacist.backUrl)"
                  >
                  <img
                    style="width:30px;height:30px"
                    src="/static/images/enlarge.png"
                    alt=""
                    @click.stop="previewPic('previewImgBack')"
                  >
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col
            :span="24"
            style="text-align: right;"
          >
            <el-button
              v-waves
              v-permission="['user:pharmacist:edit']"
              type="primary"
              @click="submit('dataForm')"
            >提交
            </el-button>
            <el-button
              v-waves
              type="primary"
              @click="close"
            >返回</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-dialog
        :visible.sync="PreviewVisible"
        append-to-body
      >
        <img
          width="100%"
          :src="PreviewImageUrl"
          alt=""
        />
      </el-dialog>
    </el-dialog></div>
</template>

<script>
import {
  getList,
  getDetail,
  uploadCertificate,
  updatePharmacis,
  addPharmacis,
  nationList,
  uploadCertiDcard,
  changeDoctorStatus
} from '@/api/user/pharmacist'
import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'
import { Message } from 'element-ui'
import { Loading } from 'element-ui'
import { isImage } from '@/utils'

export default {
  name: 'Userpharmacist',
  directives: { waves },
  filters: {},
  components: {
    DictSelect
  },
  data() {
    return {
      dialogTableVisible: false,
      dialogPharmacistTitle: '药师详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        name: '',
        phone: '',
        licenseNo: '',
        cardNo: '',
        titleId: ''
      },
      textMap: {
        update: '更新',
        setting: '设置门店'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      pharmacist: {
        id: null,
        name: '',
        phone: '',
        sex: '',
        birth: '',
        cardNo: '',
        ethnicity: '',
        licenseDate: '',
        skill: '',
        licenseNo: '',
        licenseUrl: '',
        titleId: '',
        authStatus: null,
        drugType: [],
        reviewType: []
      },

      nationList: [],
      statusArr: ['未注册', '已注册'],
      authStatusArr: ['待认证', '通过', '不通过'],
      drugTypes: [{ key: 1, value: '西药药师' }, { key: 2, value: '中药药师' }],
      reviewTypes: [{ key: 1, value: '西药处方' }, { key: 2, value: '中药处方' }],
      rules: {
        name: [{ required: true, message: '请输入药师姓名', trigger: 'blur' }],
        phone: [
          { required: true, trigger: 'blur',
            validator: (rule, value, callback) => {
              var myreg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
              if (!value || !myreg.test(value)) {
                callback(new Error('请输入正确的手机号'))
              } else {
                callback()
              }
            } }
        ],
        licenseNo: [
          { required: true, message: '请输入药师资格证编号', trigger: 'blur' }
        ],
        ethnicity: [{ required: true, message: '请选择民族', trigger: 'blur' }],
        skill: [{ required: true, message: '请输入专业特长', trigger: 'blur' }],
        titleId: [{ required: true, message: '请选择职称', trigger: 'blur' }],
        licenseDate: [
          { required: true, message: '请选择批准日期', trigger: 'blur' }
        ],
        cardNo: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!value || (value.length !== 15 && value.length !== 18)) {
                callback(new Error('请输入正确的身份证号'))
              } else {
                callback()
              }
            }
          }
        ],
        fileLicense: [
          { required: true,
            validator: (rule, value, callback) => {
              if (!this.pharmacist.licenseUrl) {
                callback(new Error('请上传执业药师资格证书'))
              } else {
                callback()
              }
            }
          }
        ],
        fileFrontUrl: [
          { required: true,
            validator: (rule, value, callback) => {
              if (!this.pharmacist.frontUrl) {
                callback(new Error('请上传身份证正面照片'))
              } else {
                callback()
              }
            }
          }
        ],
        fileBackUrl: [
          { required: true,
            validator: (rule, value, callback) => {
              if (!this.pharmacist.backUrl) {
                callback(new Error('请上传身份证反面照片'))
              } else {
                callback()
              }
            }
          }
        ],
        drugType: [{ required: true, message: '请选择执业药师类型', trigger: 'blur' }],
        reviewType: [{ required: true, message: '请选择审方类型', trigger: 'blur' }]
      },
      PreviewVisible: false,
      PreviewImageUrl: ''
    }
  },
  /* 监听table数据对象 */
  watch: {
    list(val) {
      this.doLayout()
    }
  },
  created() {
    this.getNationList()
    // this.handleFilter()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.phone = ''
      this.listQuery.licenseNo = ''
      this.listQuery.cardNo = ''
      this.listQuery.titleId = ''
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        const files = document.querySelectorAll('.imgfile')
        if (files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            files[i].value = null
          }
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    addPharmacist() {
      this.pharmacist.id = null
      this.dialogPharmacistTitle = '添加药师'
      this.dialogTableVisible = true
      this.resetTemp()
      this.pharmacist = {}
      this.$set(this.pharmacist, 'drugType', [])
      this.$set(this.pharmacist, 'reviewType', [])
    },
    pharmacistInfo(id) {
      this.pharmacist.id = id
      getDetail(this.pharmacist.id).then((res) => {
        this.pharmacist = res
        this.parseCardId()
        this.dialogPharmacistTitle = '药师详情'
        this.dialogTableVisible = true
        this.resetTemp()
      })
    },
    parseCardId() {
      const idCard = this.pharmacist.cardNo
      if (
        idCard != null &&
        idCard !== '' &&
        (idCard.length === 15 || idCard.length === 18)
      ) {
        let birthday = ''
        if (idCard.length === 15) {
          birthday = '19' + idCard.slice(6, 12)
        } else if (idCard.length === 18) {
          birthday = idCard.slice(6, 14)
        }
        //通过正则表达式来指定输出格式为:1990-01-01
        this.pharmacist.birth = birthday.replace(/(.{4})(.{2})/, '$1-$2-')
        if (parseInt(idCard.slice(-2, -1)) % 2 === 1) {
          this.pharmacist.sex = '男'
        } else {
          this.pharmacist.sex = '女'
        }
        this.$forceUpdate()
      }
    },
    handleCertificateChange(event) {
      if (!isImage(event.target.files[0].type)) {
        this.$refs.boxtx7.value = ''
        this.$message.error('不接收此文件类型！')
        return false
      }
      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      uploadCertificate(param).then(
        (response) => {
          console.log(response)
          this.pharmacist.licenseUrl = response
          this.$forceUpdate()
        },
        (error) => {}
      )
    },
    handleCertiDcardChange(event, type) {
      if (!isImage(event.target.files[0].type)) {
        if (type === 1) {
          this.$refs.boxtx8.value = ''
        } else if (type === 2) {
          this.$refs.boxtx9.value = ''
        }
        this.$message.error('不接收此文件类型！')
        return false
      }
      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      uploadCertiDcard(param).then(
        (response) => {
          console.log('handleCertiDcardChange', type, response)
          if (type === 1) {
            this.pharmacist.frontUrl = response
          } else if (type === 2) {
            this.pharmacist.backUrl = response
          }
          this.$forceUpdate()
        },
        (error) => {}
      )
    },
    submit(dataForm) {
      const that = this
      console.log(that.$refs[dataForm])
      that.$refs[dataForm].validate((valid) => {
        console.log('valid', valid)
        if (valid) {
          this.loading = true
          const params = that.pharmacist
          delete params.sex
          delete params.birth
          if (!that.pharmacist.licenseUrl) {
            Message({
              message: '请选择执业药师资格证书图片',
              type: 'error',
              duration: 5 * 1000
            })
          } else if (that.pharmacist.id) {
            const loadingInstance = Loading.service({ fullscreen: true })
            updatePharmacis(params).then((res) => {
              this.loading = false
              Message({
                message: '修改成功',
                type: 'success',
                duration: 5 * 1000
              })
              that.getList()
              that.dialogTableVisible = false
            }) .finally(function() {
              loadingInstance.close()
            })
          } else {
            const loadingInstance = Loading.service({ fullscreen: true })
            addPharmacis(params).then((res) => {
              this.loading = false
              Message({
                message: '添加成功',
                type: 'success',
                duration: 5 * 1000
              })
              that.getList()
              that.dialogTableVisible = false
            }) .finally(function() {
              loadingInstance.close()
            })
          }
        }
      })
    },
    // 停用-开启药师 1停用 0开启
    changeStatus(id, status) {
      if (status === 0) {
        this.$confirm('是否启用药师？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      } else {
        this.$confirm('是否停用药师？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      }
    },
    // 修改药师状态
    sureStatus(id, status) {
      changeDoctorStatus({
        pharmacistId: id,
        status: status
      }).then((res) => {
        Message({
          message: '修改成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    close() {
      const that = this
      that.dialogTableVisible = false
    },
    record(pharmacistId) {
      this.$router.push({
        path: './recordPh/' + pharmacistId
      })
    },
    getNationList() {
      nationList().then((res) => {
        this.nationList = res
      })
    },
    // 下载图片
    handleDownload(file) {
      console.log(file)
      this.downloadIamge(file, new Date().getTime())
    },
    downloadIamge(imgsrc, name) {
      //下载图片地址和图片名
      var image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function() {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var url = canvas.toDataURL('image/png') //得到图片的base64编码数据
        var a = document.createElement('a') // 生成一个a元素
        var event = new MouseEvent('click') // 创建一个单击事件
        a.download = name || 'photo' // 设置图片名称
        a.href = url // 将生成的URL设置为a.href属性
        a.dispatchEvent(event) // 触发a的单击事件
      }
      image.src = imgsrc
    },
    // 预览图片
    handlePreview(url) {
      this.PreviewImageUrl = url
      this.PreviewVisible = true
    },
    // 预览
    previewPic(imgRef) {
      this.$refs[imgRef].showViewer = true
    },
    /* 重新渲染table组件 */
    doLayout() {
      const that = this
      this.$nextTick(() => {
        that.$refs.table.doLayout()
      })
    },
    drugTypeChage(value, type) {
      if (value) {
        if (!this.pharmacist.reviewType.includes(type)) {
          this.pharmacist.reviewType.push(type)
        }
      }
      console.log(value, type, this.pharmacist.reviewType, '===drugTypeChage======')
    }
  }
}
</script>
<style scoped>
.btn {
  text-align: right;
  padding: 10px 50px;
}
.mboxinput {
  margin-bottom: 10px;
}
.imgfile {
  font-size: 0; /* 为了去掉‘未选择任何文件’这几个字，也可以随便弄到哪里*/
  position: absolute;
  left: -9999px;
}
/* 注意不是直接input > input[type=button] 哦*/
.imgfile::-webkit-file-upload-button {
  background: #efeeee;
  color: #333;
  border: 0;
  padding: 40px 100px;
  border-radius: 5px;
  font-size: 12px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 0, 0, 0.12);
}
.box {
  position: relative;
  margin-bottom: 10px;
  width: 180px;
  height: 180px;
  font-size: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ccc;
}
/* 使label充满整个box*/
.box label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; /* 这个z-index之后说到*/
}
.imgbox .img {
  position: relative;
  height: 120px;
  width: 120px;
  display: table;
  text-align: center;
}
.box .img div {
  display: table-cell;
  vertical-align: middle;
}
.box .img img {
  max-width: 118px;
  max-height: 118px;
}
.box .img .el-image {
  max-width: 118px;
  max-height: 118px;
}
.box .img i {
  font-size: 22px;
  position: absolute;
  left: 0;
  top: 0;
}
.downloadBox {
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 999999;
}
</style>
