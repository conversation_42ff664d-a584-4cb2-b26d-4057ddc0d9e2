<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :inline="true"
      label-width="150px"
      class="demo-form-inline"
    >
      <div class="title">诊断</div>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="姓名:"
            label-width="100px"
            prop="patientName"
            style="display:block;"
          >
            <div>{{ data.patientName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="性别:"
            label-width="100px"
            prop="patientGender"
            style="display:block;"
          >
            <div>{{ data['patientGender'] === 1? '男' : '女' }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="年龄:"
            label-width="100px"
            prop="patientAge"
            style="display:block;"
          >
            <div>{{ data['patientAge'] }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label="辨病:"
            label-width="100px"
            prop="disease"
            style="display:block;"
          >
            <div>{{ data['disease'] }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label="辨证:"
            label-width="100px"
            prop="syndrome"
            style="display:block;"
          >
            <div>{{ data['syndrome'] }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="title">处方</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label="Rp:"
            label-width="100px"
            prop="tcms"
            style="display:block;"
          >
            <div>共 <span class="red">{{ (data['tcms'] && data['tcms'].length) || 0 }}</span>味药</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label=" "
            label-width="100px"
            style="display:block;"
          >
            <span
              v-for="item in data['tcms']"
              :key="item.tcmOId"
              :span="6"
            >{{ item.tcmOName }} {{ item.quantity }}{{ item.quantityUnit }} <span v-if="item.remark">({{ item.remark }})</span>；
            </span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label=" "
            label-width="100px"
            style="display:block;"
          >
            <div v-if="data.dosageForm==1">共 <span class="red">{{ data.dose }}</span> 剂，每日 <span class="red">{{ data.dailyDose }}</span>次， 1次 <span class="red">{{ data.doseUseNumber }}</span> {{ data.tcmDosageCycleUnit==1?'克':'毫升' }}</div>
            <div v-else>共 <span class="red">{{ data.dose }}</span> 剂，每日 <span class="red">{{ data.dailyDose }}</span>剂，1剂分 <span class="red">{{ data.doseUseNumber }}</span> 次服用煎服</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="title">处方文件</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :key="tableKey" :data="data.files" fit highlight-current-row>
            <el-table-column label="处方单图片名称" prop="name" width="130" align="center" />
            <el-table-column label="处方单地址" prop="url" align="center" />
            <el-table-column label="操作" fixed="right" align="center" width="80">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="viewFile(row.url)">
                  <span>查看</span>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <!-- <template v-if="data.audit&&data.audit.status === 0">
        <div class="title">审核处方</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="审核人：" label-width="100px">
              <div>{{ data.pharmacistName }}</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="审核意见：" label-width="100px">
              <el-select v-model="auditStatus">
                <el-option
                  v-for="item in auditType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处方点评：" label-width="100px">
              <el-input
                v-model="remark"
                type="textarea"
                clearable
                placeholder="请输入内容"
                style="width: 300px"
                maxlength="50"
                minlength="1"
                @keyup.enter.native="handleFilter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-button
              v-if="data.invalid === 1"
              v-permission="['medication:recom:audit']"
              :loading="sub_loading"
              type="primary"
              disabled
              @click="submitAudit"
            >提交审核</el-button>
            <el-button
              v-else
              v-permission="['medication:recom:audit']"
              :loading="sub_loading"
              type="primary"
              @click="submitAudit"
            >提交审核</el-button>
            <span v-if="data.invalid === 1" style="color:#f00;">处方已超时效，不可审核</span>
          </el-col>
        </el-row>
      </template> -->
      <div class="title">操作日志</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :key="tableKey" :data="audits" fit highlight-current-row>
            <el-table-column label="操作人" prop="createdBy" width="100px" align="center" />
            <el-table-column label="操作时间" prop="createdAt" width="140px" align="center" />
            <el-table-column label="操作内容" prop="remark" align="center" />
          </el-table>
        </el-col>
      </el-row>
      <pagination
        v-show="auditsTotal>0"
        :total="auditsTotal"
        :page.sync="auditsQuery.pageNo"
        :limit.sync="auditsQuery.pageSize"
        @pagination="getAuditLogs"
      />
    </el-form>
    <el-dialog title="请输入验证码" :visible.sync="passdialogFormVisible" width="50%">
      <el-form ref="passdataForm" :model="verInfo" :rules="rules" label-position="left" label-width="auto">
        <el-form-item label="验证码" prop="verpass">
          <el-input
            v-model="verInfo.verpass"
            class="brandinput"
            placeholder="请输入验证码"
          />
        </el-form-item>
        <el-button type="primary" :disabled="verInfoDialog.disabled" @click="getCode">{{ verInfoDialog.msg }}</el-button>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align:right;">
        <el-button type="primary" :loading="loading" @click="surePass('passdataForm')">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="dialog.visible" width="30%" :before-close="handleClose">
      <span>{{ dialog.msg }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog.visible = false">取 消</el-button>
        <el-button v-if="dialog.ok" type="primary" @click="dialog.ok">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="查看图片"
      :visible.sync="image.isOpen"
      width="70%"
      :before-close="handleClose"
      class="img_view"
    >
      <el-image :src="image.src" fit="cover"></el-image>
    </el-dialog>
  </div>
</template>
<style scoped>
  .red{
    color: #f00;
  }
  .title {
    margin:20px auto;
    text-align: center;
    color: #707070;
  }
  .tips {
    display: inline-block;
    margin-left: 10px;
  }
  .el-dialog__footer {
    padding-right: 20px;
  }
  .img_view {
    text-align: center;
  }
</style>
<script>
import { getDetail } from '../../api/cnRecommend/index'
import { getAuditLogs, getCode, audit, veridentity } from '../../api/recommend/audit'
import { Message } from 'element-ui'
export default {
  name: '',
  filters: {},
  components: {},
  data() {
    return {
      tableKey: 0,
      auditLevel: null,
      passdialogFormVisible: false,
      verInfo: {
        verpass: null
      },
      verInfoDialog: {
        msg: '获取验证码',
        disabled: false,
        isSendCode: false
      },
      rules: {
        verpass: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      data: {
        audit: {}
      },
      auditType: [
        { value: 1, label: '审核通过' },
        { value: 2, label: '审核不通过' }
      ],
      audit_type: ['待审核', '通过', '不通过'],
      genders: ['女', '男'],
      auditStatus: 1,
      recomId: 0,
      remark: '',
      dialog: {
        msg: '审核不通过原因必填，字数限制1-50字',
        visible: false,
        ok: false
      },
      image: {
        isOpen: false,
        src: ''
      },
      loading: false,
      sub_loading: false,
      audits: [],
      auditsQuery: {
        pageNo: 1,
        pageSize: 10
      },
      auditsTotal: 0
    }
  },
  created() {
    const params = {}
    if (this.$route.params.id) {
      params.recomId = this.$route.params.id
      this.recomId = params.recomId
      this.getAuditDetail(params)
    }
  },
  methods: {
    getAuditDetail(params) {
      const that = this
      getDetail(params).then(function(data) {
        if (data) {
          that.data = data
          that.getAuditLogs()
        }
      })
    },
    checkAudit() {
      veridentity().then(response => {
        this.auditLevel = response.auditLevel
      })
    },
    submitAudit() {
      const that = this
      if (that.auditStatus === 2) {
        if (that.remark.length > 50 || that.remark.length < 1) {
          that.dialog.msg = '审核不通过原因必填，字数限制1-50字'
          that.dialog.ok = ''
          that.dialog.visible = true
          return false
        } else {
          const params = {}
          params.status = that.auditStatus
          params.remark = that.remark
          params.recomId = that.recomId
          that.sub_loading = true
          audit(params, function(res) {
            res = JSON.parse(res)
            if (res.code === 0) {
              res.data = null
            }
            return res
          }).then(function(data) {
            that.sub_loading = false
            if (data === null) {
              Message({
                message: '操作成功',
                type: 'success',
                duration: 5 * 1000
              })
              that.getAuditDetail({ recomId: that.recomId })
            }
          }).finally(function(r) {
            that.sub_loading = false
          })
        }
      } else {
        that.passdialogFormVisible = true
      }
    },
    surePass(dataForm) {
      const that = this
      if (!that.verInfoDialog.isSendCode) {
        Message({
          message: '请先发送验证码',
          type: 'error',
          duration: 5 * 1000
        })
        return false
      }
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          const params = {}
          params.status = that.auditStatus
          params.remark = that.remark
          params.recomId = that.recomId
          params.signPwd = that.verInfo.verpass
          params.force = 0
          that.loading = true
          audit(params, function(res) {
            that.loading = false
            res = JSON.parse(res)
            // if (res.code === 13050302) {
            //   res.code = 0
            //   res.data = false
            //   that.dialog.msg = '药师电子签名异常，是否确定继续？'
            //   that.dialog.ok = that.dialogOk
            //   that.dialog.visible = true
            // } else if (res.code === 0) {
            //   res.data = null
            // }
            if (res.code === 0) {
              res.data = null
            }
            return res
          }).then(function(data) {
            if (data === null) {
              Message({
                message: '操作成功',
                type: 'success',
                duration: 5 * 1000
              })
              that.getAuditDetail({ recomId: that.recomId })
            }
          }).finally(function() {
            that.loading = false
            that.verInfo.verpass = ''
          })
          this.passdialogFormVisible = false
        }
      })
    },
    statusFor(row, column, cellValue, index) {
      return this.audit_type[cellValue]
    },
    dialogOk() {
      const that = this
      that.dialog.visible = false
      const params = {}
      params.status = that.auditStatus
      params.remark = that.remark
      params.recomId = that.recomId
      params.force = 1
      that.loading = true
      audit(params).then(function(data) {
        that.loading = false
        if (data === null) {
          Message({
            message: '操作成功',
            type: 'success',
            duration: 5 * 1000
          })
          that.getAuditDetail({ recomId: that.recomId })
        }
      })
    },
    viewFile(src) {
    // const that = this
      // if (src.toLocaleUpperCase().indexOf('.PDF') === -1) {
      //   that.image.src = src
      //   that.image.isOpen = true
      // } else {
      window.open(src)
      // }
    },
    handleClose() {
    },
    getAuditLogs() {
      const that = this
      const params = {}
      params.recomId = that.recomId
      params.pageNo = that.auditsQuery.pageNo
      params.pageSize = that.auditsQuery.pageSize
      getAuditLogs(params).then(res => {
        that.audits = res.list
        that.auditsTotal = res.totalCount
      })
    },
    getCode() {
      const that = this
      const params = {}
      params.recomId = that.recomId
      getCode(params).then(res => {
        that.verInfoDialog.isSendCode = true
        that.verInfoDialog.disabled = true
        that.verInfoDialog.msg = '60s'
        let time = 60
        const intval = window.setInterval(() => {
          time--
          that.verInfoDialog.msg = time + 's'
          if (time === 0) {
            that.verInfoDialog.disabled = false
            that.verInfoDialog.msg = '获取验证码'
            window.clearInterval(intval)
          }
        }, 1000)
      })

    }
  }
}
</script>
