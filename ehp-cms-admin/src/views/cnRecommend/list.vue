<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.serialNumber"
        placeholder="处方编号"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.patientName"
        placeholder="患者姓名"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.patientPhone"
        placeholder="患者手机号"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.doctorName"
        placeholder="医生姓名"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.doctorPhone"
        placeholder="医生手机号"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-permission="['tcm:particles:list']" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-permission="['tcm:particles:list']" v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="100px" />
      <el-table-column label="处方编号" prop="serialNumber" width="150px" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="$router.push('/cnRecommend/info/'+scope.row.recomId)"
          >{{ scope.row.serialNumber }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="患者姓名" prop="patientName" align="center" width="100px" />
      <el-table-column label="医生姓名" prop="doctorName" align="center" width="100px" />
      <el-table-column label="辨病" prop="disease" width="150px" align="center" />
      <!-- <el-table-column label="辩证" prop="syndrome" width="150px" align="center" /> -->
      <el-table-column label="制药方式" prop="drugType" width="100px" align="center" :formatter="statusFor" />
      <el-table-column label="处方状态" prop="status" width="100px" align="center" :formatter="statusFor" />
      <el-table-column label="审核状态" prop="auditStatus" width="100px" align="center" :formatter="statusFor" />
      <el-table-column label="处方费用(元)" prop="price" width="100px" align="center" :formatter="formatPrice" />
      <el-table-column label="开方时间" prop="createdAt" width="150px" align="center" />
      <el-table-column label="操作" fixed="right" align="center">
        <template slot-scope="{row}">
          <el-button
            v-if="row.status === 0"
            v-waves
            v-permission="['tcm:particles:details']"
            type="primary"
            size="mini"
            @click="handleAudit(row.recomId,row.status)"
          >
            <span>查看</span>
          </el-button>
          <el-button
            v-else
            v-waves
            v-permission="['tcm:particles:details']"
            type="primary"
            size="mini"
            @click="seeAudit(row.recomId,row.status)"
          >
            <span>查看</span>
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog title="选择药师" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="verInfo" :rules="rules">
        <el-form-item label="药师" label-width="38%" prop="cistId">
          <el-select v-model="verInfo.cistId" placeholder="请选择" @change="cistChange">
            <el-option v-for="item in verList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="身份证号" label-width="38%" prop="idCard">
          <el-input
            v-model="verInfo.idCard"
            class="brandinput"
            placeholder="请输入身份证号"
            style="width:170px"
          />
        </el-form-item>
        <div v-if="showPhone">
          <el-form-item label="手机号" label-width="38%" prop="phone">
            <el-input
              v-model="phone"
              class="brandinput"
              style="width:170px"
              readonly
            />
            <el-button type="primary" :disabled="verInfoDialog.disabled" @click="getCode">{{ verInfoDialog.msg }}</el-button>
          </el-form-item>
          <el-form-item label="验证码" label-width="38%" prop="verifyCode">
            <el-input
              v-model="verInfo.verifyCode"
              class="brandinput"
              placeholder="请输入验证码"
              style="width:170px"
            />
          </el-form-item>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align:center;">
        <el-button type="primary" @click="verAudit('dataForm')">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped="">
.dis_col {
  display: flex;
  align-items: center;
}
.red{
  color: red;
}
</style>
<script>
import waves from '../../directive/waves'
import { list } from '../../api/cnRecommend'
import { veridentity, getAuthCode, setidentity } from '../../api/recommend/audit'
import { Message } from 'element-ui'
export default {
  name: '',
  directives: { waves },
  filters: {},
  components: {
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      verList: [],
      audit_type: ['待审核', '通过', '不通过'],
      order_type: ['', '未购', '已购', '过期'],
      drug_type: ['', '西药', '中药'],
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        serialNumber: '',
        patientName: '',
        patientPhone: '',
        doctorName: '',
        doctorPhone: '',
        drugType: 2
      },
      dialogFormVisible: false,
      verInfo: {
        cistId: null,
        idCard: null,
        verifyCode: null
      },
      showPhone: false,
      phone: null,
      verInfoDialog: {
        msg: '获取验证码',
        disabled: false,
        isSendCode: false
      },
      rules: {
        cistId: [{ required: true, message: '请选择药师', trigger: 'change' }],
        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
    // this.checkAudit()
  },
  methods: {
    // 获取数据
    getList() {
      list(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.serialNumber = ''
      this.listQuery.patientName = ''
      this.listQuery.patientPhone = ''
      this.listQuery.doctorName = ''
      this.listQuery.doctorPhone = ''
      this.handleFilter()
    },
    statusFor(row, { property }, cellValue, index) {
      switch (property) {
        case 'status':
          return this.order_type[cellValue]
        case 'auditStatus':
          return this.audit_type[cellValue]
        case 'drugType':
          return this.drug_type[cellValue]
        default:
          break
      }
    },
    formatPrice(row, column, cellValue) {
      if (cellValue) return cellValue / 100
    },
    //检查身份状态 如果没有审核 跳出审核弹窗填写身份信息
    checkAudit() {
      veridentity().then(response => {
        this.verList = response.list
        if (response.status === 1) {
          this.dialogFormVisible = true
        } else {
          this.auditLevel = response.auditLevel
          this.getList()
        }
      })
    },
    handleAudit(id, status) {
      if (status !== 0) {
        this.$router.push('/cnRecommend/info/' + id)
        return false
      }
      veridentity().then(response => {
        this.recomId = id
        this.verList = response.list
        if (response.status === 1) {
          this.dialogFormVisible = true
        } else {
          this.$router.push('/cnRecommend/info/' + id)
        }
      })
    },
    seeAudit(id) {
      this.$router.push('/cnRecommend/info/' + id)
    },
    cistChange(value) {
      console.log(value)
      for (let i = 0; i < this.verList.length; i++) {
        if (this.verList[i].id === value) {
          if (this.verList[i].authStatus === 1) {
            this.showPhone = false
          } else {
            this.showPhone = true
            this.phone = this.verList[i].phone
          }
          break
        }
      }
    },
    getCode() {
      const that = this
      const params = {}
      if (!(that.verInfo.cistId && that.verInfo.cistId !== '')) {
        Message({
          message: '请选择药师',
          type: 'error',
          duration: 5 * 1000
        })
        return
      }
      params.pharmacistId = that.verInfo.cistId
      getAuthCode(params).then(res => {
        that.verInfoDialog.isSendCode = true
        that.verInfoDialog.disabled = true
        that.verInfoDialog.msg = '60s'
        let time = 60
        const intval = window.setInterval(() => {
          time--
          that.verInfoDialog.msg = time + 's'
          if (time === 0) {
            that.verInfoDialog.disabled = false
            that.verInfoDialog.msg = '获取验证码'
            window.clearInterval(intval)
          }
        }, 1000)
      })
    },
    //验证身份信息
    verAudit(dataForm) {
      const that = this
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          if (that.showPhone && !that.verInfoDialog.isSendCode) {
            Message({
              message: '请先发送验证码',
              type: 'error',
              duration: 5 * 1000
            })
          } else if (that.showPhone && !(that.verInfo.verifyCode && that.verInfo.verifyCode !== '')) {
            Message({
              message: '请输入验证码',
              type: 'error',
              duration: 5 * 1000
            })
          } else {
            // 验证成功之后刷新列表
            setidentity(this.verInfo).then(response => {
              this.dialogFormVisible = false
              this.getList()
            })
          }

        }
      })
    }
  }
}
</script>
