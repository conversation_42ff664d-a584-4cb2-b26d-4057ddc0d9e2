<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.userId"
        clearable
        class="filter-item"
        placeholder="用户ID"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        class="filter-item"
        placeholder="手机号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        clearable
        class="filter-item"
        placeholder="订单号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.orderStatus"
        placeholder="请选择"
        class="filter-item"
        style="width: 150px"
        type="return_order_status"
        @change="handleFilter"
      />
      <DatePicker
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row style="width: 100%">
      <el-table-column label="订单号" fixed prop="orderSn" width="180px" align="center" />
      <el-table-column label="退货单号" prop="returnOrderSn" width="180px" align="center" />
      <el-table-column label="用户ID" prop="userId" width="80px" align="center" />
      <el-table-column label="用户名" prop="userName" width="150px" align="center" />
      <el-table-column label="订单来源" prop="orderSourceDescribe" width="110px" align="center" />
      <el-table-column label="订单状态" prop="statusDescribe" width="80px" align="center" />
      <el-table-column label="申请时间" prop="createdAt" width="140px" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="100px">
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['oms:return:order:details']"
            type="primary"
            size="mini"
            @click="handleUpdate(row)"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="80%">
      <el-form
        ref="dataForm"
        :inline="true"
        :model="orderDetails"
        label-position="right"
        label-width="100px"
      >
        <el-tabs style="min-height: 400px;" type="card">
          <el-tab-pane label="订单信息">
            <el-form-item label="用户:" prop="userId">
              <el-input v-model="orderDetails.userId" readonly />
            </el-form-item>
            <el-form-item label="订单号:" prop="orderSn">
              <el-input v-model="orderDetails.orderSn" readonly />
            </el-form-item>
            <el-form-item label="物流单号:" prop="deliveryId">
              <el-input v-model="orderDetails.deliveryId" readonly />
            </el-form-item>
            <el-form-item label="仓库ID:" prop="warehouseId">
              <el-input v-model="orderDetails.warehouseId" readonly />
            </el-form-item>
            <el-form-item label="订单时间:" prop="createdAt">
              <el-input v-model="orderDetails.createdAt" readonly />
            </el-form-item>
            <el-form-item label="捡货人:" prop="pickupMan">
              <el-input v-model="orderDetails.pickupMan" readonly />
            </el-form-item>
            <el-form-item label="订单状态:" prop="orderStatusDescribe">
              <el-input v-model="orderDetails.orderStatusDescribe" readonly />
            </el-form-item>
            <el-form-item label="订单备注:" prop="remark">
              <el-input v-model="orderDetails.remark" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="退货信息">
            <el-form-item label="退货单号:" prop="returnOrderSn">
              <el-input v-model="returnGoodsOrder.returnOrderSn" readonly />
            </el-form-item>
            <el-form-item label="物流单号:" prop="deliveryId">
              <el-input v-model="returnGoodsOrder.deliveryId" readonly />
            </el-form-item>
            <el-form-item label="审核状态:" prop="statusDescribe">
              <el-input v-model="returnGoodsOrder.statusDescribe" readonly />
            </el-form-item>
            <el-form-item label="退货来源:" prop="orderSourceDescribe">
              <el-input v-model="returnGoodsOrder.orderSourceDescribe" readonly />
            </el-form-item>
            <el-form-item label="客服备注:" prop="remark1">
              <el-input v-model="returnGoodsOrder.remark1" readonly />
            </el-form-item>
            <el-form-item label="仓库备注:" prop="remark2">
              <el-input v-model="returnGoodsOrder.remark2" readonly />
            </el-form-item>
            <el-form-item label="退货人:" prop="createdBy">
              <el-input v-model="returnGoodsOrder.createdBy" readonly />
            </el-form-item>
            <el-form-item label="申请时间:" prop="createdAt">
              <el-input v-model="returnGoodsOrder.createdAt" readonly />
            </el-form-item>
            <el-table :data="auditLog" fit highlight-current-row style="width: 100%">
              <el-table-column align="center" label="操作人" prop="createdBy" />
              <el-table-column align="center" label="备注" prop="remark" />
              <el-table-column align="center" label="操作时间" prop="createdAt" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane v-if="returnGoodsOrder.status != 0" label="退款信息">
            <el-form-item label="退款金额:" prop="refundAmount">
              <el-input v-model="refund.refundAmount" readonly />
            </el-form-item>
            <el-form-item label="审核状态:" prop="statusDescribe">
              <el-input v-model="refund.statusDescribe" readonly />
            </el-form-item>
            <el-form-item label="申请人:" prop="createdBy">
              <el-input v-model="refund.createdBy" readonly />
            </el-form-item>
            <el-form-item label="申请时间:" prop="createdAt">
              <el-input v-model="refund.createdAt" readonly />
            </el-form-item>
            <el-form-item label="备注信息:" prop="remark">
              <el-input v-model="refund.remark" readonly />
            </el-form-item>
            <el-table :data="refundLog" fit highlight-current-row style="width: 100%">
              <el-table-column align="center" label="操作人" prop="createdBy" />
              <el-table-column align="center" label="备注" prop="remark" />
              <el-table-column align="center" label="操作时间" prop="createdAt" />
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="支付信息">
            <el-form-item label="支付状态:" prop="payStatusDescribe">
              <el-input v-model="orderDetails.payStatusDescribe" readonly />
            </el-form-item>
            <el-form-item label="支付时间:" prop="payTime">
              <el-input v-model="orderDetails.payTime" readonly />
            </el-form-item>
            <el-form-item label="订单金额:" prop="realPay">
              <el-input v-model="orderDetails.realPay" readonly />
            </el-form-item>
            <el-form-item label="商品总价:" prop="totalAmount">
              <el-input v-model="orderDetails.totalAmount" readonly />
            </el-form-item>
            <el-form-item label="邮费:" prop="freight">
              <el-input v-model="orderDetails.freight" readonly />
            </el-form-item>
            <el-form-item label="优惠券:" prop="freight">
              <el-input v-model="orderDetails.couponPay" readonly />
            </el-form-item>
            <el-form-item label="三方流水号:" prop="freight">
              <el-input v-model="orderDetails.channelTradeSn" readonly />
            </el-form-item>
            <el-form-item label="支付方式:" prop="freight">
              <el-input value="在线支付" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="收货信息">
            <el-form-item label="收货人:" prop="receiver">
              <el-input v-model="orderDetails.orderUser.receiver" readonly />
            </el-form-item>
            <el-form-item label="省:" prop="provice">
              <el-input v-model="orderDetails.orderUser.provice" readonly />
            </el-form-item>
            <el-form-item label="市:" prop="city">
              <el-input v-model="orderDetails.orderUser.city" readonly />
            </el-form-item>
            <el-form-item label="县:" prop="country">
              <el-input v-model="orderDetails.orderUser.country" readonly />
            </el-form-item>
            <el-form-item label="邮编:" prop="zipcode">
              <el-input v-model="orderDetails.orderUser.zipcode" readonly />
            </el-form-item>
            <el-form-item label="电话:" prop="phone">
              <el-input v-model="orderDetails.orderUser.phone" readonly />
            </el-form-item>
            <el-form-item label="详细地址:" prop="address">
              <el-input v-model="orderDetails.orderUser.address" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="上门取件信息">
            <el-form-item label="姓名:" prop="receiver">
              <el-input v-model="returnGoodsOrder.orderUser.receiver" readonly />
            </el-form-item>
            <el-form-item label="省:" prop="receiver">
              <el-input v-model="returnGoodsOrder.orderUser.provice" readonly />
            </el-form-item>
            <el-form-item label="市:" prop="receiver">
              <el-input v-model="returnGoodsOrder.orderUser.city" readonly />
            </el-form-item>
            <el-form-item label="县:" prop="receiver">
              <el-input v-model="returnGoodsOrder.orderUser.country" readonly />
            </el-form-item>
            <el-form-item label="邮编:" prop="receiver">
              <el-input v-model="returnGoodsOrder.orderUser.zipcode" readonly />
            </el-form-item>
            <el-form-item label="电话:" prop="phone">
              <el-input v-model="returnGoodsOrder.orderUser.phone" readonly />
            </el-form-item>
            <el-form-item label="详细地址:" prop="address">
              <el-input v-model="returnGoodsOrder.orderUser.address" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="发票信息">
            <el-form-item label="发票抬头:" prop="invoiceTitle">
              <el-input v-model="orderDetails.invoiceTitle" readonly />
            </el-form-item>
            <el-form-item label="发票明细:" prop="invoiceContent">
              <el-input v-model="orderDetails.invoiceContent" readonly />
            </el-form-item>
            <el-form-item label="发票邮箱:" prop="invoiceEmail">
              <el-input v-model="orderDetails.invoiceEmail" readonly />
            </el-form-item>
            <el-form-item label="发票电话:" prop="invoicePhone">
              <el-input v-model="orderDetails.invoicePhone" readonly />
            </el-form-item>
            <el-form-item label="发票税号:" prop="invoiceTaxNo">
              <el-input v-model="orderDetails.invoiceTaxNo" readonly />
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="商品信息">
            <el-table :data="orderDetails.orderItems" fit highlight-current-row style="width: 100%">
              <el-table-column align="center" label="商品ID" prop="productId" />
              <el-table-column align="center" label="商品名称" prop="productName" />
              <el-table-column align="center" label="skuId" prop="skuId" />
              <el-table-column align="center" label="skuName" prop="skuName" />
              <el-table-column align="center" label="数量" prop="quantity" />
              <el-table-column align="center" label="售价(元)" prop="salePrice" />
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          v-if="returnGoodsOrder.status == 0 && returnGoodsOrder.status != 3"
          v-waves
          v-permission="['oms:return:order:audit']"
          type="primary"
          @click="orderAudit(2)"
        >退货审核通过</el-button>
        <el-button
          v-if="returnGoodsOrder.status == 0 && returnGoodsOrder.status != 3"
          v-waves
          v-permission="['oms:return:order:audit']"
          type="primary"
          @click="orderAudit(1)"
        >退货审核不通过</el-button>
        <el-button
          v-if="refund.status == 0 && returnGoodsOrder.status == 3"
          v-waves
          v-permission="['oms:return:order:refund']"
          type="primary"
          @click="refundAudit(2)"
        >退款审核通过</el-button>
        <el-button
          v-if="refund.status == 0 && returnGoodsOrder.status == 3"
          v-waves
          v-permission="['oms:return:order:refund']"
          type="primary"
          @click="refundAudit(1)"
        >退款审核不通过</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  getReturnOrder,
  getAuditLog,
  getRefund,
  orderAudit,
  refundAudit
} from '@/api/oms/returnOrder'
import { get } from '@/api/oms/order'
import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
export default {
  name: 'DetectionTable',
  directives: { waves },
  filters: {},
  components: {
    DictSelect,
    DatePicker
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        status: 0,
        orderByField: 'createdAt',
        orderBy: 'desc'
      },
      textMap: {
        update: '更新',
        create: '新增',
        info: '详情'
      },
      dialogStatus: '',
      dialogFormVisible: false,
      orderDetails: {
        orderUser: {}
      },
      returnGoodsOrder: {
        orderUser: {}
      },
      refund: {},
      auditLog: [],
      refundLog: []
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.orderDetails = { orderUser: {}}
        this.returnGoodsOrder = { orderUser: {}}
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'info'
      this.dialogFormVisible = true
      this.getDetails(row.orderId)
    },
    getDetails(orderId) {
      this.resetTemp()
      get(orderId).then(response => {
        this.orderDetails = response
      })
      getReturnOrder(orderId).then(response => {
        this.returnGoodsOrder = response
      })
      getAuditLog(orderId).then(response => {
        this.auditLog = response
      })
      getRefund(orderId).then(response => {
        if (response != null) {
          this.refund = response
        }
      })
    },
    orderAudit(status) {
      var orderId = this.returnGoodsOrder.orderId
      this.$confirm(
        status === 2
          ? '此操作将会修改退货单为审核通过?'
          : '此操作将会修改退货单为审核不通过?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        orderAudit(orderId, status).then(() => {
          this.$notify({
            title: '成功',
            message: '审核成功',
            type: 'success',
            duration: 2000
          })
          this.getDetails(orderId)
          this.getList()
        })
      })
    },
    refundAudit(status) {
      var orderId = this.returnGoodsOrder.orderId
      this.$confirm(
        status === 2
          ? '此操作将会修改退货单为退款审核通过?'
          : '此操作将会修改退货单为退款审核不通过?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        refundAudit(orderId, status).then(() => {
          this.$notify({
            title: '成功',
            message: '审核成功',
            type: 'success',
            duration: 2000
          })
          this.getDetails(orderId)
          this.getList()
        })
      })
    }
  }
}
</script>
