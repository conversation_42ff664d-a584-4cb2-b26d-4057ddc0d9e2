<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="经纪人Id"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        placeholder="经纪人姓名"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        placeholder="经纪人手机号"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-cascader
        v-model="listQuery.areaId"
        placeholder="所属省筛选"
        :options="cityData"
        :props="cityProps"
        clearable
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 220px"
        gte="entryTimeGte"
        lte="entryTimeLte"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.titleType"
        placeholder="职位筛选"
        class="filter-item"
        type="agent_title_type"
        style="width: 110px;"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.status"
        placeholder="经纪人状态筛选"
        class="filter-item"
        type="agent_status"
        style="width: 150px;"
        @change="handleFilter"
      />
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <!-- <el-button
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button> -->
      <el-upload
        style="display: inline-block;"
        :action="uploadPath"
        :headers="headers"
        :before-upload="handleBeforeUpload"
        :on-success="handleExcelSuccess"
        :on-error="handleExcelError"
        :on-remove="handleRemoveExcel"
        :file-list="fileList"
        accept=".zip"
        :limit="1"
        :show-file-list="false"
      >
        <el-button
          v-permission="['agent:import']"
          type="primary"
        >导入</el-button>
        <div
          slot="tip"
          class="el-upload__tip"
        ></div>
      </el-upload>
      <el-link
        v-permission="['agent:import']"
        :href="tempPath"
        type="info"
        target="_blank"
      >
        <el-button type="primary">下载批量导入经纪人模板1</el-button>
      </el-link>

    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      @selection-change="onSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50"
        fixed
      ></el-table-column>
      <el-table-column
        label="经纪人ID"
        prop="id"
        align="center"
        width="80px"
      />
      <el-table-column
        label="经纪人姓名"
        prop="name"
        width="120px"
        align="center"
      >
        <template slot-scope="{row}">
          <AgentDetail
            :id="row.id"
            :ref="row.id"
            :name="row.name"
            :handle-filter="getList"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="经纪人手机号"
        prop="phone"
        align="center"
        width="120px"
      />
      <el-table-column
        label="状态"
        prop="name"
        align="center"
        width="120px"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.status == 0"
            size="small"
            type="danger"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status == 1"
            size="small"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status == 2"
            size="small"
            type="success"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status == 3"
            size="info"
          >{{ row.statusDescribe }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="职位"
        prop="titleTypeDescribe"
        width="120px"
        align="center"
      />
      <el-table-column
        label="上级领导"
        prop="leaderName"
        align="center"
        width="120px"
      />
      <el-table-column
        label="所属区域"
        prop="areaName"
        min-width="200px"
        align="center"
      >
        <template slot-scope="{row}">
          {{ row.areaName ? row.areaName :" - " }}
        </template>
      </el-table-column>
      <el-table-column
        label="加入时间"
        prop="entryTime"
        width="135px"
        align="center"
      />
      <el-table-column
        label="总订单金额"
        prop="totalOrderAmount"
        min-width="150px"
        align="center"
      ></el-table-column>
    </el-table>

    <div
      v-show="total>0"
      class="download"
    >
      <pagination
        :total="total"
        :page.sync="listQuery.pageNo"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
      <div>下载维度：</div>
      <ReportExportTime @onPickerChange="onPickerChange"></ReportExportTime>
      <el-button type="primary" @click="handleDownload">下载业绩报表</el-button>
    </div>
  </div>
</template>
<style>
.download {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
</style>
<script>
import waves from '@/directive/waves' // Waves directive
import { agentImport, getList, areaList, tempUrl, achievementExport } from '@/api/agent/user/user'
import { getToken, getTokenName } from '@/utils/auth'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import ReportExportTime from '../components/ReportExportTime'
import AgentDetail from '../components/AgentDetail'
export default {
  name: 'UserList',
  directives: { waves },
  filters: {},
  components: {
    DictSelect,
    DatePicker,
    AgentDetail,
    ReportExportTime
  },
  data() {
    return {
      tableKey: 0,
      cityData: [],
      list: [],
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        reportType: 1
      },
      cityProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'child',
        label: 'name',
        value: 'id'
      },
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: [],
      agentId: '',
      downloadUrl: ''
    }
  },
  created() {
    this.uploadPath = agentImport()
    this.headers[getTokenName()] = getToken()
    this.tempUrl()
    this.getCityList()
    this.getList()
  },
  mounted() {
    this.bus.$on('onUpdateList', (e) => {
      this.getList()
    })
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getCityList() {
      areaList({ type: 1 }).then((response) => {
        this.cityData = response
      })
    },
    tempUrl() {
      tempUrl().then((response) => {
        this.tempPath = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    // 下载报表
    handleDownload() {
      const params = {
        agentIds: this.agentId,
        reportDates: this.listQuery.reportDate,
        reportType: this.listQuery.reportType
      }
      params[getTokenName()] = getToken()
      this.downloadUrl = achievementExport(params)
      console.log(params, this.downloadUrl, 'downloadUrl')
      window.location.href = this.downloadUrl
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10,
        reportType: 1
      }
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleCreate() {
      this.dialogFormVisible = true
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.$message({
          message: '操作成功',
          type: 'success'
        })
        this.getList()
      }
    },
    handleRemoveExcel(file, fileList) {},
    handleBeforeUpload(file) {
      const isZip = file.name.endsWith('.zip')
      if (!isZip) {
        this.$message.error('请选择zip文件!')
        return false
      }
    },
    onPickerChange(data) {
      this.listQuery.reportType = data.type
      this.listQuery.reportDate = data.date
    },
    onSelectionChange(data) {
      this.agentId = data.map(item => item.id).join(',')
    }

  }
}
</script>
