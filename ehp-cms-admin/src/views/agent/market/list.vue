<!-- 销售内容管理 -->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.changedBy"
        class="filter-item"
        clearable
        placeholder="编辑人"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.releaseBy"
        class="filter-item"
        clearable
        placeholder="发布人"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker
        ref="datePickerRef1"
        :query-model="listQuery"
        class="filter-item"
        start-placeholder="编辑时间-开始"
        end-placeholder="编辑时间-结束"
        gte="changedAtGte"
        lte="changedAtLte"
        style="width: 230px"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.status"
        class="filter-item"
        placeholder="发布状态选择"
        style="width: 120px"
        type="agent_notice_status"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef2"
        :query-model="listQuery"
        class="filter-item"
        start-placeholder="发布时间-开始"
        end-placeholder="发布时间-结束"
        gte="releaseTimeGte"
        lte="releaseTimeLte"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button v-permission="['agent:notice:save']" type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
    </div>
    <el-table :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="序号" type="index" align="center" />
      <el-table-column label="消息类型" prop="typeDescribe" align="center" />
      <el-table-column label="发布状态" prop="status" align="center">
        <template slot-scope="{row}">
          <el-tag v-if="row.status == 0">待发布</el-tag>
          <el-tag v-if="row.status == 1" type="success">已发布</el-tag>
          <el-tag v-if="row.status == 2" type="info">已下架</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="编辑时间" prop="changedAt" width="150px" align="center" />
      <el-table-column label="发布时间" prop="releaseTime" width="150px" align="center" />
      <el-table-column label="编辑人" prop="changedBy" align="center" />
      <el-table-column label="发布人" prop="releaseBy" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="400px">
        <template slot-scope="{row}">
          <el-button v-if="row.status == 2||row.status == 1" v-permission="['agent:notice:get']" type="primary" @click="handleDetail(row)">查看</el-button>
          <el-button v-if="row.status == 0" type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button v-if="row.status == 0" v-permission="['agent:notice:release']" type="primary" @click="handleIssue(row)">发布</el-button>
          <el-button v-if="row.status == 1" v-permission="['agent:notice:release:cancel']" type="primary" @click="undercarriage(row)">下架</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false" top="2vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="公告标题:" prop="title">
          <el-input v-model="form.title" :disabled="disabled" placeholder="请输入公告标题" maxlength="20" show-word-limit class="inputW" />
        </el-form-item>
        <el-form-item label="公告内容:" prop="content">
          <el-input v-model="form.content" type="textarea" :disabled="disabled" placeholder="请输入公告内容" rows="12" maxlength="1000" show-word-limit />
        </el-form-item>
      </el-form>
      <div v-if="dialogStatus=='create'||dialogStatus=='update'" slot="footer" class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="dialogStatus==='create'?createData(0):updateData(0)">保存</el-button>
        <el-button v-permission="['agent:notice:release']" type="primary" @click="dialogStatus==='create'?createData(1):updateData(1)">发布</el-button>
      </div>
      <div v-else slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button v-if="status==1" v-permission="['agent:notice:release:cancel']" type="primary" @click="undercarriage(1)">下架</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, get, create, update, issue, undercarriage } from '@/api/agent/market'
import waves from '@/directive/waves'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'

export default {
  name: 'MarketList',
  directives: { waves },
  components: { DictSelect, DatePicker },
  filters: {},

  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        changedBy: '',
        releaseBy: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '详情',
        update: '编辑',
        create: '新增'
      },
      form: {
        id: '',
        title: '',
        content: '',
        pushState: null //发布状态: 0:不发布，1发布
      },
      rules: {
        title: [
          { required: true, message: '请填写公告标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请填写公告内容', trigger: 'blur' }
        ]
      },
      disabled: false,
      id: '',
      status: ''
    }
  },
  created() {
    this.getList()
  },

  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.$refs.datePickerRef1.reset()
      this.$refs.datePickerRef2.reset()
      this.listQuery.changedBy = ''
      this.listQuery.releaseBy = ''
      this.listQuery.status = ''
      this.handleFilter()
    },
    resetForm() {
      this.$nextTick(() => {
        this.form = {
          id: '',
          title: '',
          content: ''
        }
        this.$refs['form'].clearValidate()
      })
    },
    // 新增
    handleAdd() {
      this.id = ''
      this.disabled = false
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetForm()
    },
    createData(pushState) {
      this.form.pushState = pushState
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.pushState) {
            this.$confirm('确认发布消息至经纪人客户端？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              create(this.form).then(() => {
                this.dialogFormVisible = false
                this.$message.success('新增发布成功！')
                this.handleFilter()
              })
            })
          } else {
            create(this.form).then(() => {
              this.dialogFormVisible = false
              this.$message.success('新增保存成功！')
              this.handleFilter()
            })
          }
        }
      })
    },
    // 编辑
    handleEdit(row) {
      this.id = row.id
      this.disabled = false
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetForm()
      const params = {
        id: row.id
      }
      get(params).then(res => {
        this.form = res
      })
    },
    updateData(pushState) {
      this.form.pushState = pushState
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.pushState) {
            this.$confirm('确认发布更改内容至经纪人客户端？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              update(this.form).then(() => {
                this.dialogFormVisible = false
                this.$message.success('编辑发布成功！')
                this.handleFilter()
              })
            })
          } else {
            update(this.form).then(() => {
              this.dialogFormVisible = false
              this.$message.success('编辑保存成功！')
              this.handleFilter()
            })
          }
        }
      })
    },
    // 取消
    cancel() {
      if (this.form.title !== '' || this.form.content !== '') {
        this.$confirm('尚未保存确认退出？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.dialogFormVisible = false
        })
      } else {
        this.dialogFormVisible = false
      }
    },
    // 查看
    handleDetail(row) {
      this.id = row.id
      this.status = row.status
      this.disabled = true
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
      this.resetForm()
      console.log(this.id)
      const params = {
        id: row.id
      }
      get(params).then(res => {
        this.form = res
      })
    },
    // 发布
    handleIssue(row) {
      this.$confirm('确认发布消息至经纪人客户端？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {}
        if (row === 1) {
          params.id = this.id
        } else {
          params.id = row.id
        }
        issue(params).then(response => {
          this.dialogFormVisible = false
          this.$message.success('发布成功！')
          this.getList()
        })
      })
    },
    // 下架
    undercarriage(row) {
      this.$confirm('确认下架该条公告信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {}
        if (row === 1) {
          params.id = this.id
        } else {
          params.id = row.id
        }
        undercarriage(params).then(response => {
          this.dialogFormVisible = false
          this.$message.success('下架成功！')
          this.getList()
        })
      })
    }
  }
}
</script>

<style scoped>
.inputW{
  width: 250px;
}
.sameText{
  margin-bottom: 15px;
}
</style>
