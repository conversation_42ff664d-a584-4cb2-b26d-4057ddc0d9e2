<template>
  <div class="">
    <!-- 下属经纪人 -->
    <el-input
      v-model="listQuery.id"
      placeholder="经纪人Id"
      clearable
      class="filter-item"
      style="width: 120px;"
      @keyup.enter.native="handleFilter"
    />
    <el-input
      v-model="listQuery.name"
      placeholder="经纪人姓名"
      clearable
      class="filter-item"
      style="width: 120px;"
      @keyup.enter.native="handleFilter"
    />
    <el-input
      v-model="listQuery.phone"
      placeholder="经纪人手机号"
      clearable
      class="filter-item"
      style="width: 120px;"
      @keyup.enter.native="handleFilter"
    />
    <DictSelect
      v-model="listQuery.titleType"
      placeholder="职位筛选"
      class="filter-item"
      type="agent_title_type"
      style="width: 110px;"
      @change="handleFilter"
    />
    <DatePicker
      ref="datePickerRef"
      :query-model="listQuery"
      class="filter-item"
      style="width: 220px"
      gte="entryTimeGte"
      lte="entryTimeLte"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      @change="handleFilter"
    />
    <DictSelect
      v-model="listQuery.status"
      placeholder="经纪人状态筛选"
      class="filter-item"
      type="agent_status"
      style="width: 150px;"
      @change="handleFilter"
    />
    <el-button
      type="primary"
      icon="el-icon-search"
      @click="handleFilter"
    >搜索</el-button>

    <el-table
      :data="list"
      fit
      highlight-current-row
      @selection-change="onSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50"
        fixed
      ></el-table-column>
      <el-table-column
        label="经纪人ID"
        prop="id"
        align="center"
        width="80px"
      />
      <el-table-column
        label="姓名"
        prop="name"
        align="center"
        width="80px"
      />
      <el-table-column
        label="手机号"
        prop="phone"
        align="center"
        width="120px"
      />
      <el-table-column
        label="状态"
        prop="statusDescribe"
        align="center"
        width="120px"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.status == 0"
            size="small"
            type="danger"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status == 1"
            size="small"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status == 2"
            size="small"
            type="success"
          >{{ row.statusDescribe }}</el-tag>
          <el-tag
            v-if="row.status == 3"
            size="info"
          >{{ row.statusDescribe }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="职位"
        prop="titleTypeDescribe"
        width="120px"
        align="center"
      />
      <el-table-column
        label="所属区域"
        prop="areaName"
        min-width="200px"
        align="center"
      />
      <el-table-column
        label="加入时间"
        prop="entryTime"
        width="135px"
        align="center"
      />
      <el-table-column
        label="总订单金额"
        prop="totalOrderAmount"
        min-width="150px"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="150px">
        <template slot-scope="{row}">
          <el-button
            v-if="id==row.id"
            type="primary"
            size="mini"
            @click="handleOpen(row.id)"
          >业绩信息</el-button>
          <el-button
            v-else
            type="primary"
            size="mini"
            @click="handleOpen(row.id)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script>
import { getList } from '@/api/agent/user/user'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
export default {
  components: {
    DatePicker,
    DictSelect
  },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {
    getData() {
      this.listQuery.parentId = this.id
      getList(this.listQuery).then((response) => {
        this.total = response.totalCount
        this.list = response.list
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getData()
    },
    handleOpen(id) {
      this.$emit('onOpenAgent', { id })
    },
    onSelectionChange() {
    }
  }
}
</script>
<style>
</style>
