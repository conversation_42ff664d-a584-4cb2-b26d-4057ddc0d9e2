<template>
  <div class="">
    <!-- 业绩信息 -->
    <el-table
      :data="[info]"
      border
      style="width: 600px"
    >
      <el-table-column
        label="姓名"
        prop="name"
        align="center"
        width="150px"
      />
      <el-table-column
        label="下属医生总人数"
        prop="chlidDoctorTotal"
        align="center"
        width="150px"
      />
      <el-table-column
        label="处方总数量"
        prop="recipelTotal"
        align="center"
        width="150px"
      />
      <el-table-column
        label="订单总金额"
        prop="orderPriceTotal"
        align="center"
        width="150px"
      />
    </el-table>
    <div class="download">
      <ReportExportTime @onPickerChange="onPickerChange"></ReportExportTime>
      <el-button type="primary" @click="handleDownload()">下载报表</el-button>
    </div>
    <el-table
      :data="list"
      fit
      highlight-current-row
      @selection-change="onSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        label="日期"
        prop="reportDate"
        align="center"
        width="150px"
      />
      <el-table-column
        label="新增医生"
        prop="addDoctorTotal"
        align="center"
        width="150px"
      />
      <el-table-column
        label="处方数量"
        prop="recipelTotal"
        align="center"
        width="150px"
      />
      <el-table-column
        label="订单金额"
        prop="orderPriceTotal"
        align="center"
      >
        <template slot-scope="{row}">
          <span>{{ row.orderPriceTotal / 100 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        align="center"
        width="150px"
      >
        <template slot-scope="{row}">
          <el-link
            :href="tempPath"
            type="info"
            target="_blank"
          >
            <el-button type="primary" @click="handleDownload(row.reportDate)">下载报表</el-button>
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script>
import {
  achievementList,
  achievementExport,
  achievementInfo
} from '@/api/agent/user/user.js'
import { getToken, getTokenName } from '@/utils/auth'
import ReportExportTime from '../components/ReportExportTime'
export default {
  components: {
    ReportExportTime
  },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      info: {},
      list: [],
      total: 0,
      download: {
        reportType: 1
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        reportType: 1
      },
      totalList: [],
      tempPath: ''
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {},
  mounted() {},
  methods: {
    getinfo() {
      achievementInfo({ agentId: this.id }).then((response) => {
        this.info = response
      })
    },
    getData(params) {
      this.listQuery.agentId = this.id
      achievementList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
        if (params !== 'change') {
          this.getinfo(this.id)
        }
      })
    },
    // 下载报表
    handleDownload(date = '') {
      console.log(date, 162)
      const params = {
        agentIds: this.id,
        reportDates: date ? date : this.download.reportDates,
        reportType: this.listQuery.reportType
      }
      params[getTokenName()] = getToken()
      this.downloadUrl = achievementExport(params)
      console.log(params, this.downloadUrl, 'downloadUrl')
      window.location.href = this.downloadUrl
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    onSelectionChange(data) {
      this.download.reportDates = data.map(item => item.reportDate).join(',')
      console.log(this.download.reportDates, 178)
    },
    onPickerChange(data) {
      this.listQuery.reportType = data.type
      this.listQuery.reportDate = data.date
      this.download.reportDates = data.date
      this.getData('change')
    }
  }
}
</script>
<style>
</style>
