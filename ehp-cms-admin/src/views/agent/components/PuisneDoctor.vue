<template>
  <div class="">
    <!-- 下属医生 -->
    <el-input
      v-model="listQuery.doctorId"
      placeholder="医生Id"
      clearable
      class="filter-item"
      style="width: 120px;"
      @keyup.enter.native="handleFilter"
    />
    <el-input
      v-model="listQuery.doctorName"
      placeholder="姓名"
      clearable
      class="filter-item"
      style="width: 120px;"
      @keyup.enter.native="handleFilter"
    />
    <el-input
      v-model="listQuery.doctorPhone"
      placeholder="手机号"
      clearable
      class="filter-item"
      style="width: 120px;"
      @keyup.enter.native="handleFilter"
    />
    <DatePicker
      ref="datePickerRef"
      :query-model="listQuery"
      class="filter-item"
      style="width: 220px"
      gte="entryTimeGte"
      lte="entryTimeLte"
      start-placeholder="业务开始时间"
      end-placeholder="业务结束时间"
      @change="handleFilter"
    />
    <el-button
      type="primary"
      icon="el-icon-search"
      @click="handleFilter"
    >搜索</el-button>

    <el-button type="primary" @click="handleDownload">报表下载</el-button>

    <el-table
      :data="list"
      fit
      highlight-current-row
      @selection-change="onSelectionChange"
    >
      <el-table-column
        type="selection"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        label="医生ID"
        prop="doctorId"
        align="center"
        width="80px"
      />
      <el-table-column
        label="姓名"
        prop="doctorName"
        align="center"
        width="80px"
      />
      <el-table-column
        label="手机号"
        prop="doctorPhone"
        align="center"
        width="120px"
      />
      <el-table-column
        label="问诊次数"
        prop="consultCnt"
        width="120px"
        align="center"
      />
      <el-table-column
        label="处方次数"
        prop="prescriptionCnt"
        min-width="120px"
        align="center"
      />
      <el-table-column
        label="续方次数"
        prop="repeatCnt"
        align="center"
        width="150px"
      />
      <el-table-column
        label="患者人数"
        prop="patientCnt"
        width="135px"
        align="center"
      />
      <el-table-column
        label="总订单金额"
        prop="orderAmount"
        min-width="150px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="患者满意度"
        prop="degreeOfSatisfaction"
        min-width="150px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="投诉次数"
        prop="complainCnt"
        min-width="150px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="入驻时间"
        prop="entryTime"
        min-width="150px"
        align="center"
      ></el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script>
import { doctorList, customExport } from '@/api/agent/user/user'
import { getToken, getTokenName } from '@/utils/auth'
import DatePicker from '@/components/DatePicker'
export default {
  components: {
    DatePicker
  },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        agentId: ''
      },
      doctorIds: '',
      tempPath: ''
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {
    // this.uploadPath = excelURL()
  },
  mounted() {},
  methods: {
    getData() {
      this.listQuery.agentId = this.id
      doctorList(this.listQuery).then((response) => {
        this.total = response.totalCount
        this.list = response.list
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getData()
    },
    // 导出医生数据
    handleDownload() {
      const params = {
        doctorIds: this.doctorIds,
        startDate: this.listQuery.entryTimeGte,
        endDate: this.listQuery.entryTimeLte
      }
      params[getTokenName()] = getToken()
      this.downloadUrl = customExport(params)
      console.log(params, this.downloadUrl, 'downloadUrl')
      window.location.href = this.downloadUrl
    },
    onSelectionChange(data) {
      this.doctorIds = data.map(item => item.doctorId).join(',')
      console.log(data, this.doctorIds, 201)
    }
  }
}
</script>
<style>
</style>
