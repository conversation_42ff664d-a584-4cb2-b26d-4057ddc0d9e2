<template>
  <div>
    <el-dialog
      title="经纪人详情"
      :visible.sync="dialogInfoVisible"
      width="80%"
      top="2vh"
      append-to-body
      @close="handleCloseDetails"
    >
      <el-tabs
        v-model="activeTabs"
        style="min-height: 300px;"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          label="经纪人基本信息"
          name="tab_1"
        >
          <AgentInfo
            :id="agentId"
            ref="tab_1"
          ></AgentInfo>
        </el-tab-pane>
        <el-tab-pane
          label="下属经纪人"
          name="tab_2"
        >
          <AgentPuisne
            :id="agentId"
            ref="tab_2"
            @onOpenAgent="onOpenAgent"
          ></AgentPuisne>
        </el-tab-pane>
        <el-tab-pane
          label="下属医生"
          name="tab_3"
        >
          <PuisneDoctor :id="agentId" ref="tab_3"></PuisneDoctor>
        </el-tab-pane>
        <el-tab-pane
          label="业绩信息"
          name="tab_4"
        >
          <Performance
            :id="agentId"
            ref="tab_4"
          ></Performance>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-link
      v-if="name"
      type="primary"
      :underline="false"
      @click="handleAgentDetailsInfo()"
    >{{ name }}</el-link>

  </div>
</template>
<style scoped>
.el-dialog {
  text-align: left;
}
</style>
<script>
import AgentInfo from '../components/AgentInfo'
import AgentPuisne from '../components/AgentPuisne'
import PuisneDoctor from '../components/PuisneDoctor'
import Performance from '../components/Performance'
export default {
  name: 'AgentDetail',
  components: {
    AgentInfo,
    AgentPuisne,
    PuisneDoctor,
    Performance
  },
  props: {
    id: {
      type: [String, Number],
      required: false,
      default: ''
    },
    name: {
      type: String,
      required: false,
      default: ''
    },
    handleFilter: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      agentId: this.id,
      activeTabs: 'tab_1',
      dialogInfoVisible: false
    }
  },
  watch: {
    id(newVal, oldVal) {
      this.agentId = newVal
    }
  },
  mounten() {},
  methods: {
    handleTabClick(event) {
      this.$nextTick(() => {
        this.$refs[event.name].getData()
      })
    },
    handleAgentDetailsInfo() {
      console.log(this.id, '116')
      this.agentId = this.id
      this.dialogInfoVisible = true
      this.activeTabs = 'tab_1'
      this.$nextTick(() => {
        this.$refs['tab_1'].getData()
      })
    },
    handleCloseDetails() {
      this.dialogInfoVisible = false
    },
    onOpenAgent(data) {
      if (this.agentId === data.id) {
        this.activeTabs = 'tab_4'
        this.$refs['tab_4'].getData()
      } else {
        this.agentId = data.id
        this.activeTabs = 'tab_1'
      }
    }
  }
}
</script>
