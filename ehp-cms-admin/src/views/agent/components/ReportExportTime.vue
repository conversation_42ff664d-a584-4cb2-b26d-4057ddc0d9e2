<template>
  <div class="exportTime">
    <div class="exportTime-left">
      <span
        v-for="(item,index) in list"
        :key="index"
        :class="currentType == item.type ? 'active':''"
        @click="handleChangeType(item.type)"
      >{{ item.name }}</span>
    </div>
    <div
      v-if="currentType!==1"
      class="exportTime-time"
    >
      <el-date-picker
        v-model="currentDate"
        :type="datePickerType"
        :value-format="dateFormat"
        @change="ondatePickerChange"
      >
      </el-date-picker>
    </div>
  </div>
</template>

<script>
import { customFormat } from '@/utils/date'
export default {
  components: {},
  data() {
    return {
      currentDate: '',
      datePickerType: 'year',
      dateFormat: 'yyyy-MM',
      currentType: 1,
      list: [
        { name: '年', type: 1 },
        { name: '月', type: 2 },
        { name: '日', type: 3 }
      ]
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleChangeType(type) {
      this.currentType = type
      this.currentDate = ''
      if (type === 2) {
        this.dateFormat = 'yyyy'
        this.datePickerType = 'year'
        this.currentDate = customFormat(new Date().getTime(), 'yyyy')
      } else if (type === 3) {
        this.dateFormat = 'yyyy-MM'
        this.datePickerType = 'month'
        this.currentDate = customFormat(new Date().getTime(), 'yyyy-MM')
      }
      this.$emit('onPickerChange', { type: this.currentType, date: this.currentDate })
    },
    ondatePickerChange(date) {
      this.$emit('onPickerChange', { type: this.currentType, date: this.currentDate })
    }
  }
}
</script>
<style scoped>
.exportTime {
  display: flex;
  align-items: center;
}
.exportTime-left {
  display: flex;
  align-items: center;
  margin-right: 10px;
}
.exportTime-left span {
  padding: 5px 10px;
  border: 1px solid #eee;
  margin-right: 5px;
  border-radius: 4px;
}
.exportTime-time {
  margin-right: 10px;
}
.active {
  background: #1990ff;
  color: #fff;
}
</style>
