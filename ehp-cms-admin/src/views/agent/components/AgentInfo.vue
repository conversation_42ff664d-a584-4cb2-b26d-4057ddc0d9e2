<template>
  <div class="">
    <!-- 经纪人基本信息 -->
    <el-form
      ref="dataForm"
      :model="agentInfo"
      label-position="right"
      label-width="150px"
    >
      <el-row>
        <el-col :span="13">
          <el-form-item
            label="认证状态:"
            prop="status"
          >
            <div>{{ agentInfo.statusDescribe }}</div>
          </el-form-item>
          <el-form-item
            label="加入时间:"
            prop="status"
          >
            <div>{{ agentInfo.entryTime }}</div>
          </el-form-item>
          <el-form-item
            label="姓名:"
            prop="status"
          >
            <div>{{ agentInfo.name }}</div>
          </el-form-item>
          <el-form-item
            label="性别:"
            prop="status"
          >
            <div>{{ agentInfo.gender == 0?'未知':agentInfo.gender == 1 ?'男':'女' }}</div>
          </el-form-item>
          <el-form-item
            label="手机号:"
            prop="gender"
          >
            <el-input
              v-model="agentInfo.phone"
              readonly
            />
          </el-form-item>
          <el-form-item
            label="职位:"
            prop="gender"
          >
            <el-input
              v-model="agentInfo.titleTypeDescribe"
              readonly
            />
          </el-form-item>
          <el-form-item
            label="所属区域:"
            prop="gender"
          >
            <el-input
              v-model="agentInfo.areaName"
              readonly
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13">
          <el-form-item
            label="身份证号:"
            prop="number"
          >
            <el-input
              v-model="agentInfo.cardNo"
              readonly
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13">
          <el-form-item label="身份证正面:">
            <el-upload
              :disabled="!isSave"
              list-type="picture-card"
              :action="uploadPath"
              :headers="headers"
              :file-list="agentInfo.frontUrl"
              :before-upload="handleBeforeUpload"
              :on-success="handlefrontSuccess"
              accept="image/png, image/jpg"
            >
              <div
                slot="file"
                slot-scope="{ file }"
              >
                <el-image
                  class="el-upload-list__item-thumbnail"
                  :src="file.url"
                  fit="fill"
                  alt
                />
                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-preview"
                    @click="handlePreview(file.url)"
                  >
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleDownload(file.url)"
                  >
                    <i class="el-icon-download"></i>
                  </span>
                  <span
                    v-if="isSave"
                    class="el-upload-list__item-delete"
                    @click="handleRemoveUrls(file.url,'front')"
                  >
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
              <el-button
                :disabled="!isSave"
                size="small"
                type="primary"
              >点击上传</el-button>
              <div
                slot="tip"
                class="el-upload__tip"
              >只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13">
          <el-form-item label="身份证反面:">
            <el-form-item>
              <el-upload
                :disabled="!isSave"
                list-type="picture-card"
                :action="uploadPath"
                :headers="headers"
                :file-list="agentInfo.backUrl"
                :before-upload="handleBeforeUpload"
                :on-success="handlebackSuccess"
                accept="image/png, image/jpg"
              >
                <div
                  slot="file"
                  slot-scope="{ file }"
                >
                  <el-image
                    class="el-upload-list__item-thumbnail"
                    :src="file.url"
                    fit="fill"
                    alt
                  />
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePreview(file.url)"
                    >
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span
                      class="el-upload-list__item-delete"
                      @click="handleDownload(file.url)"
                    >
                      <i class="el-icon-download"></i>
                    </span>
                    <span
                      v-if="isSave"
                      class="el-upload-list__item-delete"
                      @click="handleRemoveUrls(file.url,'back')"
                    >
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
                <el-button
                  :disabled="!isSave"
                  size="small"
                  type="primary"
                >点击上传</el-button>
                <div
                  slot="tip"
                  class="el-upload__tip"
                >只能上传jpg/png文件，且不超过500kb</div>
              </el-upload>
            </el-form-item>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col
          :span="24"
          style="text-align:right;"
        >
          <el-button
            v-if="agentInfo.status === 1"
            v-permission="['agent:audit']"
            type="primary"
            @click="handleAuth"
          >认证审核</el-button>
          <el-button
            v-if="isSave"
            v-permission="['agent:save']"
            type="primary"
            @click="handleSave()"
          >保存资料</el-button>
          <el-button
            v-else
            v-permission="['agent:save']"
            type="primary"
            @click="isSave = true"
          >编辑资料</el-button>

        </el-col>
      </el-row>
    </el-form>
    <el-dialog
      title="认证审核"
      :visible.sync="dialogStatusInfoVisible"
      append-to-body
    >
      <el-form
        ref="userStatusDataForm"
        :model="userStatus"
        :rules="statusRules"
        label-position="right"
        label-width="150px"
        style="width:90%"
      >

        <el-form-item
          label="审核状态"
          prop="status"
        >
          <el-select
            v-model="userStatus.status"
            placeholder="请选择审核状态"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="userStatus.status == 1"
          label="加入省份"
        >
          <el-cascader
            v-model="userStatus.areaId"
            placeholder="所属省筛选"
            :options="cityData"
            :props="cityProps"
            clearable
          />
        </el-form-item>
        <el-form-item
          v-if="userStatus.status == 0"
          label="审核原因"
          prop="reason"
        >
          <el-input
            v-model="userStatus.reason"
            type="textarea"
            :maxlength="35"
            :rows="2"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogStatusInfoVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleStatusData()"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      append-to-body
      :visible.sync="dialogVisible"
    >
      <img
        width="100%"
        :src="dialogImageUrl"
        alt
      />
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves' // Waves directive
import { getDetail, save, audit, areaList } from '@/api/agent/user/user'
import { getToken, getTokenName } from '@/utils/auth'
export default {
  directives: { waves },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      cityData: null,
      cityProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'child',
        label: 'name',
        value: 'id'
      },
      agentInfo: {},
      headers: {},
      uploadPath: process.env.VUE_APP_BASE_API + '/storage',
      dialogImageUrl: '',
      dialogVisible: false,
      dialogStatusInfoVisible: false,
      userStatus: {},
      statusOptions: [
        {
          value: 1,
          label: '审核通过'
        },
        {
          value: 0,
          label: '审核不通过'
        }
      ],
      statusRules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'blur' }
        ],
        reason: [
          { required: true, message: '请填写审核原因', trigger: 'blur' }
        ]
        // reason: [
        //   {
        //     validator: (rule, value, callback) => {
        //       if (
        //         this.userStatus.status === 1 &&
        //         this.userStatus.reason === ''
        //       ) {
        //         callback(new Error('请填写审核原因'))
        //       } else {
        //         callback()
        //       }
        //     },
        //     trigger: 'blur'
        //   }
        // ]
      },
      isSave: false
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {
    this.headers[getTokenName()] = getToken()
  },
  mounted() {
    this.getCityList()
  },
  methods: {
    getData(id) {
      getDetail({ id: this.id }).then((response) => {
        this.isSave = false
        this.agentInfo = response
        this.userStatus.id = this.agentInfo.id
        this.userStatus.areaId = this.agentInfo.areaId
        this.agentInfo.backUrl = [{ url: this.agentInfo.backUrl }]
        this.agentInfo.frontUrl = [{ url: this.agentInfo.frontUrl }]
      })
    },
    getCityList() {
      areaList({ type: 2 }).then((response) => {
        this.cityData = response
      })
    },
    handleAuth() {
      this.dialogStatusInfoVisible = true
      this.resetTemp()
    },
    handleSave() {
      const params = {
        backUrl: this.agentInfo.backUrl[0].url,
        frontUrl: this.agentInfo.frontUrl[0].url,
        id: this.id
      }
      save(params).then((response) => {
        this.isSave = false
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
      })
    },
    handleStatusData() {
      if (this.userStatus.status === 0 && this.userStatus.reason === '') {
        this.$message({
          type: 'warning',
          message: '请填写审核原因～'
        })
        return
      }
      this.$refs['userStatusDataForm'].validate((valid) => {
        if (valid) {
          this.$confirm('此操作将审核用户状态, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            audit(this.userStatus).then(() => {
              this.dialogStatusInfoVisible = false
              this.getData(this.id)
              this.bus.$emit('onUpdateList')
              this.$message({
                type: 'success',
                message: '操作成功!'
              })
            })
          })
        }
      })
    },
    handlefrontSuccess(response) {
      console.log(response, 448)
      this.agentInfo.frontUrl = [{ url: response.data }]
    },
    handlebackSuccess(response) {
      this.agentInfo.backUrl = [{ url: response.data }]
    },
    handleRemoveUrls(file, type) {
      this.$confirm('此操作将删除信息照片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (type === 'front') {
          this.$set(this.agentInfo, 'frontUrl', [])
        } else {
          this.$set(this.agentInfo, 'backUrl', [])
        }
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['userStatusDataForm'].clearValidate()
        this.userStatus = {
          id: this.agentInfo.id,
          areaId: this.agentInfo.areaId
        }
      })
    },
    handlePreview(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handleDownload(file) {
      this.downloadIamge(file, new Date().getTime())
    },
    handleBeforeUpload(file) {
      const isLt = file.size / 1024 < 500
      if (!isLt) {
        this.$message({
          message: '上传文件大小不能超过 5MB!',
          type: 'warning'
        })
      }
      return isLt
    },
    downloadIamge(imgsrc, name) {
      //下载图片地址和图片名
      var image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function() {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var url = canvas.toDataURL('image/png') //得到图片的base64编码数据
        var a = document.createElement('a') // 生成一个a元素
        var event = new MouseEvent('click') // 创建一个单击事件
        a.download = name || 'photo' // 设置图片名称
        a.href = url // 将生成的URL设置为a.href属性
        a.dispatchEvent(event) // 触发a的单击事件
      }
      image.src = imgsrc
    }
  }
}
</script>
<style scoped>
/deep/.el-upload-list__item.is-ready {
  display: none;
}
/deep/ .el-upload-list__item {
  transition: none !important;
}
</style>

