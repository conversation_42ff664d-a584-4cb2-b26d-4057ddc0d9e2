<!-- 区域管理（大区管理） -->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.id" class="filter-item" clearable placeholder="大区ID" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.areaName" class="filter-item" maxlength="10" clearable placeholder="大区名称" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.agentName" class="filter-item" clearable placeholder="大区经理" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.agentPhone" class="filter-item" clearable placeholder="经理电话" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-permission="permissionCode.add" type="primary" icon="el-icon-plus" @click="handleAdd">添加</el-button>
    </div>
    <el-table :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="大区ID" prop="id" align="center" />
      <el-table-column label="大区名称" prop="areaName" align="center" />
      <el-table-column label="大区经理" prop="agentName" align="center" />
      <el-table-column label="经理电话" prop="agentPhone" align="center" />
      <el-table-column label="下属省区" prop="cityName" align="center" width="200" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <span v-for="(item, index) in row.childCity" :key="index">

            {{ item.name }}<span v-show="row.childCity.length > 1 && (row.childCity.length - 1) !== index">、</span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="150">
        <template slot-scope="{row}">
          <el-button type="primary" @click="handleDetail(row,'detail')">查看</el-button>
          <el-button v-permission="permissionCode.edit" type="primary" @click="handleDetail(row,'update')">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" :close-on-click-modal="false" top="2vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="大区名称:" prop="areaName">
          <el-input v-model="form.areaName" :disabled="dialogStatus === 'detail'" maxlength="10" placeholder="请输入大区名称" class="inputW" />
        </el-form-item>
        <el-form-item label="下属省份:" prop="cityIds">
          <el-cascader v-model="form.cityIds" :disabled="dialogStatus === 'detail'" :options="provinceOptions" :props="props" class="inputW" @change="handleChange"></el-cascader>

        </el-form-item>
        <el-form-item label="大区经理:">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            :disabled="dialogStatus === 'detail'"
            reserve-keyword
            placeholder="请输入经纪人姓名、id、手机号进行搜素"
            :remote-method="remoteMethod"
            :loading="loading"
            value-key="id"
            class="inputW"
            clearable
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="(item) in managerOptions" :key="item.id" :label="item.name" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <div class="sameText">ID: {{ form.agentId }}</div>
          <div class="sameText">姓名: {{ form.agentName }}</div>
          <div class="sameText">手机号: {{ form.agentPhone }}</div>
          <div class="sameText">身份证: {{ form.cardNo }}</div>
        </el-form-item>
      </el-form>
      <div v-if="dialogStatus!=='detail'" slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="updateData()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getListData, saveArea, findCity, findAreaDetails } from '@/api/agent/district/region'
import { findAgent } from '@/api/agent/district/province'

import waves from '@/directive/waves'

export default {
  name: 'RegionList',
  directives: { waves },
  components: {},
  filters: {},

  data() {
    return {
      permissionCode: {
        add: ['agent:area:save'],
        edit: ['agent:area:save'],
        see: ['agent:area:get']
      },
      props: {
        multiple: true,
        value: 'id',
        label: 'name',
        children: 'children'
      },
      list: [],
      rules: {
        areaName: [
          { required: true, message: '请输入大区名称', trigger: 'blur' }
        ],
        cityIds: [
          { required: true, message: '请选择省份', trigger: 'change' }
        ]
      },
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '详情',
        update: '编辑',
        create: '新增'
      },
      form: {
        id: '',
        cityIds: [],
        areaName: '',
        agentId: '',
        agentName: '',
        agentPhone: '',
        cardNo: ''
      },
      searchTxt: '',
      provinceOptions: [],
      managerOptions: [],
      loading: false
    }
  },
  created() {
    this.handleFilter()
  },

  activated() {
  },

  mounted() {
  },

  methods: {
    // 获取数据
    getList() {
      getListData(this.listQuery).then(response => {
        if (response) {
          this.list = response.list || []
          this.total = response.totalCount
        }
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.form = {
          id: '',
          cityIds: [],
          areaName: '',
          agentId: '',
          agentName: '',
          agentPhone: '',
          cardNo: ''
        }
        this.searchTxt = ''
        this.managerOptions = []
        this.$refs['form'].resetFields()
        this.$refs['form'].clearValidate()
      })
    },
    // 查询城市
    findCityFun(id) {
      findCity({
        areaId: id
      }).then((res) => {
        this.provinceOptions = res
      })
    },
    // 新增
    handleAdd() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
      this.findCityFun('')
    },
    updateData() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // eslint-disable-next-line no-self-assign
          this.dialogStatus === 'create' ? this.form.id = undefined : this.form.id = this.form.id
          this.form.cityIds = Array.prototype.concat.apply([], this.form.cityIds)
          saveArea(this.form).then(() => {
            this.dialogFormVisible = false
            if (this.dialogStatus === 'create') {
              this.handleFilter()
            } else {
              this.getList()
            }
            this.$message.success('保存成功！')
          })
        }
      })
    },
    // 查询详情
    findAreaDetailsFun(row) {
      findAreaDetails({
        id: row.id
      }).then((res) => {
        console.log(res)
        this.form.id = res.id
        this.form.agentId = res.agentId
        this.form.agentName = res.agentName
        this.form.agentPhone = res.agentPhone
        this.form.areaName = res.areaName
        this.form.cardNo = res.cardNo
        this.searchTxt = res.agentName
        const childCityArr = []
        res.childCity.forEach((item, index) => {
          const arr = []
          arr.push(item.id)
          childCityArr.push(arr)
        })
        this.form.cityIds = childCityArr
      })
    },
    // 编辑 查看
    handleDetail(row, type) {
      this.dialogStatus = type
      this.dialogFormVisible = true
      this.resetTemp()
      this.findCityFun(row.id)
      setTimeout(() => {
        this.findAreaDetailsFun(row)
      }, 100)
      console.log(this.form.cityIds)
    },
    handleChange(value) {
    },
    changeSelect(value) {
      console.log(value)
      this.form.agentId = value.id
      this.form.agentName = value.name
      this.form.agentPhone = value.phone
      this.form.cardNo = value.cardNo
    },
    clearSelect() {
      this.form.agentId = ''
      this.form.agentName = ''
      this.form.agentPhone = ''
      this.form.cardNo = ''
    },
    // 大区经理搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        findAgent({
          searchTxt: query
        }).then((res) => {
          console.log(res)
          this.managerOptions = res || []
          this.loading = false
        })
      } else {
        this.managerOptions = []
      }
    }
  }
}
</script>

<style scoped>
.inputW {
  width: 250px;
}
.sameText {
  margin-bottom: 15px;
}
</style>
