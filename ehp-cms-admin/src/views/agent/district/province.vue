<!-- 区域管理（省区管理） -->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.areaId" class="filter-item" clearable placeholder="省区ID" style="width: 150px" @input="areaInput" />
      <el-input v-model="listQuery.areaName" class="filter-item" clearable placeholder="省区名称" maxlength="10" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.id" placeholder="请选择省区" clearable @change="handleSelect">
        <el-option v-for="item in areaList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-input v-model="listQuery.agentName" class="filter-item" clearable placeholder="经理姓名" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.agentPhone" class="filter-item" clearable placeholder="经理电话" style="width: 150px" @keyup.enter.native="handleFilter" />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
    </div>
    <el-table :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="省区ID" prop="id" align="center" />
      <el-table-column label="省区名称" prop="areaName" align="center" />
      <el-table-column label="省份" prop="cityName" align="center" />
      <el-table-column label="经理姓名" prop="agentName" align="center" />
      <el-table-column label="经理电话" prop="agentPhone" align="center" />
      <el-table-column label="所属大区" prop="parentAreaName" align="center" />
      <el-table-column label="操作" fixed="right" align="center">
        <template slot-scope="{row}">
          <el-button v-permission="permissionCode.edit" type="primary" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog title="编辑" :visible.sync="dialogFormVisible" :close-on-click-modal="false" top="2vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="省区名称:" prop="areaName">
          <el-input v-model="form.areaName" maxlength="10" placeholder="请输入大区名称" class="inputW" />
        </el-form-item>
        <el-form-item label="省区经理:">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            reserve-keyword
            placeholder="请输入经纪人姓名、id、手机号进行搜素"
            :remote-method="remoteMethod"
            :loading="loading"
            clearable
            value-key="id"
            class="inputW"
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="item in managerOptions" :key="item.id" :label="item.name" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <div class="sameText">ID: {{ form.agentId }}</div>
          <div class="sameText">姓名: {{ form.agentName }}</div>
          <div class="sameText">手机号: {{ form.agentPhone }}</div>
          <div class="sameText">身份证: {{ form.cardNo }}</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="updateData()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getListData, findAllArea, fetchAreaDetails, saveAreaDate, findAgent } from '@/api/agent/district/province'
import waves from '@/directive/waves'
export default {
  name: 'ProvinceList',
  directives: { waves },
  components: { },
  filters: {},

  data() {
    return {
      list: [],
      permissionCode: {
        edit: ['agent:area:city:save'],
        see: ['agent:area:city:get']
      },
      rules: {
        areaName: [
          { required: true, message: '请输入大区名称', trigger: 'blur' }
        ]
      },
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      areaList: [],
      dialogFormVisible: false,
      form: {
        id: '',
        areaName: '',
        agentId: '',
        agentName: '',
        agentPhone: '',
        cardNo: ''
      },
      searchTxt: '',
      managerOptions: [],
      loading: false
    }
  },
  created() {
    this.handleFilter()
    this.findAllAreaFun()
  },

  activated() {
  },

  mounted() {
  },

  methods: {
    areaInput() {
      const areaList = this.areaList.map((item) => {
        return item.id
      })
      if (areaList.includes(Number(this.listQuery.areaId))) {
        this.listQuery.id = Number(this.listQuery.areaId)
      } else {
        this.listQuery.id = this.listQuery.areaId
      }
    },
    handleSelect(value) {
      this.listQuery.areaId = value
      this.handleFilter()
    },
    // 获取数据
    getList() {
      getListData(this.listQuery).then(response => {
        if (response) {
          this.list = response.list
          this.total = response.totalCount
        }
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    // 查询城市列表
    findAllAreaFun() {
      findAllArea().then((res) => {
        this.areaList = res
      })
    },
    changeSelect(value) {
      console.log(value)
      this.form.agentId = value.id
      this.form.agentName = value.name
      this.form.agentPhone = value.phone
      this.form.cardNo = value.cardNo
    },
    clearSelect() {
      this.form.agentId = ''
      this.form.agentName = ''
      this.form.agentPhone = ''
      this.form.cardNo = ''
    },
    // 编辑
    handleEdit(row) {
      this.dialogFormVisible = true
      this.managerOptions = []
      fetchAreaDetails({
        id: row.id
      }).then((res) => {
        console.log(res)
        this.form.id = res.id
        this.form.agentId = res.agentId
        this.form.agentName = res.agentName
        this.form.agentPhone = res.agentPhone
        this.form.areaName = res.areaName
        this.form.cardNo = res.cardNo
        this.searchTxt = res.agentName
      })
    },
    // 编辑经纪人
    updateData() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          saveAreaDate(this.form).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.$message.success('保存成功！')
          })
        }
      })
    },
    // 省区经理搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        findAgent({
          searchTxt: query
        }).then((res) => {
          this.managerOptions = res || []
          this.loading = false
        })
      } else {
        this.managerOptions = []
      }
    }
  }
}
</script>

<style scoped>
.inputW {
  width: 250px;
}
.sameText {
  margin-bottom: 15px;
}
</style>
