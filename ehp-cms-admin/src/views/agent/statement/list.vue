<!-- 大区营业报表 -->
<template>
  <div class="app-container">
    <div class="filter-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="item">
            <div class="top">
              <img class="icon" src="@/assets/images/ic_order.png" alt="">
              <div>
                <div class="same">订单数量</div>
                <div class="num">{{ statisticsData.orderTotal?statisticsData.orderTotal:0 }}</div>
              </div>
            </div>
            <div class="num">昨日：+ {{ statisticsData.yesterdayAddOrder?statisticsData.yesterdayAddOrder:0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="item">
            <div class="top">
              <img class="icon" src="@/assets/images/ic_money.png" alt="">
              <div>
                <div class="same">订单总金额</div>
                <div class="num">{{ statisticsData.orderPriceTotal?statisticsData.orderPriceTotal:0 }}</div>
              </div>
            </div>
            <div class="num">昨日：+ ￥{{ statisticsData.yesterdayAddOrderPrice?statisticsData.yesterdayAddOrderPrice:0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="item">
            <div class="top">
              <img class="icon" src="@/assets/images/ic_prescription.png" alt="">
              <div>
                <div class="same">处方数量</div>
                <div class="num">{{ statisticsData.recipelTotal?statisticsData.recipelTotal:0 }}</div>
              </div>
            </div>
            <div class="num">昨日：+ {{ statisticsData.yesterdayAddRecipel?statisticsData.yesterdayAddRecipel:0 }}</div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="item">
            <div class="top">
              <img class="icon" src="@/assets/images/ic_doctor.png" alt="">
              <div>
                <div class="same">邀请医生人数</div>
                <div class="num">{{ statisticsData.inviteTotal?statisticsData.inviteTotal:0 }}</div>
              </div>
            </div>
            <div class="num">昨日：+ {{ statisticsData.yesterdayAddInvite?statisticsData.yesterdayAddInvite:0 }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <el-table :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="大区ID" prop="id" align="center" />
      <el-table-column label="大区名称" prop="areaName" align="center" />
      <el-table-column label="大区经理" prop="areaLeader" align="center" />
      <el-table-column label="经理电话" prop="leaderPhone" align="center" />
      <el-table-column label="下属省区" prop="childCity" align="center" />
      <el-table-column label="大区总人数" prop="areaTotalNumber" align="center" />
      <el-table-column label="下属医生总人数" prop="childDoctorTotal" align="center" />
      <el-table-column label="总处方数量" prop="recipelTotal" align="center" />
      <el-table-column label="总订单金额￥" prop="orderPriceTotal" walign="center" />
      <el-table-column label="操作" fixed="right" align="center">
        <template slot-scope="{row}">
          <el-button type="primary" @click="handleDetail(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog title="详情/报表" :visible.sync="dialogFormVisible" :close-on-click-modal="false" top="2vh" width="70%">
      <el-tabs v-model="activeName">
        <el-tab-pane label="大区详情" name="first">
          <el-form ref="form" :model="form" label-width="80px">
            <el-form-item label="大区ID:">
              <div>{{ detail.id }}</div>
            </el-form-item>
            <el-form-item label="大区名称:">
              <div>{{ detail.areaName }}</div>
            </el-form-item>
            <el-form-item label="创建时间:">
              <div>{{ detail.createdAt }}</div>
            </el-form-item>
            <el-form-item label="大区经理:">
              <div>{{ detail.leaderName?detail.leaderName:'暂无' }}</div>
            </el-form-item>
            <el-form-item label="手机号:">
              <div>{{ detail.leaderPhone?detail.leaderPhone:'暂无' }}</div>
            </el-form-item>
            <el-form-item label="身份证:">
              <div>{{ detail.cardNo?detail.cardNo:'暂无' }}</div>
            </el-form-item>
            <el-form-item label="下属省区:">
              <el-table :data="detail.childCityList" fit highlight-current-row style="width: 100%;">
                <el-table-column label="省区ID" prop="id" align="center" />
                <el-table-column label="省区名称" prop="name" align="center" />
                <el-table-column label="省区经理" prop="leader" align="center" />
                <el-table-column label="经理电话" prop="leaderPhone" align="center" />
                <el-table-column label="身份证信息" prop="cardNo" align="center" />
              </el-table>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="大区报表" name="second">
          <div class="regionTop">
            <ReportExportTime @onPickerChange="onPickerChange"></ReportExportTime>
            <el-button v-permission="['area:report:export']" type="primary" @click="downloadAll">下载报表</el-button>
            <el-button type="primary" @click="provincesChange(btnText)">{{ btnText }}</el-button>
          </div>
          <div v-show="regionListShow">
            <el-table :data="regionList" fit highlight-current-row style="width: 100%;" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="日期" prop="reportDate" align="center" />
              <el-table-column label="大区名称" prop="areaName" align="center" />
              <el-table-column label="下属省区" prop="childCity" align="center" />
              <el-table-column label="大区经理" prop="leaderName" align="center" />
              <el-table-column label="经理电话" prop="leaderPhone" align="center" />
              <el-table-column label="下属经纪总人数" prop="childAgentTotal" align="center" />
              <el-table-column label="下属医生总人数" prop="childDoctorTotal" align="center" />
              <el-table-column label="总处方数量" prop="recipelTotal" align="center" />
              <el-table-column label="总订单金额￥" prop="orderPriceTotal" walign="center" />
              <el-table-column label="操作" fixed="right" align="center">
                <template slot-scope="{row}">
                  <el-button v-permission="['area:report:export']" type="primary" @click="downloadRow(row)">下载报表</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="regiontotal>0"
              :total="regiontotal"
              :page.sync="Params.pageNo"
              :limit.sync="Params.pageSize"
              @pagination="getTableList"
            />
          </div>
          <div v-show="proListShow">
            <el-table :data="proList" fit highlight-current-row style="width: 100%;" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="日期" prop="reportDate" align="center" />
              <el-table-column label="大区名称" prop="areaName" align="center" />
              <el-table-column label="省区名称" prop="cityName" align="center" />
              <el-table-column label="省区经理" prop="cityLeader" align="center" />
              <el-table-column label="经理电话" prop="leaderPhone" align="center" />
              <el-table-column label="下属经纪总人数" prop="childAgentTotal" align="center" />
              <el-table-column label="下属医生总人数" prop="childDoctorTotal" align="center" />
              <el-table-column label="总处方数量" prop="recipelTotal" align="center" />
              <el-table-column label="总订单金额￥" prop="orderPriceTotal" walign="center" />
              <el-table-column label="操作" fixed="right" align="center">
                <template slot-scope="{row}">
                  <el-button type="primary" @click="downloadRow(row)">下载报表</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="prototal>0"
              :total="prototal"
              :page.sync="Params.pageNo"
              :limit.sync="Params.pageSize"
              @pagination="getTableList"
            />
          </div>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStatistics, getList, get, getTableList, downloadExport } from '@/api/agent/statement'
import { getToken, getTokenName } from '@/utils/auth'
import waves from '@/directive/waves'
import ReportExportTime from '../components/ReportExportTime'

export default {
  name: 'StatementList',
  directives: { waves },
  components: { ReportExportTime },
  filters: {},

  data() {
    return {
      statisticsData: {},
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogFormVisible: false,
      activeName: 'first',
      detail: {},
      form: {},
      provinceList: null,
      Params: {
        pageNo: 1,
        pageSize: 10,
        areaId: '',
        reportType: 1, //报告类型：1.年 2.月 3.日
        reportDate: '', //年月日val yyyy-MM-dd格式
        type: 1 //类型：1.大区 2.省区
      },
      regiontotal: 0,
      prototal: 0,
      regionList: [],
      proList: [],
      btnText: '切换至省区',
      regionListShow: true,
      proListShow: false,
      multipleSelection: [], //大区报告日期 / 省区报告日期
      areaIds: [] //大区Id / 省区id
    }
  },
  created() {
    this.getStatistics()
    this.getList()
  },

  methods: {
    // 全平台数据统计
    getStatistics() {
      getStatistics().then(res => {
        this.statisticsData = res
      })
    },
    // 获取营业报表数据
    getList() {
      getList(this.listQuery).then(res => {
        this.list = res.list
        this.total = res.totalCount
      })
    },
    //查看
    handleDetail(row) {
      this.activeName = 'first'
      this.Params.areaId = row.id
      this.dialogFormVisible = true
      this.getTableList()
      const params = {
        areaId: row.id
      }
      get(params).then(res => {
        this.detail = res
      })
    },
    //大区报表多选
    handleSelectionChange(value) {
      this.areaIds = []
      this.multipleSelection = []
      const arr = value
      if (arr.length > 0) {
        for (var i in arr) {
          this.multipleSelection.push(arr[i].reportDate)
          this.areaIds.push(arr[i].id)
        }
      }
      console.log(this.areaIds)
      console.log(this.multipleSelection)
    },
    // 下载报表
    downloadAll() {
      const params = {
        areaIds: this.areaIds.join(','),
        type: this.Params.type,
        reportType: this.Params.reportType,
        reportDates: this.multipleSelection.join(',')
      }
      params[getTokenName()] = getToken()
      console.log(params, 'paramsAll')
      const url = downloadExport(params)
      window.open(url)
      // this.multipleSelection = []
      // this.areaIds = []
      // downloadExport(params, { reportDates: this.multipleSelection }).then(res => {
      //   console.log(res)
      //   this.$message.success('下载成功！')
      //   this.multipleSelection = []
      // })
    },
    downloadRow(row) {
      const params = {
        areaIds: row.id,
        type: this.Params.type,
        reportType: this.Params.reportType,
        reportDates: row.reportDate
      }
      params[getTokenName()] = getToken()
      console.log(params, 'paramsRow')
      const url = downloadExport(params)
      window.open(url)
    },
    //年月日筛选
    onPickerChange(data) {
      this.Params.reportType = data.type
      this.Params.reportDate = data.date
      this.getTableList()
    },
    // 切换 大区/省区
    provincesChange(btnText) {
      if (!this.regionListShow) {
        this.btnText = '切换至省区'
        this.Params.type = 1
        this.getTableList()
      } else {
        this.btnText = '切换至大区'
        this.Params.type = 2
        this.getTableList()
      }
      this.regionListShow = !this.regionListShow
      this.proListShow = !this.proListShow
    },
    //获取(大区-省区)table数据
    getTableList() {
      getTableList(this.Params).then(response => {
        if (this.Params.type === 1) {
          this.regionList = response.list
          this.regiontotal = response.totalCount
          this.areaIds = response.list.map(item => item.id)
          this.multipleSelection = response.list.map(item => item.reportDate)
        } else {
          this.proList = response.list
          this.prototal = response.totalCount
          this.areaIds = response.list.map(item => item.id)
          this.multipleSelection = response.list.map(item => item.reportDate)
        }
      })
    }
  }
}
</script>

<style scoped>
.item{
  width: 100%;
  height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.top{
  display: flex;
  margin-bottom: 30px;
}
.icon{
  margin-right: 20px;
  width: 70px;
  height: 70px;
}
.same{
  margin-top: 10px;
  margin-bottom: 10px;
}
.num{
  width: 100%;
  text-align: center;
  font-weight: 600;
}
.regionTop{
  display: flex;
  margin-top: 15px;
  margin-bottom: 20px;
}
</style>
