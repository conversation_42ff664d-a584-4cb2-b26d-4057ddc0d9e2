<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.skuNumber"
        clearable
        class="filter-item"
        placeholder="商品SKU码"
        style="width: 150px"
        type="textarea"
        :rows="4"
      />
      <el-input
        v-model="listQuery.skuName"
        clearable
        class="filter-item"
        placeholder="商品名称"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        style="vertical-align: top;"
        @click="handleFilter"
      >搜索</el-button>
    </div>
    <div v-for="item in list.list" :key="item.skuId" class="r_list">
      <el-row :gutter="20" class="middle color_6">
        <el-col :span="7">
          <div>商品SKU码: {{ item.skuNumber }}</div>
        </el-col>
        <el-col :span="11">
          <div>商品名称: {{ item.skuName }}</div>
        </el-col>
        <el-col :span="4">
          <div>总可用库存: {{ item.totalQuantity }}</div>
        </el-col>
      </el-row>
      <div class="middle_1 color_6">库存信息:</div>
      <div
        class="middle_1 color_6"
      >总可用库存 = 总实物库存:{{ item.totalPhysicalQuantity }} - 总占用库存:{{ item.totalTakeUpQuantity }} - 预占库存:{{ item.totalWillQuantity }} = {{ item.totalQuantity }}</div>
      <br />
      <el-table
        :key="tableKey"
        :data="item.inventors"
        fit
        highlight-current-row
        style="width: 100%"
      >
        <el-table-column label="仓库名" prop="warehouseName" align="center"></el-table-column>
        <el-table-column label="实物库存" prop="physicalQuantity" width="150px" align="center"></el-table-column>
        <el-table-column label="预占库存" prop="willQuantity" width="150px" align="center" />
        <el-table-column label="占用库存" prop="takeUpQuantity" width="150px" align="center" />
        <el-table-column label="可用库存" prop="totalQuantity" width="150px" align="center" />
        <el-table-column label="是否冻结" prop="statusDescribe" width="150px" align="center">
          <template slot-scope="{row}">
            <el-tag
              size="small"
              :type="row.status === 0 ? 'success' : 'danger'"
            >{{ row.statusDescribe }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="list.list.length==0" class="nodata color_6">暂无数据</div>
    <pagination
      v-show="list.totalCount > 0"
      :total="list.totalCount"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<style type="text/css" scoped>
.middle {
  padding: 20px 0;
  border-bottom: 1px solid #dcdfe6;
}
.middle_1 {
  padding: 10px 0 0 0;
}
.color_6 {
  color: #606266;
}
.r_list {
  border: 2px solid #aaa;
  border-radius: 4px;
  padding: 20px;
  margin-top: 20px;
}
.nodata {
  border-top: 1px solid #999;
  padding: 20px;
  margin-top: 20px;
  text-align: center;
}
</style>
<script>
import { getList } from '@/api/stock/index'
import waves from '@/directive/waves'
export default {
  name: '',
  directives: { waves },
  filters: {},
  components: {},
  data() {
    return {
      list: {
        list: [],
        pageNo: 1,
        totalCount: 1
      },
      data: {},
      tableKey: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        if (response) {
          this.list = response
        }
      })
    },
    handleFilter() {
      this.getList()
    },
    handleAudit(id) {
      this.$router.push({
        path: './info/' + id
      })
    }
  }
}
</script>
