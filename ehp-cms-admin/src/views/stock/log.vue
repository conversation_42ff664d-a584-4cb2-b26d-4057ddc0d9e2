<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.skuNumber"
        clearable
        placeholder="商品SKU码"
        style="width: 150px"
        type="textarea"
        :rows="4"
      />
      <el-input
        v-model="listQuery.skuName"
        clearable
        placeholder="商品名称"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        clearable
        placeholder="单据编号"
        style="width: 150px;vertical-align: top;"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        style="vertical-align: top;"
        @click="handleFilter"
      >查询</el-button>
    </div>
    <el-table :key="tableKey" :data="list.list" fit highlight-current-row style="width: 100%">
      <el-table-column label="时间" prop="date" width="140px" align="center" />
      <el-table-column label="单据编号" prop="orderSn" width="140px" align="center" />
      <el-table-column label="商品SKU" prop="skuNumber" width="120px" align="center" />
      <el-table-column label="商品名称" prop="skuName" width="180px" align="center" />
      <el-table-column label="业务类型" prop="businessTypeDescribe" width="100px" align="center" />
      <el-table-column label="变更数量" prop="quantity" width="80px" align="center" />
      <el-table-column label="药店名称" prop="warehouseName" width="100px" align="center" />
      <el-table-column label="药店编号" prop="warehouseCode" width="90px" align="center" />
      <el-table-column label="原始实物库存" prop="beforePhysicalQuantity" width="100px" align="center" />
      <el-table-column label="实物库存结存" prop="afterPhysicalQuantity" width="100px" align="center" />
      <el-table-column label="原始预占库存" prop="beforeWillQuantity" width="100px" align="center" />
      <el-table-column label="预占库存结存" prop="afterWillQuantity" width="100px" align="center" />
      <el-table-column label="原始占用库存" prop="beforeTakeUpQuantity" width="100px" align="center" />
      <el-table-column label="占用库存结存" prop="afterTakeUpQuantity" width="100px" align="center" />
    </el-table>
    <pagination
      v-show="list.totalCount > 0"
      :total="list.totalCount"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getWaterList } from '@/api/stock/index'
import waves from '@/directive/waves'
export default {
  name: '',
  directives: { waves },
  filters: {},
  components: {},
  data() {
    return {
      list: {
        list: [],
        pageNo: 1,
        totalCount: 1
      },
      tableKey: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 20
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      getWaterList(this.listQuery).then(response => {
        if (response) {
          this.list = response
        }
      })
    },
    handleFilter() {
      this.getList()
    }
  }
}
</script>
