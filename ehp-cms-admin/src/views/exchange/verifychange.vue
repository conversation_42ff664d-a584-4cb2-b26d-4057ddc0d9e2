<template>
  <div class="app-container">
    <changeDetail c-type="verify" :order-id="orderId" />
  </div>
</template>
<script>
import changeDetail from './components/changeDetail'
export default {
  name: 'Retreat',
  components: {
    changeDetail
  },
  data() {
    return {
      orderId: null
    }
  },
  created() {
    if (this.$route.params.orderId) {
      this.orderId = this.$route.params.orderId
    }
  },
  methods: {

  }
}
</script>
