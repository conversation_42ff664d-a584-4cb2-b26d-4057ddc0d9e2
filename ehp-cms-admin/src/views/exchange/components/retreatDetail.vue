<template>
  <div>
    <el-form
      ref="dataForm"
      :model="retreatData"
      :rules="rules"
      :inline="true"
      label-width="150px"
      class="demo-form-inline"
    >
      <div v-if="cType=='edit' || cType=='verify' || cType=='detail'">
        <el-row>
          <el-col :span="12" class="ctitle">退货订单:{{ retreatData.orderSn }}</el-col>
          <el-col :span="12" class="ctitle">状态:{{ retreatData.statusDescribe }}</el-col>
        </el-row>
        <el-divider></el-divider>
      </div>
      <div class="ctitle">订单信息</div>
      <div class="filter-container">
        <el-form-item label="" prop="orderSn">
          <el-input
            v-if="cType=='add'"
            v-model="retreatData.orderSn"
            class="elinput"
            placeholder="销售订单"
            style="width: 150px"
            @focus="checkOrder"
          />
          <el-input
            v-else
            v-model="retreatData.orderSn"
            class="elinput"
            placeholder="销售订单"
            disabled="true"
            style="width: 150px"
          />
        </el-form-item>
        <el-input
          v-model="retreatData.orderInfoVO.createdAt"
          class="elinput"
          placeholder="制单日期"
          disabled="true"
          style="width: 150px"
        />
        <el-input
          v-model="retreatData.orderInfoVO.patientName"
          class="elinput"
          placeholder="患者姓名"
          disabled="true"
          style="width: 150px"
        />
        <el-input
          v-model="retreatData.orderInfoVO.receiver"
          class="elinput"
          placeholder="收货人"
          disabled="true"
          style="width: 150px"
        />
        <el-input
          v-model="retreatData.orderInfoVO.phone"
          class="elinput"
          placeholder="手机号"
          disabled="true"
          style="width: 150px"
        />
        <el-select
          v-model="retreatData.orderInfoVO.logisticsCompany"
          class="elinput"
          placeholder="物流公司"
          disabled="true"
        >
          <el-option v-for="item in logisticsOption" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <el-input
          v-model="retreatData.orderInfoVO.deliveryId"
          class="elinput"
          placeholder="物流单号"
          disabled="true"
          style="width: 150px"
        />
      </div>
      <el-divider></el-divider>
      <div class="ctitle">退货单信息</div>
      <div class="filter-container">
        <el-form-item label="" prop="warehouseId">
          <el-select
            v-model="retreatData.warehouseId"
            class="elinput"
            placeholder="请选择药店"
            :disabled="cType=='detail' || cType=='verify'"
            clearable
          >
            <el-option v-for="item in selectOption" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="returnReason">
          <DictSelect
            v-model="retreatData.returnReason"
            class="elinput"
            placeholder="退换货原因"
            type="return-goods-order-reason"
            :disabled="cType=='detail' || cType=='verify'"
            style="width: 150px;"
          />
        </el-form-item>
        <el-form-item label="" prop="logisticsCompanyId">
          <el-select
            v-model="retreatData.logisticsCompanyId"
            class="elinput"
            placeholder="退回物流公司"
            :disabled="cType=='detail' || cType=='verify'"
          >
            <el-option v-for="item in logisticsOption" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="deliveryId">
          <el-input
            v-model="retreatData.deliveryId"
            clearable
            class="elinput"
            placeholder="退回物流单号"
            :disabled="cType=='detail' || cType=='verify'"
            style="width: 150px"
          />
        </el-form-item>
      </div>
      <el-divider></el-divider>
      <div class="ctitle">退入商品</div>
      <el-table
        :key="tableKey"
        :data="retreatData.returnProducts"
        fit
        highlight-current-row
      >
        <el-table-column label="商品SKU码" prop="skuNumber" align="center" />
        <el-table-column label="商品名称" prop="skuName" align="center" />
        <el-table-column v-if="cType=='add' || cType=='edit'" label="数量" prop="quantity" align="center">
          <template slot-scope="scope">
            <div><el-input-number v-model="retreatData.returnProducts[scope.$index].quantity" :min="1" :max="retreatData.returnProducts[scope.$index].oldQuantity" label="请输入数量" :step="1"></el-input-number></div>
          </template>
        </el-table-column>
        <el-table-column v-else label="数量" prop="quantity" align="center" />
        <el-table-column label="实际单价" prop="salePrice" align="center" />
      </el-table>
      <div class="ctitle beiz">备注信息：</div>
      <el-input
        v-model="retreatData.remark"
        type="textarea"
        width="50%"
        placeholder="备注"
        :maxlength="100"
        :disabled="cType=='detail' || cType=='verify'"
      ></el-input>
      <div v-if="cType=='edit' || cType=='verify' || cType=='detail'" class="ctitle beiz">日志信息：</div>
      <el-table
        v-if="cType=='edit' || cType=='verify' || cType=='detail'"
        :key="tableKey2"
        :data="retreatData.operateLogs"
        fit
        highlight-current-row
      >
        <el-table-column label="操作人" prop="createdBy" align="center" />
        <el-table-column label="操作记录" prop="operate" align="center" />
        <el-table-column label="操作时间" prop="createdAt" align="center" />
      </el-table>
    </el-form>
    <div v-if="cType=='edit' || cType=='add'" class="btncomm">
      <el-button
        type="primary"
        @click="saveOrder"
      >保存</el-button>
      <el-button
        type="primary"
        @click="saveSubOrder"
      >保存并提交</el-button>
      <el-button
        type="primary"
        @click="back"
      >取消</el-button>
    </div>
    <div v-if="cType=='detail'" class="btncomm">
      <el-button
        type="primary"
        @click="back"
      >返回</el-button>
    </div>
    <div v-if="cType=='verify'" class="btncomm">
      <el-button
        type="primary"
        @click="verifyOrder"
      >审核</el-button>
      <el-button
        type="primary"
        @click="back"
      >返回上一页</el-button>
    </div>
    <el-dialog title="订单选择" :visible.sync="dialogFormVisible" width="80%">
      <div class="filter-container">
        <el-input
          v-model="listQuery.userId"
          clearable
          class="filter-item"
          placeholder="用户ID"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.phone"
          clearable
          class="filter-item"
          placeholder="手机号"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.orderSn"
          clearable
          class="filter-item"
          placeholder="订单号"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <DictSelect
          v-model="listQuery.orderStatus"
          placeholder="请选择"
          class="filter-item"
          style="width: 120px"
          type="order_status"
          @change="handleFilter"
        />
        <DatePicker
          :query-model="listQuery"
          class="filter-item"
          style="width: 230px"
          @change="handleFilter"
        />
        <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      </div>
      <el-table
        v-if="cType=='add'"
        :key="tableKey"
        :data="orderList"
        highlight-current-row
      >
        <el-table-column fixed label="选择" width="90">
          <template slot-scope="scope">
            <el-radio v-model="radio" :label="scope.row.id" :value="scope.row.id" @change.native="getCurrentRow(scope.row)"></el-radio>
          </template>
        </el-table-column>
        <el-table-column label="订单号" prop="orderSn" width="140px" align="center" />
        <el-table-column label="用户ID" prop="userId" width="80px" align="center" />
        <el-table-column label="用户名" prop="userName" width="150px" align="center" />
        <el-table-column label="商品总价" prop="totalAmount" width="80px" align="center" />
        <el-table-column label="邮费" prop="freight" width="80px" align="center" />
        <el-table-column label="优惠券" prop="couponPay" width="80px" align="center" />
        <el-table-column label="实付" prop="realPay" width="80px" align="center" />
        <el-table-column label="订单状态" prop="orderStatusDescribe" width="80px" align="center" />
        <el-table-column label="支付状态" prop="payStatusDescribe" width="80px" align="center" />
        <el-table-column label="订单时间" prop="createdAt" width="140px" align="center" />
        <el-table-column label="支付时间" prop="payTime" width="140px" align="center" />
        <el-table-column label="发货时间" prop="sendTime" width="140px" align="center" />
      </el-table>

      <pagination
        v-if="cType=='add'"
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.pageNo"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="sureOrder">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.ctitle{line-height:30px;font-size:20px;}
.elinput{margin-top: 10px;}
.beiz{margin:10px 0;}
.btncomm{ text-align:center;margin-top:20px; }
</style>
<script>
import {
  getList,
  get
} from '@/api/oms/order'
import api_exchange from '@/api/exchange/exchange'
import api_pharmacy from '@/api/pharmacy/index'
import DictSelect from '@/components/DictSelect'
import waves from '@/directive/waves' // Waves directive
import DatePicker from '@/components/DatePicker'
export default {
  directives: { waves },
  components: {
    DictSelect,
    DatePicker
  },
  props: {
    cType: {
      type: String,
      required: false,
      default: ''
    },
    orderId: {
      type: [Number, String],
      required: false,
      default: ''
    }
  },
  data() {
    return {
      retreatData: {
        orderInfoVO: {},
        returnProducts: [],
        operateLogs: {}
      },
      selectOption: [],
      logisticsOption: [],
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      radio: null,
      orderList: [],
      total: 0,
      orderRadio: {},
      dialogFormVisible: false,
      tableKey: 1,
      list: [],
      tableKey2: 2,
      list2: [],
      rules: {
        orderSn: [{ required: true, message: '请选择销售订单' }],
        warehouseId: [{ required: true, message: '请选择药店' }],
        returnReason: [{ required: true, message: '请选择原因' }]
      }
    }
  },
  created() {
    api_pharmacy.select().then(response => {
      this.selectOption = response
    })
    api_exchange.logistics().then(response => {
      this.logisticsOption = response
    })
    if (this.cType === 'detail' || this.cType === 'edit' || this.cType === 'verify') {
      this.getDetail()
    }
  },
  methods: {
    getDetail() {
      api_exchange.getRetreatDetail(this.orderId).then(response => {
        this.retreatData = response
      }, error => {

      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.orderList = response.list
        this.total = response.totalCount
        this.radio = null
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    saveOrder(dataForm) {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          api_exchange.saveRetreat(this.retreatData).then(response => {
            this.$message({
              message: '成功',
              type: 'success'
            })
            if (this.cType === 'add') {
              this.$router.push({
                path: './goods/list'
              })
            } else {
              this.$router.push({
                path: '../goods/list'
              })
            }
          }, error => {

          })
        }
      })

    },
    saveSubOrder() {
      this.retreatData.submitFlag = 1
      this.saveOrder()
    },
    verifyOrder() {
      this.$confirm('是否审核通过订单？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_exchange.orderAudit([this.orderId]).then(
          response => {
            this.$message({
              message: '成功',
              type: 'success'
            })
            this.$router.push({
              path: '../goods/list'
            })
            // this.getDetail()
          },
          error => {}
        )
      })
    },
    back() {
      this.$router.go(-1)//返回上一层
    },
    checkOrder() {
      this.handleFilter()
      this.dialogFormVisible = true
    },
    sureOrder() {
      // console.log(this.orderRadio)
      if (this.radio) {
        const orderItem = {
          orderSn: this.orderRadio.orderSn,
          createdAt: this.orderRadio.changedAt,
          patientName: this.orderRadio.userName,
          receiver: this.orderRadio.userName,
          phone: this.orderRadio.phone,
          logisticsCompany: this.orderRadio.logisticsCompany,
          deliveryId: this.orderRadio.deliveryId
        }
        var orderId = this.orderRadio.id
        this.$set(this.retreatData, 'orderInfoVO', orderItem)
        this.$set(this.retreatData, 'orderSn', this.orderRadio.orderSn)
        this.$refs['dataForm'].validateField('orderSn')
        // debugger
        // this.refs.dataForm.validateField('orderSn');
        // console.log(this.retreatData)
        this.orderRadio = {}
        this.dialogFormVisible = false
        get(orderId).then(response => {
          // this.retreatData.returnProducts = response
          response.orderItems.forEach(item => {
            item.oldQuantity = item.quantity
          })
          this.$set(this.retreatData, 'returnProducts', response.orderItems)
        })
      } else {
        this.$message({
          message: '请选择订单',
          type: 'warning'
        })
      }
    },
    getCurrentRow(row) {
      this.radio = row.id
      this.orderRadio = row
    }
  }
}
</script>
