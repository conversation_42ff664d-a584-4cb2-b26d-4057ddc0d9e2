<template>
  <div class="app-container">
    <repairDetail c-type="detail" :order-id="orderId" />
  </div>
</template>
<script>
import repairDetail from './components/repairDetail'
export default {
  name: 'Repair',
  components: {
    repairDetail
  },
  data() {
    return {
      orderId: null
    }
  },
  created() {
    if (this.$route.params.orderId) {
      this.orderId = this.$route.params.orderId
    }
  },
  methods: {

  }
}
</script>
