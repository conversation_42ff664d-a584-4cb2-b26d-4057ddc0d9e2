<template>
  <div class="app-container">
    <retreatDetail c-type="verify" :order-id="orderId" />
  </div>
</template>
<script>
import retreatDetail from './components/retreatDetail'
export default {
  name: 'Retreat',
  components: {
    retreatDetail
  },
  data() {
    return {
      orderId: null
    }
  },
  created() {
    if (this.$route.params.orderId) {
      this.orderId = this.$route.params.orderId
    }
  },
  methods: {

  }
}
</script>
