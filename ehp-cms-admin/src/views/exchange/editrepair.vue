<template>
  <div class="app-container">
    <repairDetail c-type="edit" :order-id="orderId" />
  </div>
</template>
<script>
import repairDetail from './components/repairDetail'
export default {
  name: 'Retreat',
  components: {
    repairDetail
  },
  data() {
    return {
      orderId: null
    }
  },
  created() {
    if (this.$route.params.orderId) {
      this.orderId = this.$route.params.orderId
    }
  },
  methods: {
    handleClick(tab, event) {

    },
    handleFilter() {

    }
  }
}
</script>
