<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="退货单" name="first">
        <retreatDetail c-type="add" />
      </el-tab-pane>
      <el-tab-pane label="换货单" name="second">
        <changeDetail c-type="add" />
      </el-tab-pane>
      <el-tab-pane label="补寄单" name="third">
        <repairDetail c-type="add" />
      </el-tab-pane>
    </el-tabs>

  </div>
</template>
<script>
import changeDetail from './components/changeDetail'
import repairDetail from './components/repairDetail'
import retreatDetail from './components/retreatDetail'
export default {
  name: 'Retreat',
  components: {
    changeDetail,
    repairDetail,
    retreatDetail
  },
  data() {
    return {
      activeName: 'first'
    }
  },
  created() {

  },
  methods: {
    handleClick(tab, event) {

    },
    handleFilter() {

    },
    getList() {

    }
  }
}
</script>
