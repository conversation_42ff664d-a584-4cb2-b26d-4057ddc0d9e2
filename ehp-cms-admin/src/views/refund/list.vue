<template>
  <div class="app-container">
    <div class="filter-container">
      <DatePicker
        ref="datePickerRef1"
        :query-model="listQuery"
        class="filter-item"
        start-placeholder="制单日期-开始"
        end-placeholder="制单日期-结束"
        style="width: 230px"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef2"
        :query-model="listQuery"
        class="filter-item"
        start-placeholder="审核日期-开始"
        end-placeholder="审核日期-结束"
        gte="auditDateGte"
        lte="auditDateLte"
        style="width: 230px"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.method"
        class="filter-item"
        placeholder="退款方式"
        style="width: 120px"
        type="refund-method"
        @change="handleFilter"
      />
      <el-input
        v-model="listQuery.refundOrderSn"
        class="filter-item"
        clearable
        placeholder="退款单号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        class="filter-item"
        clearable
        placeholder="订单号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.status"
        class="filter-item"
        placeholder="单据状态"
        style="width: 120px"
        type="refund-order-status"
        @change="handleFilter"
      />
      <el-button
        v-permission="['oms:refund:order:list']"
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-permission="['oms:refund:order:list']"
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        v-waves
        v-permission="['oms:refund:order:update']"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加</el-button>
      <el-button
        v-waves
        v-permission="['oms:refund:order:audit']"
        type="primary"
        :disabled="checkListLen === auditArr.length && auditArr.length > 0 ? false : true"
        @click="handleAudit"
      >审核</el-button>
      <el-button
        v-waves
        v-permission="['oms:refund:order:reverse:audit']"
        type="primary"
        :disabled="checkListLen === resetArr.length && resetArr.length > 0 ? false : true"
        @click="handleReverseAudit"
      >反审核</el-button>
      <el-button
        v-waves
        v-permission="['oms:refund:order:pay']"
        type="primary"
        :disabled="checkListLen === confirmArr.length && confirmArr.length > 0 ? false : true"
        @click="handlePay"
      >确认付款</el-button>
      <el-button
        v-waves
        v-permission="['oms:refund:order:submit']"
        type="primary"
        :disabled="checkListLen === submitArr.length && submitArr.length > 0 ? false : true"
        @click="handleSubmit"
      >提交</el-button>
      <el-button
        v-waves
        v-permission="['oms:refund:order:invalid']"
        type="primary"
        :disabled="checkListLen === invalidArr.length && invalidArr.length > 0 ? false : true"
        @click="handleInvalid"
      >作废</el-button>
    </div>
    <el-table
      ref="table"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      @selection-change="handleSelectionChange"
    >
      >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="操作" align="center" fixed="left" width="180px">
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['oms:refund:order:detai']"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(row)"
          >详情</el-button>
          <el-button
            v-if="row.status == 1"
            v-waves
            v-permission="['oms:refund:order:update']"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          >修改</el-button>
        </template>
      </el-table-column>
      <el-table-column label="退款单号" prop="refundOrderSn" width="125px" align="center" />
      <el-table-column label="订单编号" prop="orderSn" width="135px" align="center" />
      <!-- <el-table-column label="退货单号" prop="name" width="90px" align="center" /> -->
      <el-table-column label="退款方式" prop="methodDescribe" width="100px" align="center" />
      <el-table-column label="退款状态" prop="statusDescribe" width="130px" align="center" />
      <el-table-column label="退款金额" prop="amount" width="80px" align="center" />
      <el-table-column label="退款原因" prop="reasonDescribe" align="center" />
      <el-table-column label="备注" prop="remark" align="center" />
      <el-table-column label="责任人" prop="createdBy" width="100px" align="center" />
      <el-table-column label="制单日期" prop="createdAt" width="135px" align="center" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="refundOrderDetail"
        :rules="rules"
        :disabled="dialogStatus === 'detail'"
        label-position="right"
        label-width="100px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="销售订单" prop="orderId">
              <el-select
                v-model="selectOrder"
                filterable
                remote
                reserve-keyword
                placeholder="请输入订单号"
                :remote-method="queryOrder"
                :loading="loading"
                style="width:100%"
              >
                <el-option
                  v-for="item in orderList"
                  :key="item.orderId"
                  :label="item.orderSn"
                  :value="item"
                >
                  <el-row :gutter="24">
                    <el-col :span="15">订单号：{{ item.orderSn }}</el-col>
                    <el-col :span="9">订单金额：{{ item.orderAmount }}</el-col>
                  </el-row>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付流水号">
              <el-input v-model="selectOrder.payOrderSn" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单金额">
              <el-input v-model="selectOrder.orderAmount" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账号">
              <el-input v-model="refundOrderDetail.paymentAccount" placeholder="请输入收款账号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="退款金额" prop="amount">
              <el-input v-model="refundOrderDetail.amount" :placeholder="refundAmountStr !='' ? refundAmountStr : '请输入退款金额'" />
              <!-- <el-input v-model="refundOrderDetail.amount" /> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退款方式" prop="method">
              <DictSelect
                v-model="refundOrderDetail.method"
                placeholder="请选择退款方式"
                type="refund-method"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="退款原因" prop="reason">
              <DictSelect
                v-model="refundOrderDetail.reason"
                placeholder="请选择退款原因"
                type="refund-order-reason"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12"></el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="refundOrderDetail.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            :maxlength="100"
          />
        </el-form-item>
      </el-form>
      <div v-if="dialogStatus !== 'create'">
        <el-divider content-position="left">日志信息</el-divider>
        <el-table :data="refundOrderDetail.logs" fit highlight-current-row style="width: 100%;">
          <el-table-column label="操作人" prop="createdBy" width="70px" align="center" />
          <el-table-column label="操作记录" prop="operateDescribe" align="center" />
          <el-table-column label="备注" prop="remark" align="center" />
          <el-table-column label="操作时间" prop="createdAt" width="150px" align="center" />
        </el-table>
      </div>

      <div v-if="dialogStatus !== 'detail'" slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">取消</el-button>
        <el-button
          v-if="refundOrderDetail.status === 2"
          v-waves
          type="primary"
          @click="handleAudit()"
        >审核</el-button>
        <el-button
          v-if="refundOrderDetail.status === 1"
          v-waves
          type="primary"
          @click="updateData()"
        >保存</el-button>
        <el-button
          v-if="refundOrderDetail.status === 1"
          v-waves
          type="primary"
          @click="updateAndSubmit()"
        >保存并提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { audit, get, getList, invalid, pay, queryOrder, reverseAudit, submit, update, updateAndSubmit } from '@/api/refund/order'
import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import { Loading } from 'element-ui'
export default {
  name: 'Refundlist',
  directives: { waves },
  components: { DictSelect, DatePicker },
  filters: {},
  data() {
    return {
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'createdAt',
        orderBy: 'desc',
        method: '',
        refundOrderSn: '',
        orderSn: '',
        status: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '详情',
        update: '更新',
        create: '新增'
      },
      refundOrderDetail: {
        status: 1
      },
      rules: {
        orderId: [
          { required: true, message: '请输入销售订单', trigger: 'blur' }
        ],
        payOrderSn: [{ required: true, message: '请输入支付流水号', trigger: 'blur' }],
        orderAmount: [{ required: true, message: '请输入订单金额', trigger: 'blur' }],
        paymentAccount: [{ required: true, message: '请输入首款账户', trigger: 'blur' }],
        amount: [{ required: true, message: '请输入退款金额', trigger: 'blur' }],
        method: [{ required: true, message: '请选择退款方式', trigger: 'blur' }],
        reason: [{ required: true, message: '请选择退款原因', trigger: 'blur' }],
        name: [{ required: true, message: '请输入真实姓名', trigger: 'blur' }],
        email: [{ required: true, message: '请输入邮箱', trigger: 'blur' }]
      },
      loading: false,
      orderList: [],
      selectOrder: {},
      selectTableOrder: [],
      checkListLen: 0,
      auditArr: [], //审核
      resetArr: [], //反审
      submitArr: [], //提交
      invalidArr: [], //作废
      confirmArr: [], //确认收货
      exportArr: [], //导出
      refundAmountStr: '',
      isPaying: false,
      remainingRefundableAmount: '' // 可退金额
    }
  },
  watch: {
    selectOrder: function(val) {
      this.refundOrderDetail.orderId = val.orderId
      if (val.remainingRefundableAmount) {
        this.refundAmountStr = `可退${val.remainingRefundableAmount}元`
        this.remainingRefundableAmount = val.remainingRefundableAmount
      }
    },
    list(val) {
      this.doLayout()
    }
  },
  created() {
    // this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    handleSelectionChange(value) {
      this.selectTableOrder = []
      for (var i = 0; i < value.length; i++) {
        this.selectTableOrder.push(value[i].id)
      }
      var auditArr = []
      var resetArr = []
      var submitArr = []
      var invalidArr = []
      var confirmArr = []
      if (value.length > 0) {
        value.forEach((currentValue, index, arr) => {
          if (currentValue.status === 1) {
            submitArr.push(currentValue.id)
            invalidArr.push(currentValue.id)
          } else if (currentValue.status === 2) {
            auditArr.push(currentValue.id)
            resetArr.push(currentValue.id)
          } else if (currentValue.status === 3) {
            resetArr.push(currentValue.id)
            confirmArr.push(currentValue.id)
          }
        })
      }
      this.auditArr = auditArr
      this.resetArr = resetArr
      this.submitArr = submitArr
      this.invalidArr = invalidArr
      this.confirmArr = confirmArr
      this.checkListLen = value.length
      console.log(
        this.auditArr,
        this.resetArr,
        this.submitArr,
        this.invalidArr,
        this.confirmArr,
        this.checkListLen
      )
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.$refs.datePickerRef1.reset()
      this.$refs.datePickerRef2.reset()
      this.listQuery.method = ''
      this.listQuery.refundOrderSn = ''
      this.listQuery.orderSn = ''
      this.listQuery.status = ''
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.selectOrder = {}
        this.refundOrderDetail = { status: 1 }
        this.queryOrder('')
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          console.info(this.refundOrderDetail)
        }
      })
    },
    handleDetail(row) {
      this.dialogStatus = 'detail'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.refundOrderDetail = response
        this.setSelectOrder(this.refundOrderDetail)
        this.queryOrder(this.refundOrderDetail.orderSn)
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.refundOrderDetail = response
        this.setSelectOrder(this.refundOrderDetail)
        this.queryOrder(this.refundOrderDetail.orderSn)
      })
    },
    setSelectOrder(refundOrderDetail) {
      this.selectOrder = {
        orderId: refundOrderDetail.orderId,
        orderSn: refundOrderDetail.orderSn,
        payOrderSn: refundOrderDetail.payOrderSn,
        orderAmount: refundOrderDetail.orderAmount
      }
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (parseFloat(this.refundOrderDetail.amount) > parseFloat(this.remainingRefundableAmount)) {
            this.$message({
              message: '退款金额不能大于剩余可退款金额',
              type: 'warning'
            })
            return
          }
          const loadingInstance = Loading.service({ fullscreen: true })
          const {
            id,
            orderId,
            amount,
            method,
            paymentAccount,
            reason,
            remark
          } = this.refundOrderDetail
          update({
            id: id,
            orderId: orderId,
            amount: amount,
            method: method,
            paymentAccount: paymentAccount,
            reason: reason,
            remark: remark
          }).then(() => {
            this.dialogFormVisible = false
            loadingInstance.close()
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单保存成功',
              type: 'success',
              duration: 2000
            })
          }).finally(() => {
            loadingInstance.close()
          })
        }
      })
    },
    updateAndSubmit() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          if (parseFloat(this.refundOrderDetail.amount) > parseFloat(this.remainingRefundableAmount)) {
            this.$message({
              message: '退款金额不能大于剩余可退款金额',
              type: 'warning'
            })
            return
          }
          const loadingInstance = Loading.service({ fullscreen: true })
          const {
            id,
            orderId,
            amount,
            method,
            paymentAccount,
            reason,
            remark
          } = this.refundOrderDetail
          updateAndSubmit({
            id: id,
            orderId: orderId,
            amount: amount,
            method: method,
            paymentAccount: paymentAccount,
            reason: reason ? reason : '',
            remark: remark
          }).then(() => {
            this.dialogFormVisible = false
            loadingInstance.close()
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单保存提交成功',
              type: 'success',
              duration: 2000
            })
          }) .finally(() => {
            loadingInstance.close()
          })
        }
      })
    },
    queryOrder(orderSn) {
      if (orderSn !== '' && orderSn.length > 10) {
        this.loading = true
        queryOrder(orderSn).then(response => {
          this.orderList = response
          if (this.dialogStatus === 'update') {
            this.remainingRefundableAmount = response[0].remainingRefundableAmount
          }
          this.loading = false
        })
      } else {
        this.orderList = []
      }
    },
    handleSubmit() {
      this.$confirm('是否要将选中的订单提交?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          submit(this.submitArr).then(() => {
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单提交成功',
              type: 'success',
              duration: 2000
            })
          })
        })
        .catch(() => {})
    },
    handlePay() {
      // 防止重复点击
      if (this.isPaying) {
        this.$message.warning('请勿重复点击')
        return
      }
      this.$confirm('是否要将选中的订单打款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.isPaying = true
          pay(this.confirmArr).then(() => {
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单付款申请提交成功，等待系统付款。',
              type: 'success',
              duration: 2000
            })
            setTimeout(() => {
              this.isPaying = false
            }, 5000)
          })
        })
        .catch(() => {})
    },
    handleAudit() {
      this.$confirm('是否要将选中的订单审核通过?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          audit(this.auditArr).then(() => {
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单审核成功。',
              type: 'success',
              duration: 2000
            })
          })
        })
        .catch(() => {})
    },
    handleReverseAudit() {
      this.$confirm('是否要将选中的订单进行反审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          reverseAudit(this.resetArr).then(() => {
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单反审核成功。',
              type: 'success',
              duration: 2000
            })
          })
        })
        .catch(() => {})
    },
    handleInvalid() {
      this.$confirm('是否要将选中的订单作废?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          invalid(this.invalidArr).then(() => {
            this.getList()
            this.$notify({
              title: '成功',
              message: '订单作废成功',
              type: 'success',
              duration: 2000
            })
          })
        })
        .catch(() => {})
    },
    /* 重新渲染table组件 */
    doLayout() {
      const that = this
      this.$nextTick(() => {
        that.$refs.table.doLayout()
      })
    }
  }
}
</script>
