<template>
  <div class="app-container">
    <p v-if="$route.query.name" style="font-weight: bold;">当前选择的厂家：{{ $route.query.name }}</p>
    <div class="filter-container">
      <el-input v-model="listQuery.name" placeholder="药材名称" clearable class="filter-item" style="width: 120px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.aslias" placeholder="别名" clearable class="filter-item" style="width: 120px;" @keyup.enter.native="handleFilter" />
      <el-select v-model="listQuery.productionEnterpriseId" clearable placeholder="生产企业" @change="handleFilter">
        <el-option v-for="item in baseoptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
      </el-select>
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-if="!$route.params.id" v-permission="['tcm:particle:save']" type="primary" icon="el-icon-plus" @click="handleCreate">添加中药配方颗粒</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="50px" />
      <el-table-column label="药材名称" prop="name" min-width="100px" align="center" />
      <el-table-column label="别名" prop="alias" align="center" min-width="100px" />
      <el-table-column label="功能主治" prop="indications" align="center" min-width="300px" />
      <el-table-column label="规格" prop="spec" width="100px" align="center" />
      <el-table-column label="当量" prop="equivalent" width="100px" align="center" />
      <el-table-column label="生产企业" prop="productionEnterpriseName" width="100px" align="center" />
      <el-table-column label="药材类型" prop="typeDescribe" width="100px" align="center" />
      <el-table-column label="销售价格" prop="price" width="100px" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="180px">
        <template slot-scope="{row}">
          <el-button v-permission="['tcm:particle:list']" type="primary" @click="editProduct(row.id)">编辑</el-button>
          <el-button v-permission="['tcm:particle:update']" type="primary" @click="delProduct(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="1000px">
      <el-form ref="dataForm" :model="productData" :rules="rules" :inline="true" label-width="120px" class="demo-form-inline">
        <el-row v-if="title=='添加'" :gutter="20">
          <el-col :span="24">
            <el-form-item label>
              <el-button type="primary" @click="openSlice">请选择关联的中药材</el-button>
            </el-form-item>
            <el-button type="primary" @click="clearSlice">清除关联</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="药材名" prop="sliceDetails.name">
              <el-input v-model="productData.sliceDetails.name" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格" prop="spec">
              <el-input v-model="productData.spec" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="别名">
              <el-input v-model="aslias" clearable>
                <el-button slot="append" @click="addAlias">添加</el-button>
              </el-input>
            </el-form-item>
            <el-tag v-for="(tag, i) in productData.sliceDetails.alias" :key="tag" class="i_tag" closable size="mini" @close="removeTag(i)">{{ tag }}</el-tag>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="当量" prop="equivalent">
              <el-input v-model.number="productData.equivalent" type="number" clearable @keyup.native="formatNum" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="贮藏">
              <el-input v-model="productData.sliceDetails.storage" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="生产企业" prop="productionEnterpriseId">
              <el-select v-model="productData.productionEnterpriseId" clearable placeholder="生产企业">
                <el-option v-for="item in baseoptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" :gutter="20" class="dis_col mrt8">
            找不到相关药企？去
            <span class="blue" @click="$router.push('/cnCompany/list')">生产企业管理</span> 里添加吧
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="价格" prop="price">
              <el-input v-model="productData.price" type="number" clearable @keyup.native="format2Num">
                <template slot="append">元</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="性状">
              <el-input v-model="productData.sliceDetails.phenotypicTrait" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="化学成分">
              <el-input v-model="productData.sliceDetails.ingredients" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="功能主治">
              <el-input v-model="productData.sliceDetails.indications" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用法用量">
              <el-input v-model="productData.sliceDetails.usageDosage" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="来源">
              <el-input v-model="productData.sliceDetails.source" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原形态">
              <el-input v-model="productData.sliceDetails.protomorphic" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="毒性">
              <el-input v-model="productData.sliceDetails.toxicity" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药理作用">
              <el-input v-model="productData.sliceDetails.pharmaco" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="性味">
              <DictSelect v-model="productData.sliceDetails.sexualFlavour" :multiple="true" placeholder="请选择" type="tcm-slice-sf" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归经">
              <DictSelect v-model="productData.sliceDetails.channelTropism" :multiple="true" placeholder="请选择" type="tcm-slice-channeltropism" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="药性">
              <DictSelect v-model="productData.sliceDetails.drugProperties" placeholder="请选择" type="tcm-slice-drugproperties" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出处">
              <el-input v-model="productData.sliceDetails.provenance" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24">
            <el-form-item label="注意事项">
              <el-input v-model="productData.sliceDetails.mattersNeedingAttention" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button v-permission="['tcm:particle:save']" type="primary" @click="nextCreate('dataForm')">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="选择药品" :visible.sync="dialogTableVisible" width="800px">
      <div class="filter-container">
        <el-input
          v-model="sliceListQuery.name"
          placeholder="药材名称"
          clearable
          class="filter-item"
          style="width: 120px;"
          @keyup.enter.native="openSlice"
        />
        <el-button type="primary" icon="el-icon-search" @click="openSlice">搜索</el-button>
      </div>
      <el-table :key="sliceTableKey" :data="sliceList" fit highlight-current-row>
        <el-table-column prop="id" label="id" width="80px" />
        <el-table-column prop="name" label="药品名称" />
        <el-table-column label="操作" align="center" width="180px">
          <template slot-scope="{row}">
            <el-button v-permission="['tcm:particle:list']" type="primary" @click="setSlice(row.id)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="sliceTotal>0"
        :total="sliceTotal"
        :page.sync="sliceListQuery.pageNo"
        :limit.sync="sliceListQuery.pageSize"
        @pagination="getSliceList"
      />
    </el-dialog>
  </div>
</template>
<style scoped>
.dis_col {
  display: flex;
  align-items: center;
}
.blue {
  color: #1890ff;
}
.blue:hover {
  cursor: pointer;
}
.red {
  color: red;
}
.labelitem .el-form-item {
  display: flex;
}
.labelitem /deep/ .el-form-item__content {
  flex: 1;
}
.i_tag {
  margin: 8px 5px 0;
}
</style>
<script>
import API from '@/api/cnMedicine/index'
import API_COM from '@/api/cnCompany/index'
import DictSelect from '@/components/DictSelect'
export default {
  name: '',
  filters: {},
  components: {
    DictSelect
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        productionEnterpriseId: ''
      },
      dialogFormVisible: false,
      dialogTableVisible: false,
      formLabelWidth: '25%',
      productData: {
        sliceDetails: {}
      },
      rules: {
        'sliceDetails.name': [{ required: true, message: '请填写药品名称', trigger: 'blur' }],
        equivalent: [{ required: true, message: '请填写当量', trigger: 'blur' }],
        price: [{ required: true, message: '请填写价格', trigger: 'blur' }],
        productionEnterpriseId: [{ required: true, message: '请选择企业', trigger: 'change' }],
        spec: [{ required: true, message: '请填写规格', trigger: 'blur' }]
      },
      aslias: '',
      setList: null,
      sliceList: [],
      pharmacologyData: [],
      title: '添加',
      sliceListQuery: {
        pageNo: 1,
        pageSize: 10
      },
      sliceTotal: 0,
      sliceTableKey: 0,
      sliceDisabled: false,
      autosize: { minRows: 2, maxRows: 20 }
    }
  },
  created() {
    if (this.$route.params.id) {
      this.listQuery.productionEnterpriseId = parseInt(this.$route.params.id)
    }
    this.handleFilter()
    this.getBaseData()
  },
  methods: {
    // 获取数据
    getList() {
      API.particleList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getBaseData() {
      const params = {}
      params.pageSize = 100
      API_COM.list(params).then(response => {
        this.baseoptions = response.list
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleCreate() {
      this.title = '添加'
      this.productData = { sliceDetails: {}}
      this.$forceUpdate()
      this.dialogFormVisible = true
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          API.updateParticle(this.productData).then(res => {
            this.$message.success('操作成功')
            this.getList()
            this.dialogFormVisible = false
          })
        }
      })
    },
    editProduct(productId) {
      this.title = '编辑'
      API.getParticle(productId) .then(res => {
        console.log(this.productData)
        this.productData = res
        this.dialogFormVisible = true
      })
      console.log(productId)
    },
    delProduct(productId) {
      this.$confirm('是否删除此中药配方？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        API.delParticle(productId).then(res => {
          this.$message.success('删除成功')
          this.getList()
          console.log('删除方剂', productId)
        })
      })
    },
    addAlias() {
      if (this.aslias) {
        if (!(this.productData.sliceDetails.alias instanceof Array)) {
          this.productData.sliceDetails.alias = []
        }
        this.productData.sliceDetails.alias.push(this.aslias)
        this.aslias = ''
        this.$forceUpdate()
      }
    },
    removeTag(index) {
      console.log(this.productData.sliceDetails.alias, index)
      this.productData.sliceDetails.alias.splice(index, 1)
      this.$forceUpdate()
    },
    handleChange() {
      console.log('change')
    },
    getSliceList() {
      API.list(this.sliceListQuery).then(response => {
        this.sliceList = response.list
        this.sliceTotal = response.totalCount
      })
    },
    openSlice() {
      this.sliceListQuery.pageNo = 1
      this.getSliceList()
      this.dialogTableVisible = true
    },
    setSlice(id) {
      API.getSlices(id).then(res => {
        this.$set(this.productData, 'sliceDetails', res)
        this.dialogTableVisible = false
      })
    },
    clearSlice() {
      this.productData.sliceDetails = {}
    },
    format2Num(e) {
      // 通过正则过滤小数点后两位
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null
      this.productData.price = e.target.value
    },
    formatNum(e) {
      // 通过正则过滤小数
      e.target.value = e.target.value.match(/^\d*/g)[0] || null
      this.productData.equivalent = e.target.value
    }
  }
}
</script>
