<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.name" placeholder="药材名称" clearable class="filter-item" style="width: 120px;" @keyup.enter.native="handleFilter" />
      <el-input v-model="listQuery.alias" placeholder="别名" clearable class="filter-item" style="width: 120px;" @keyup.enter.native="handleFilter" />
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button v-permission="['tcm:slice:save']" type="primary" icon="el-icon-plus" @click="handleCreate">添加中药饮片/中药材</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="50px" />
      <el-table-column label="药材名称" prop="name" min-width="100px" align="center" />
      <el-table-column label="别名" prop="alias" align="center" min-width="250px" />
      <el-table-column label="功能主治" prop="indications" align="center" min-width="300px" />
      <el-table-column label="药材类型" prop="typeDescribe" width="100px" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="180px">
        <template slot-scope="{row}">
          <el-button v-permission="['tcm:slice:list']" type="primary" @click="editProduct(row.id)">编辑</el-button>
          <el-button v-permission="['tcm:slice:delete']" type="primary" @click="delProduct(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <el-dialog :title="title" :visible.sync="dialogFormVisible" width="1000px">
      <el-form ref="dataForm" :model="productData" :rules="rules" :inline="true" label-width="130px" class="demo-form-inline">
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24">
            <el-form-item label="请选择药材类型" prop="type">
              <DictRadio v-model="productData.type" placeholder="请选择" type="tcm-slice-type" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="药材名" prop="name">
              <el-input v-model="productData.name" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="炮制方法">
              <el-input v-model="productData.processing" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="别名">
              <el-input v-model="aslias" clearable>
                <el-button slot="append" @click="addAlias">添加</el-button>
              </el-input>
            </el-form-item>
            <el-tag v-for="(tag, i) in productData.alias" :key="tag" class="i_tag" closable size="mini" @close="removeTag(i)">{{ tag }}</el-tag>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="贮藏">
              <el-input v-model="productData.storage" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性状">
              <el-input v-model="productData.phenotypicTrait" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="化学成分">
              <el-input v-model="productData.ingredients" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出处">
              <el-input v-model="productData.provenance" type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="功能主治">
              <el-input v-model="productData.indications" type="textarea" :autosize="autosize" placeholder />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用法用量">
              <el-input v-model="productData.usageDosage" type="textarea" :autosize="autosize" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="来源">
              <el-input v-model="productData.source" type="textarea" :autosize="autosize" placeholder />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="原形态">
              <el-input v-model="productData.protomorphic" type="textarea" :autosize="autosize" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="毒性">
              <el-input v-model="productData.toxicity" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="药理作用">
              <el-input v-model="productData.pharmaco" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="性味">
              <DictSelect v-model="productData.sexualFlavour" :multiple="true" placeholder="请选择" type="tcm-slice-sf" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="归经">
              <DictSelect v-model="productData.channelTropism" :multiple="true" placeholder="请选择" type="tcm-slice-channeltropism" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="12">
            <el-form-item label="药性">
              <DictSelect v-model="productData.drugProperties" placeholder="请选择" type="tcm-slice-drugproperties" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="注意事项">
              <el-input v-model="productData.mattersNeedingAttention" type="textarea" :autosize="autosize" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button v-permission="['tcm:slice:save']" type="primary" @click="nextCreate('dataForm')">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.dis_col {
  display: flex;
  align-items: center;
}
.mrt8 {
  margin-top: 8px;
}
.red {
  color: red;
}
.labelitem .el-form-item {
  display: flex;
}
.labelitem /deep/ .el-form-item__content {
  flex: 1;
}
.i_tag {
  margin: 8px 5px 0;
}
</style>
<script>
import API from '@/api/cnMedicine/index'
import DictSelect from '@/components/DictSelect'
import DictRadio from '@/components/DictRadio'

export default {
  name: '',
  filters: {},
  components: {
    DictSelect,
    DictRadio
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        alias: ''
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      productData: {},
      rules: {
        type: [{ required: true, message: '请选择药材类型', trigger: 'blur' }],
        name: [{ required: true, message: '请填写药材名称', trigger: 'blur' }]
      },
      aslias: '',
      title: '添加',
      autosize: { minRows: 2, maxRows: 20 }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      API.list(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      // this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.alias = ''
      this.handleFilter()
    },
    handleCreate() {
      this.title = '添加'
      this.resetTemp()
      this.productData = {}
      this.dialogFormVisible = true
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          API.update(this.productData).then(res => {
            this.$message.success('操作成功')
            this.getList()
            this.dialogFormVisible = false
          })
        }
      })
    },
    editProduct(productId) {
      this.title = '编辑'
      this.resetTemp()
      API.getSlices(productId).then(res => {
        this.productData = res
        this.dialogFormVisible = true
      })
    },
    delProduct(productId) {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        API.del(productId).then(res => {
          this.$message.success('删除成功')
          this.getList()
          console.log('删除方剂', productId)
        })
      })
    },
    addAlias() {
      if (this.aslias) {
        if (!(this.productData.alias instanceof Array)) {
          this.productData.alias = []
        }
        this.productData.alias.push(this.aslias)
        this.aslias = ''
        this.$forceUpdate()
      }
    },
    removeTag(index) {
      console.log(this.productData.alias, index)
      this.productData.alias.splice(index, 1)
      this.$forceUpdate()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.productData = {}
        this.aslias = null
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>
