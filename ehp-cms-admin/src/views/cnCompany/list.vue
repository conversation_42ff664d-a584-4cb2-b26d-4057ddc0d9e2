<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="生产企业名称"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.contact"
        placeholder="生产企业联系电话"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-permission="['tcm:pe:list']" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-permission="['tcm:pe:list']" type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button
        v-permission="['tcm:pe:save']"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加生产企业</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="50px" />
      <el-table-column label="生产企业名称" prop="name" width="120px" align="center" />
      <el-table-column label="生产企业地址" prop="address" align="center" width="250px" />
      <el-table-column label="联系人" prop="linkman" align="center" />
      <el-table-column label="联系电话" prop="contact" width="100px" align="center" />
      <!-- <el-table-column label="药材数量" width="100px" align="center">
        <template slot-scope="{row}">
          <el-button
            type="text"
            size="mini"
            @click="$router.push({path:`/cnCompany/list/${row.id}/drugs`})"
          >{{ row.count }}</el-button>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" fixed="right" align="center" width="180px">
        <template slot-scope="scope">
          <el-button
            v-permission="['tcm:pe:update']"
            type="primary"
            @click="editProduct(scope.$index)"
          >编辑</el-button>
          <el-button
            v-permission="['tcm:pe:delete']"
            type="primary"
            @click="delProduct(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog :title="title" :visible.sync="dialogFormVisible">
      <el-form
        ref="dataForm"
        :model="productData"
        :rules="rules"
        :inline="true"
        label-width="150px"
        class="demo-form-inline"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="生产企业名称" prop="name">
              <el-input v-model="productData.name" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="生产企业地址" prop="address">
              <el-input v-model="productData.address" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="联系电话" prop="contact">
              <el-input
                v-model="productData.contact"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="联系人" prop="linkman">
              <el-input
                v-model="productData.linkman"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button v-permission="['tcm:pe:save']" type="primary" @click="nextCreate('dataForm')">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.col_input /deep/ input {
  width: 400px;
}
.red{
  color: red;
}
</style>
<script>
import API from '@/api/cnCompany/index'
export default {
  name: '',
  filters: {},
  components: {},
  data() {
    var checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('手机号不能为空'))
      } else {
        const reg = /^1[3|4|5|7|8][0-9]\d{8}$/
        if (reg.test(value)) {
          callback()
        } else {
          return callback(new Error('请输入正确的手机号'))
        }
      }
    }
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        contact: ''
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      productData: {},
      rules: {
        name: [{ required: true, message: '请填写生产企业名称', trigger: 'blur' }],
        address: [{ required: true, message: '请填写地址', trigger: 'blur' }],
        contact: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        linkman: [{ required: true, message: '请填写联系人', trigger: 'blur' }]
      },
      detail: [],
      title: '添加'
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      API.list(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      // this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.contact = ''
      this.handleFilter()
    },
    handleCreate() {
      this.title = '添加'
      this.productData = {}
      this.dialogFormVisible = true
      this.resetForm('dataForm')
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          API.update(this.productData).then(res => {
            this.getList()
            this.dialogFormVisible = false
            this.$message.success({
              message: '操作成功'
            })
          })
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    editProduct(index) {
      this.title = '编辑'
      this.productData = this.list[index]
      this.dialogFormVisible = true
      this.resetForm('dataForm')
    },
    delProduct(productId) {
      this.$confirm('是删除此生产企业？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        API.del(productId).then(res => {
          this.getList()
          console.log('删除方剂', productId)
          this.$message.success({
            message: '删除成功'
          })
        })
      })
    },
    addAlias() {
      if (this.aslias) {
        this.tags.push(this.aslias)
        this.aslias = ''
      }
    },
    removeTag(index) {
      this.tags.splice(index, 1)
    }
  }
}
</script>
