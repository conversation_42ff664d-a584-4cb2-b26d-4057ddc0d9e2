<template>
  <div class="dashboard-container">
    <!-- <component :is="currentRole" /> -->
    <dataOverview />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import adminDashboard from './admin'
import dataOverview from '@/views/dataStatistics/dataOverview'
export default {
  name: 'Dashboard',
  components: { dataOverview },
  data() {
    return {
      currentRole: 'dataOverview',
      driver: null
    }
  },
  computed: {
    ...mapGetters(['roles'])
  },
  created() {
    // if (!this.roles.includes('admin')) {
    //   this.currentRole = ''
    // }
    this.currentRole = ''
  }
}
</script>
