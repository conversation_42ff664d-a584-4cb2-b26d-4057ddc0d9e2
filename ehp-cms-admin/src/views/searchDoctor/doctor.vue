<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        class="filter-item"
        placeholder="医生ID"
        style="width: 150px"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        class="filter-item"
        placeholder="医生姓名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        clearable
        class="filter-item"
        placeholder="手机号"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-upload
        style="display: inline-block;"
        :action="uploadPath"
        :headers="headers"
        :on-success="handleExcelSuccess"
        :on-error="handleExcelError"
        :on-remove="handleRemoveExcel"
        :file-list="fileList"
        :limit="1"
        :show-file-list="false"
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      >
        <el-button v-permission="['user:doctor:expert:import']" type="primary">导入</el-button>
        <div slot="tip" class="el-upload__tip"></div>
      </el-upload>
      <el-link :href="tempPath" type="info" target="_blank">下载导入模板</el-link>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row style="width: 100%">
      <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
      <el-table-column label="姓名" prop="name" width="100px" align="center">
        <template slot-scope="{row}">
          <DoctorDetails
            :ref="row.id"
            :doctor-id="row.id"
            :doctor-name="row.name"
            :handle-filter="getList"
          />
        </template>
      </el-table-column>
      <el-table-column label="手机" prop="phone" width="110px" align="center" />
      <el-table-column label="执业医院" prop="hospitalName" align="center" />
      <el-table-column label="科室" prop="departmentName" width="130px" align="center" />

      <el-table-column label="操作" prop="createdAt" width="300px" align="center">
        <template slot-scope="scope">
          <el-button
            v-permission="['user:doctor:expert:remove']"
            size="mini"
            type="danger"
            @click="doctorRemove(scope.row.id,scope.row.name,scope.$index)"
          >移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getList, doctorRemove, excelURL, tempUrl } from '@/api/searchDoctor/index'
import waves from '@/directive/waves' // Waves directive
import DoctorDetails from '../user/components/doctorDetails'
import { getToken, getTokenName } from '@/utils/auth'
export default {
  name: 'SearchDoctordoctor',
  directives: { waves },
  filters: {},
  components: {
    DoctorDetails
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        id: '',
        name: '',
        phone: ''
      },
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: []
    }
  },
  created() {
    this.uploadPath = excelURL()
    this.tempUrl()
    this.headers[getTokenName()] = getToken()
    // this.handleFilter()
  },
  activated() {
    this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        console.log(response.list, 123)
        this.list = response.list
        this.total = response.totalCount
      })
    },
    tempUrl() {
      tempUrl().then(response => {
        this.tempPath = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.phone = ''
      this.handleFilter()
    },
    //停用启用弹出层
    doctorRemove(id, name, index) {
      console.log(name)
      this.$confirm('确认把' + name + '医生移出专家名医吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.sureSetRemove(id, index)
      })
    },
    //弹出层确认事件
    sureSetRemove(id, index) {
      const params = {}
      params.doctorId = id
      doctorRemove(params).then(response => {
        this.getList()
      }).finally(() => {

      })
    },
    uploadEXCEL() {

    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.getList()
        window.open(response.data, '_blank')
      }
    },
    handleRemoveExcel(file, fileList) {

    }
  }
}
</script>
