<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.username"
        class="filter-item"
        clearable
        placeholder="用户名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker ref="datePickerRef" :query-model="listQuery" style="width: 230px" @change="handleFilter" />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
    </div>

    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="用户名" prop="username" width="60px" align="center" />
      <el-table-column label="用户操作" prop="operation" align="center" />
      <el-table-column label="请求方法" prop="method" align="center" />
      <el-table-column label="请求参数" prop="params" align="center" />
      <el-table-column label="请求耗时" prop="time" width="110px" align="center">
        <template slot-scope="{row}">
          <el-tag size="small">{{ row.time }}ms</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="IP地址" prop="ip" width="80px" align="center" />
      <el-table-column label="请求时间" prop="createdAt" width="135px" align="center" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { getList } from '@/api/system/log'
import waves from '@/directive/waves' // Waves directive
import DatePicker from '@/components/DatePicker'
export default {
  name: 'Systemlog',
  directives: { waves },
  filters: {},
  components: {
    DatePicker
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'createdAt',
        orderBy: 'desc'
      },
      textMap: {
        update: '更新',
        create: '新增'
      }
    }
  },
  created() {
    // this.getList()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.username = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    }
  }
}
</script>
