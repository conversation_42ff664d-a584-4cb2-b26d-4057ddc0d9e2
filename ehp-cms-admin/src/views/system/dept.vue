<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        v-permission="['sys:dept:save']"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加</el-button>
    </div>
    <el-table
      row-key="id"
      :data="list"
      fit
      highlight-current-row
      default-expand-all
      style="width: 100%;"
    >
      <el-table-column label="部门名称" prop="name" align="left" />
      <el-table-column label="排序号" prop="orderNum" width="70px" align="center" />
      <el-table-column label="创建时间" prop="createdAt" width="140px" align="center" />
      <el-table-column label="操作" align="center" width="150px">
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['sys:dept:update']"
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          />
          <el-button
            v-waves
            v-permission="['sys:dept:delete']"
            icon="el-icon-delete"
            type="danger"
            @click="handleDelete(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="500px">
      <el-form
        ref="dataForm"
        :model="dept"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="dept.name" maxlength="50" placeholder="请输入部门名称" />
        </el-form-item>
        <el-form-item label="上级部门" prop="parentId">
          <el-cascader
            v-model="dept.parentId"
            :options="deptNewList"
            :props="deptProps"
            clearable
            :show-all-levels="false"
            style="width:310px"
          />
        </el-form-item>
        <el-form-item label="排序号" prop="orderNum">
          <el-input-number v-model="dept.orderNum" :min="1" label="排序号" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">取消</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  getDeptList,
  get,
  create,
  update,
  del
} from '@/api/system/dept'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'

export default {
  name: 'DeptTable',
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        orderByField: 'orderNum',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      roleList: {},
      deptList: [],
      deptNewList: [],
      dept: {},
      rules: {
        name: [{ required: true, message: '请输入部门名称', trigger: 'blur' }],
        parentId: [
          { required: true, message: '请选择上级部门', trigger: 'blur' }
        ]
      },
      deptProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id',
        disabled: 'disabled'
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  created() {
    this.getList()
    this.getDeptList()
  },
  methods: {
    getDeptList() {
      getDeptList().then(response => {
        this.deptList = response
        this.deptNewList = response
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
      })
    },
    handleFilter() {
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.dept = {
          orderNum: 1
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          create(this.dept).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getDeptList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    searchItem(deptList, row) {
      for (var i = 0; i < deptList.length; i++) {
        deptList[i].disabled = false
        // row.id === 1 新增判断顶级部门 不可分配至子级部门
        if (deptList[i].id === row.id || deptList[i].parentId === row.id || row.id === 1) {
          deptList[i].disabled = true
        }
        if (deptList[i].children) {
          this.searchItem(deptList[i].children, row)
        }
      }
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      this.deptNewList = this.deptList
      this.searchItem(this.deptNewList, row)
      get(row.id).then(response => {
        this.dept = response
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          update(this.dept).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getDeptList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该部门, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del([row.id]).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getDeptList()
        })
      })
    }
  }
}
</script>
