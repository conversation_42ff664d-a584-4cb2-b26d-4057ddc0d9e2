<template>
  <div class="app-container">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="医生日志" name="1">
        <div class="filter-container">
          <el-input
            v-model="listQuery.name"
            class="filter-item"
            clearable
            placeholder="用户名"
            style="width: 150px"
            @keyup.enter.native="handleFilter"
          />
          <DatePicker :query-model="listQuery" style="width: 230px" @change="handleFilter" />
          <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
          <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </div>

        <el-table :key="tableKey" :data="list" fit highlight-current-row>
          <el-table-column label="用户ID" prop="userId" width="80px" align="center" />
          <el-table-column label="用户名" prop="name" width="100px" align="center" />
          <el-table-column label="设备号" prop="deviceSN" width="270px" align="center" />
          <el-table-column label="设备类型" prop="model" width="100px" align="center" />
          <el-table-column label="操作描述" prop="operate" width="100px" align="center" />
          <el-table-column label="网络IP" prop="ip" width="120px" align="center" />
          <el-table-column label="登录地址" prop="loginAddr" min-width="120px" align="center" />
          <el-table-column label="请求时间" prop="loginTime" width="180px" align="center" />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.pageNo"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="药师日志" name="2">
        <div class="filter-container">
          <el-input
            v-model="listQuery.name"
            class="filter-item"
            clearable
            placeholder="用户名"
            style="width: 150px"
            @keyup.enter.native="handleFilter"
          />
          <DatePicker :query-model="listQuery" style="width: 230px" @change="handleFilter" />
          <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        </div>

        <el-table :key="tableKey" :data="list" fit highlight-current-row>
          <el-table-column label="用户ID" prop="userId" width="80px" align="center" />
          <el-table-column label="用户名" prop="name" width="100px" align="center" />
          <el-table-column label="设备号" prop="deviceSN" width="270px" align="center" />
          <el-table-column label="设备类型" prop="model" width="100px" align="center" />
          <el-table-column label="操作描述" prop="operate" width="100px" align="center" />
          <el-table-column label="网络IP" prop="ip" width="120px" align="center" />
          <el-table-column label="登录地址" prop="loginAddr" min-width="120px" align="center" />
          <el-table-column label="请求时间" prop="loginTime" width="180px" align="center" />
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="listQuery.pageNo"
          :limit.sync="listQuery.pageSize"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import API from '@/api/system/log'
import waves from '@/directive/waves' // Waves directive
import DatePicker from '@/components/DatePicker'
export default {
  name: 'SystemappLog',
  directives: { waves },
  filters: {},
  components: {
    DatePicker
  },
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        type: 1,
        orderByField: 'createdAt',
        orderBy: 'desc'
      },
      textMap: {
        update: '更新',
        create: '新增'
      },
      activeName: '1'
    }
  },
  created() {
    // this.getList()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      API.getAppList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleClick(tab, event) {
      this.listQuery.type = tab.name
      this.listQuery.pageNo = 1
      this.getList()
      console.log(tab.name, tab, event)
    },
    handleReset() {
      this.listQuery.name = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    }
  }
}
</script>
