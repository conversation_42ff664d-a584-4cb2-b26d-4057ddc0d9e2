<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="8" :lg="7" :xl="5">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div style="text-align: center">
              <el-upload :show-file-list="false" action class="avatar-uploader" accept="image/png, image/gif, image/jpeg, image/jpg">
                <img v-if="avatar" :src="avatar" title="点击上传头像" class="avatar" />
                <i v-else class="el-icon-plus avatar-uploader-icon" />
              </el-upload>
            </div>
            <ul class="user-info">
              <li>
                <svg-icon icon-class="user" />用户名称
                <div class="user-right">{{ user.name }}</div>
              </li>
              <li>
                <svg-icon icon-class="phone" />手机号码
                <div class="user-right">{{ user.mobile }}</div>
              </li>
              <li>
                <svg-icon icon-class="email" />用户邮箱
                <div class="user-right">{{ user.email }}</div>
              </li>
              <li>
                <svg-icon icon-class="dept" />所属部门
                <div class="user-right">{{ user.deptName }}</div>
              </li>
              <li>
                <svg-icon icon-class="date" />创建日期
                <div class="user-right">{{ user.createdAt }}</div>
              </li>
              <li>
                <svg-icon icon-class="anq" />安全设置
                <div class="user-right">
                  <a @click="$refs.pass.dialog = true">修改密码</a>
                </div>
              </li>
            </ul>
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="16" :lg="17" :xl="19">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>操作日志</span>
            <div style="display:inline-block;float: right;cursor: pointer" @click="refresh">
              <i :class="ico" />
            </div>
          </div>
          <div>
            <el-table :data="list" size="small" style="width: 100%;">
              <el-table-column prop="operation" label="操作" />
              <el-table-column prop="ip" label="IP" />
              <el-table-column prop="time" label="请求耗时" align="center">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.time <= 300">{{ scope.row.time }}ms</el-tag>
                  <el-tag v-else-if="scope.row.time <= 1000" type="warning">{{ scope.row.time }}ms</el-tag>
                  <el-tag v-else type="danger">{{ scope.row.time }}ms</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="创建日期" width="180px">
                <template slot-scope="scope">{{ scope.row.createdAt }}</template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total>0"
              :total="total"
              :page.sync="listQuery.pageNo"
              :limit.sync="listQuery.pageSize"
              @pagination="getList"
            />
          </div>
        </el-card>
      </el-col>
    </el-row>
    <UpdatePass ref="pass" />
  </div>
</template>

<script>
import { getUserLog } from '@/api/system/user'
import UpdatePass from './updatePass'
export default {
  name: 'Center',
  components: { UpdatePass },
  data() {
    return {
      ico: 'el-icon-refresh',
      user: this.$store.state['system/user'].user,
      avatar: this.$store.state['system/user'].avatar,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'createdAt',
        orderBy: 'desc'
      }
    }
  },
  computed: {},
  created() {
    this.getList()
  },
  methods: {
    getList() {
      getUserLog().then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    refresh() {
      this.getList()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.avatar-uploader-icon {
  font-size: 28px;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
  border-radius: 50%;
}
.user-info {
  padding-left: 0px;
  list-style: none;
  li {
    border-bottom: 1px solid #f0f3f4;
    border-top: 1px solid #f0f3f4;
    padding: 11px 0px;
    font-size: 13px;
  }
  .user-right {
    float: right;

    a {
      color: #317ef3;
    }
  }
}
</style>
