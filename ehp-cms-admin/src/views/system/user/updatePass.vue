<template>
  <div style="display: inline-block">
    <el-dialog
      :visible.sync="dialog"
      :close-on-click-modal="false"
      title="修改密码"
      append-to-body
      width="620px"
      @close="cancel"
    >
      <el-form ref="dataForm" :model="pwd" :rules="rules" size="small" label-width="88px">
        <el-form-item label="旧密码" prop="password">
          <el-input
            v-model="pwd.password"
            type="password"
            auto-complete="on"
            style="width: 400px;"
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="pwd.newPassword"
            type="password"
            auto-complete="on"
            style="width: 400px;"
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPass">
          <el-input
            v-model="pwd.confirmPass"
            type="password"
            auto-complete="on"
            style="width: 400px;"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialog = false">取消</el-button>
        <el-button :loading="loading" type="primary" @click="updateData">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { userPassword } from '@/api/system/user'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'
import { encrypt } from '@/utils/rsaEncrypt'

export default {
  name: 'UpdatePass',
  directives: { waves },
  data() {
    const confirmPass = (rule, value, callback) => {
      if (value) {
        if (this.pwd.newPassword !== value) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      } else {
        callback(new Error('请再次输入密码'))
      }
    }
    return {
      pwd: {},
      dialog: false,
      loading: false,
      rules: {
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        newPassword: [
          {
            required: true,
            validator: (rule, value, callback) => {
              var strlc = /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9]))(?=^[^\u4e00-\u9fa5]{0,}$).{8,20}$/
              if (value == null) {
                callback(new Error('请输入新密码'))
              } else if (value.match(strlc) == null) {
                callback(
                  new Error(
                    '密码过于简单有被盗风险，请保证密码大于8位，并且由大小写字母、数字，特殊符号组成'
                  )
                )
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ],
        confirmPass: [
          { required: true, validator: confirmPass, trigger: 'blur' }
        ]
      }
    }
  },

  methods: {
    open() {
      console.info('-----')
    },
    cancel() {
      this.resetTemp()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.pwd = {}
        this.$refs['dataForm'].clearValidate()
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid) {
          const params = new FormData()
          params.append('password', encrypt(this.pwd.password))
          params.append('newPassword', encrypt(this.pwd.newPassword))
          params.append('confirmPass', encrypt(this.pwd.confirmPass))
          userPassword(params).then(() => {
            this.pwd = {}
            this.dialog = false
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    }
  }
}
</script>

