<template>
  <div class="app-container">
    <el-card v-for="(item,index) in list" :key="index" class="setingCard">
      <div class="setingList">
        <div>
          <div class="seting-title">{{ item.configTitle }}</div>
          <div class="seting-description">{{ item.configDesc }}</div>
        </div>
        <div class="seting-switch">
          <el-switch
            v-model="item.configSwith"
            active-color="#13ce66"
            inactive-color="#ff4949"
            @change="onSwitchChange($event,item)"
          >
          </el-switch>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  getList,
  setConfig
} from '@/api/system/setting'
export default {
  components: {},
  data() {
    return {
      list: []
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getList()
  },
  mounted() {

  },
  methods: {
    // 获取数据
    getList() {
      getList().then(response => {
        this.list = response
      })
    },
    onSwitchChange(event, item) {
      setConfig(item).then(response => {
        this.$notify({
          title: '成功',
          message: '更新成功',
          type: 'success',
          duration: 2000
        })
      })
    }
  }
}
</script>
<style>
.setingCard{
	margin-bottom: 10px;
}
.setingList{
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.seting-title{
	font-weight: 500;
}
.seting-description{
	margin-top: 10px;
	color: #999999;
}
</style>
