<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        v-permission="['sys:menu:save']"
        style="margin-left: 10px;"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >添加</el-button>
    </div>
    <el-table
      row-key="id"
      :data="list"
      fit
      highlight-current-row
      default-expand-all
      :row-class-name="treeRowClassName"
      style="width: 100%;"
    >
      <el-table-column label="菜单名称" prop="name" align="left">
        <template slot-scope="{row}">
          <svg-icon v-if="row.icon != null" :icon-class="row.icon" />
          <span>{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" prop="typeDescribe" width="60px" align="center" />
      <el-table-column label="排序号" prop="orderNum" width="70px" align="center" />
      <el-table-column label="菜单URL" prop="url" width="80px" align="center" />
      <el-table-column label="组件路径" prop="component" width="150px" align="center" />
      <el-table-column label="权限标识" prop="perms" width="150px" align="center" />
      <el-table-column label="是否隐藏" prop="hiddenDescribe" width="80px" align="center" />
      <el-table-column label="创建时间" prop="createdAt" width="140px" align="center" />
      <el-table-column label="操作" align="center" width="150px">
        <template slot-scope="{row}">
          <el-button
            v-waves
            v-permission="['sys:menu:update']"
            type="primary"
            icon="el-icon-edit"
            @click="handleUpdate(row)"
          />
          <el-button
            v-waves
            v-permission="['sys:menu:delete']"
            icon="el-icon-delete"
            type="danger"
            @click="handleDelete(row)"
          />
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      top="2vh"
      width="500px"
    >
      <el-form
        ref="dataForm"
        :model="menu"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item label="类型" prop="type">
          <DictRadio v-model="menu.type" type="menu_type" />
        </el-form-item>
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="menu.name" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="上级菜单" prop="parentId">
          <el-cascader
            v-model="menu.parentId"
            :options="menuList"
            :props="menuProps"
            clearable
            style="width:310px"
          />
        </el-form-item>
        <el-form-item v-if="menu.type != 2" label="菜单URL" prop="url">
          <el-input v-model="menu.url" placeholder="请输入菜单URL" />
        </el-form-item>
        <el-form-item v-if="menu.type == 1 || menu.type == 2" label="权限标识" prop="perms">
          <el-input v-model="menu.perms" placeholder="请输入权限标识" />
        </el-form-item>
        <el-form-item v-if="menu.type == 1" label="组件路径" prop="component">
          <el-input v-model="menu.component" placeholder="请输入组件路径" />
        </el-form-item>
        <el-form-item label="是否隐藏" prop="hidden">
          <DictRadio v-model="menu.hidden" type="menu_hidden" />
        </el-form-item>
        <el-form-item v-if="menu.type != 2" label="排序号" prop="perms">
          <el-input-number v-model="menu.orderNum" :min="1" />
        </el-form-item>
        <el-form-item v-if="menu.type != 2" label="菜单图标">
          <el-input v-model="menu.icon" placeholder="请选择菜单图标" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogFormVisible = false">取消</el-button>
        <el-button
          v-waves
          type="primary"
          @click="dialogStatus==='create'?createData():updateData()"
        >确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, get, getSelect, create, update, del } from '@/api/system/menu'
import waves from '@/directive/waves' // Waves directive
import {} from '@/utils'
import DictRadio from '@/components/DictRadio'
export default {
  name: 'MenuTable',
  directives: { waves },
  components: { DictRadio },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      listQuery: {
        orderByField: 'orderNum',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      menu: {
        orderNum: 1
      },
      rules: {
        type: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
        name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        component: [
          { required: true, message: '请输入组件路径', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '请选择上级菜单', trigger: 'blur' }
        ]
      },
      menuList: [],
      menuProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      }
    }
  },
  created() {
    this.getList()
    this.getSelect()
  },
  methods: {
    treeRowClassName({ row, rowIndex }) {
      if (row.parentId === -1) {
        return 'level-1-row'
      }
    },
    getSelect() {
      getSelect().then(response => {
        this.menuList = response
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
      })
    },
    handleFilter() {
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.menu = {
          orderNum: 1,
          hidden: 0
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleCreate() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    createData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid && this.validator()) {
          create(this.menu).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getMenuList()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    handleUpdate(row) {
      this.dialogStatus = 'update'
      this.dialogFormVisible = true
      this.resetTemp()
      get(row.id).then(response => {
        this.menu = response
      })
    },
    updateData() {
      this.$refs['dataForm'].validate(valid => {
        if (valid && this.validator()) {
          update(this.menu).then(() => {
            this.dialogFormVisible = false
            this.getList()
            this.getSelect()
            this.$notify({
              title: '成功',
              message: '更新成功',
              type: 'success',
              duration: 2000
            })
          })
        }
      })
    },
    validator() {
      if (this.menu.type === 1) {
        if (!this.menu.url) {
          this.$notify({
            title: '参数错误',
            message: '菜单URL不能为空',
            type: 'error',
            duration: 2000
          })
          return false
        }
      }
      return true
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该菜单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        del([row.id]).then(response => {
          this.$notify({
            title: '成功',
            message: '删除成功',
            type: 'success',
            duration: 2000
          })
          this.getList()
          this.getSelect()
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .level-1-row {
  background: #eeecec;
}
</style>
