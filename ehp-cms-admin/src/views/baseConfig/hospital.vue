<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        placeholder="医院ID"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="医院名称"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-cascader
        v-model="listQuery.cityId"
        placeholder="请选择城市"
        :options="cityData"
        :props="cityProps"
        :show-all-levels="false"
        clearable
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.hide"
        placeholder="是否停用"
        type="hospital_hide_status"
        @change="handleFilter"
      />

      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <!-- v-permission="['user:hospital:save']" -->
      <el-button
        type="primary"
        @click="addHospitalt"
      >添加医院
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="ID"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column
        label="医院名称"
        prop="name"
        width="150"
        align="center"
      />
      <el-table-column
        label="联系方式"
        prop="phone"
        width="150"
        align="center"
      />
      <el-table-column
        label="地址信息"
        prop="address"
        width="250"
        align="center"
      />
      <el-table-column
        label="乘车路线"
        prop="busRoute"
        width="150"
        align="center"
      />
      <el-table-column
        label="医院状态"
        prop="available"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag size="small" :type="row.hide == 0 ? '' : 'danger'">{{ row.hide == 0 ? '启用':'停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="网址"
        prop="website"
        width="150"
        align="center"
      />
      <el-table-column
        label="主要设备"
        prop="equipment"
        width="150"
        align="center"
      />
      <el-table-column
        label="创建时间"
        prop="createdAt"
        width="150"
        align="center"
      />
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >

        <template slot-scope="scope">
          <el-button
            v-if="scope.row.hide == 0"
            size="mini"
            type="danger"
            @click="changeStatus(scope.row.id,1)"
          >停用</el-button>
          <el-button
            v-else
            type="primary"
            size="mini"
            @click="changeStatus(scope.row.id,0)"
          >启用</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="record(scope.row)"
          >编辑信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogHospitaltTitle"
      :visible.sync="dialogTableVisible"
      width="80%"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="hospital"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="医院名称"
              prop="name"
            >
              <el-input v-model="hospital.name" :maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="联系方式"
              prop="phone"
            >
              <el-input v-model="hospital.phone" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="所在城市"
              prop="cityId"
            >
              <el-cascader
                v-model="hospital.cityId"
                :options="cityData"
                :props="cityProps"
                :show-all-levels="false"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="地址信息"
              prop="address"
              :maxlength="255"
            >
              <el-input v-model="hospital.address" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="级别">
              <el-input v-model="hospital.level" :maxlength="45" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="特色专科">
              <el-input v-model="hospital.specDepartment" :maxlength="600" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="主要设备" :maxlength="1500">
              <el-input v-model="hospital.equipment" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网址" :maxlength="255">
              <el-input v-model="hospital.website" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="巴士路线" :maxlength="255">
              <el-input v-model="hospital.busRoute" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="等级类型" prop="levelType">
              <DictSelect
                v-model="hospital.levelType"
                placeholder="请选择"
                type="hospital_level_type"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="启用状态"
              prop="hide"
            >
              <el-select
                v-model="hospital.hide"
                clearable
                style="width: 150px"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in available"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col
            :span="24"
            style="text-align: center;"
          >
            <el-button
              v-waves
              type="primary"
              @click="submit('dataForm')"
            >提交
            </el-button>
            <el-button
              v-waves
              type="primary"
              @click="close"
            >返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getCityList } from '@/api/user/pharmacist'
import {
  getList,
  updateHospital,
  addHospital,
  changehospitalStatus
} from '@/api/baseConfig/index'

import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'

export default {
  name: 'DoctorTable',
  directives: { waves },
  filters: {},
  components: {
    DictSelect
  },
  data() {
    var checkMyPhone = (rule, value, callback) => {
      if (value == '') {
        callback(new Error('联系电话不可为空！'))
      } else {
        let regPone = null
        const mobile = /^1(3|4|5|6|7|8|9)\d{9}$/ //最新16手机正则
        const tel = /^(0\d{2,3}-){0,1}\d{7,8}$/ //座机
        // let mobile = /^([0-9]{3,4}-)?[0-9]{7,8}$/;
        // let tel=/^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[02356789][0-9]{8}|147[0-9]{8}|1349[0-9]{7})$/;
        if (value.length == 11) {
          regPone = mobile
        } else {
          regPone = tel
        }
        if (!regPone.test(value)) {
          callback(new Error('请输入正确的电话格式！'))
        }
        callback()
      }
    }
    return {
      dialogTableVisible: false,
      dialogHospitaltTitle: '医院详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        id: '',
        name: '',
        cityId: '',
        hide: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      hospital: {},
      available: [
        {
          value: 0,
          label: '启用'
        },
        {
          value: 1,
          label: '停用'
        }
      ],
      cityData: null,
      cityProps: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      rules: {
        name: [{ required: true, message: '请输入医院姓名', trigger: 'blur' }],
        address: [
          { required: true, message: '请输入地址信息', trigger: 'blur' }
        ],
        cityId: [
          { required: true, message: '请输入所在城市', trigger: 'blur' }
        ],
        phone: [
          { validator: checkMyPhone, required: true, trigger: 'blur' }
        ],
        hide: [{ required: true, message: '请选择启用状态', trigger: 'blur' }],
        levelType: [{ required: true, message: '请选择等级类型', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.handleFilter()
    this.getCityList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.cityId = ''
      this.listQuery.hide = ''
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        const files = document.querySelectorAll('.imgfile')
        if (files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            files[i].value = null
          }
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    addHospitalt() {
      this.hospital.id = null
      this.dialogHospitaltTitle = '添加医院'
      this.dialogTableVisible = true
      this.resetTemp()
      this.hospital = {}
    },
    submit(dataForm) {
      console.log(this.$refs[dataForm])
      this.$refs[dataForm].validate((valid) => {
        console.log('valid', valid)
        if (valid) {
          const params = this.hospital
          delete params.sex
          delete params.birth
          if (this.hospital.id) {
            updateHospital(params, this.hospital.id).then((res) => {
              this.$message({
                message: '修改成功',
                type: 'success',
                duration: 5 * 1000
              })
              this.getList()
              this.dialogTableVisible = false
            })
          } else {
            addHospital(params).then((res) => {
              this.$message({
                message: '添加成功',
                type: 'success',
                duration: 5 * 1000
              })
              this.getList()
              this.dialogTableVisible = false
            })
          }
        }
      })
    },
    close() {
      this.dialogTableVisible = false
    },
    record(item) {
      this.dialogHospitaltTitle = '编辑医院'
      this.dialogTableVisible = true
      this.resetTemp()
      this.hospital = Object.assign({}, item)
    },
    getCityList() {
      getCityList().then((response) => {
        this.cityData = response
      })
    },
    changeStatus(id, status) {
      if (status === 0) {
        this.$confirm('是否启用医院？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      } else {
        this.$confirm('是否停用医院？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      }
    },
    // 修改药师状态
    sureStatus(id, status) {
      changehospitalStatus({
        id: id,
        status: status
      }).then((res) => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    }
  }
}
</script>
