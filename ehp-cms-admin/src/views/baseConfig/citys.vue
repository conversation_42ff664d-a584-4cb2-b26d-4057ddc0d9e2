<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        placeholder="城市ID"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="城市名称"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-cascader
        v-model="listQuery.parentId"
        placeholder="上级城市"
        :options="cityData"
        :props="cityProps"
        clearable
        @change="handleFilter"
      />
      <el-select
        v-model="listQuery.type"
        clearable
        style="width: 150px"
        placeholder="类型"
        @change="handleFilter"
      >
        <el-option
          v-for="item in cityType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="listQuery.available"
        clearable
        style="width: 150px"
        placeholder="是否停用"
        @change="handleFilter"
      >
        <el-option
          v-for="item in available"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        type="primary"
        @click="addcityst"
      >添加城市
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="ID"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column
        label="城市名称"
        prop="name"
        width="150"
        align="center"
      />
      <el-table-column
        label="全称"
        prop="fullName"
        width="150"
        align="center"
      />
      <el-table-column
        label="城市状态"
        prop="available"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag size="small" :type="row.available ? '' : 'danger'">{{ row.available ? '启用':'停用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="直辖市"
        prop="municipality"
        width="90"
        align="center"
      >
        <template slot-scope="{row}">
          <span
            v-if="row.municipality === 0"
            size="small"
          >不是</span>
          <span
            v-if="row.municipality === 1"
            size="small"
          >是</span>
        </template>
      </el-table-column>

      <el-table-column
        label="创建人"
        prop="createdBy"
        width="150px"
        align="center"
      />
      <el-table-column
        label="创建时间"
        prop="createdAt"
        align="center"
        width="155px"
      />
      <el-table-column
        label="修改人"
        prop="changedBy"
        width="155px"
        align="center"
      />
      <el-table-column
        label="修改时间"
        prop="changedAt"
        align="center"
      />
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.available"
            size="mini"
            type="danger"
            @click="changeStatus(scope.row.id,0)"
          >停用</el-button>
          <el-button
            v-else
            type="primary"
            size="mini"
            @click="changeStatus(scope.row.id,1)"
          >启用</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="record(scope.row)"
          >编辑信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogcitystTitle"
      :visible.sync="dialogTableVisible"
      width="80%"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="citys"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="城市名称"
              prop="name"
            >
              <el-input
                v-model="citys.name"
                :maxlength="20"
                placeholder="请输入城市名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否热门城市"
              prop="hot"
            >
              <el-select
                v-model="citys.hot"
                clearable
                style="width: 150px"
                placeholder="是否热门城市"
              >
                <el-option
                  v-for="item in selectArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="城市类型"
              prop="type"
            >
              <el-select
                v-model="citys.type"
                clearable
                style="width: 150px"
                placeholder="类型"
              >
                <el-option
                  v-for="item in cityType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="直辖市"
              prop="municipality"
            >
              <el-select
                v-model="citys.municipality"
                clearable
                style="width: 150px"
                placeholder="是否是直辖市"
              >
                <el-option
                  v-for="item in selectArr"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col v-if="citys.parentId!=0" :span="12">
            <el-form-item
              label="上级地区"
              prop="parentId"
            >
              <el-cascader
                v-model="citys.parentId"
                :options="cityData"
                :props="props"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="启用状态"
              prop="available"
            >
              <el-select
                v-model="citys.available"
                clearable
                style="width: 150px"
              >
                <el-option
                  v-for="item in available"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col
            :span="24"
            style="text-align: center;"
          >
            <el-button
              v-waves
              type="primary"
              @click="submit('dataForm')"
            >提交
            </el-button>
            <el-button
              v-waves
              type="primary"
              @click="close"
            >返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getCityList } from '@/api/user/pharmacist'
import { getList, changeCityStatus, addCity } from '@/api/baseConfig/citys'

import waves from '@/directive/waves' // Waves directive
import DictSelect from '@/components/DictSelect'

export default {
  name: 'DoctorTable',
  directives: { waves },
  filters: {},
  components: {
    DictSelect
  },
  data() {
    return {
      dialogTableVisible: false,
      dialogcitystTitle: '城市详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderBy: 'desc',
        id: '',
        name: '',
        parentId: '',
        type: '',
        available: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      cityType: [
        {
          value: 0,
          label: '全国'
        },
        {
          value: 1,
          label: '省'
        },
        {
          value: 2,
          label: '市'
        },
        {
          value: 3,
          label: '区'
        }
      ],
      selectArr: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ],
      available: [
        {
          value: true,
          label: '启用'
        },
        {
          value: false,
          label: '停用'
        }
      ],
      props: {
        value: 'id',
        emitPath: false,
        label: 'name',
        checkStrictly: true
      },
      cityProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      cityData: null,
      citys: {},
      authStatusArr: ['正常', '屏蔽'],
      rules: {
        name: [{ required: true, message: '请输入城市姓名', trigger: 'blur' }],
        type: [{ required: true, message: '请选择城市类型', trigger: 'blur' }],
        municipality: [{ required: true, message: '请选择是否是直辖市', trigger: 'blur' }],
        parentId: [{ required: true, message: '请选择上级地区', trigger: 'blur' }],
        available: [{ required: true, message: '请选择启用状态', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.handleFilter()
    this.getCityList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.parentId = ''
      this.listQuery.type = ''
      this.listQuery.available = ''
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        const files = document.querySelectorAll('.imgfile')
        if (files.length > 0) {
          for (let i = 0; i < files.length; i++) {
            files[i].value = null
          }
        }
        this.$refs['dataForm'].clearValidate()
      })
    },
    addcityst() {
      this.citys.id = null
      this.dialogcitystTitle = '添加城市'
      this.dialogTableVisible = true
      this.resetTemp()
      this.citys = {}
    },
    submit(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        console.log('valid', valid)
        if (valid) {
          const params = this.citys
          addCity(params).then((res) => {
            this.$message({
              message: this.citys.id ? '修改成功' : '添加成功',
              type: 'success',
              duration: 5 * 1000
            })
            this.getCityList()
            this.getList()
            this.dialogTableVisible = false
          })
        }
      })
    },
    close() {
      this.dialogTableVisible = false
    },
    record(item) {
      this.dialogcitystTitle = '编辑城市'
      this.dialogTableVisible = true
      this.resetTemp()
      this.citys = Object.assign({}, item)
    },
    getCityList() {
      getCityList({ type: 2 }).then((response) => {
        this.cityData = response
      })
    },
    changeStatus(id, status) {
      if (status === 1) {
        this.$confirm('是否启用城市？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      } else {
        this.$confirm('是否停用城市？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, status)
        })
      }
    },
    // 修改药师状态
    sureStatus(id, status) {
      changeCityStatus({
        id: id,
        status: status
      }).then((res) => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    }
  }
}
</script>
