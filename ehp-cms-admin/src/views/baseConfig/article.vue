<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        clearable
        placeholder="标题"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button v-waves type="primary" @click="addArticle">添加 </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
      <el-table-column label="协议名称" prop="title" align="center" />
      <el-table-column label="备注" prop="remarks" align="center" />
      <el-table-column label="发布时间" prop="createdAt" width="155px" align="center" />
      <el-table-column label="操作" width="200px" align="center">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="getDetail(scope.row.id)">编辑信息 </el-button>
          <el-button type="danger" size="mini" @click="deleteItem(scope.row.id)">删除 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog :title="dialogArticleTitle" :visible.sync="dialogTableVisible" width="80%" top="2vh">
      <el-form ref="dataForm" :model="article" :rules="rules" label-position="right" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="业务标识" prop="type">
              <el-input v-model="article.type" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="协议名称" prop="title">
              <el-input v-model="article.title" maxlength="50" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <wangEditor ref="editor" :text-value.sync="article.content" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input v-model="article.remarks" maxlength="20" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" style="text-align: center;">
            <el-button v-waves type="primary" @click="submit('dataForm')">发布 </el-button>
            <el-button v-waves type="primary" @click="close">返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/baseConfig/article'
import waves from '@/directive/waves' // Waves directive
import wangEditor from '@/components/wangEditor'
export default {
  name: 'DoctorTable',
  directives: { waves },
  filters: {},
  components: {
    wangEditor
  },
  data() {
    return {
      dialogTableVisible: false,
      dialogArticleTitle: '文章详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        title: ''
      },
      dialogFormVisible: false,
      dialogStatus: '',
      article: {
        id: null,
        title: '',
        content: '',
        releaseTime: ''
      },
      rules: {
        type: [{ required: true, message: '请输入标识', trigger: 'blur' }],
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        releaseTime: [{ required: true, message: '请输入发布时间', trigger: 'blur' }]
      },
      dateFormat: 'yyyy-MM-dd'
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      API.getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.title = ''
      this.handleFilter()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    addArticle() {
      this.article.id = null
      this.dialogArticleTitle = '添加文章'
      this.dialogTableVisible = true
      this.resetTemp()
      this.article = {}
      this.$nextTick(() => {
        this.$refs.editor.setValue('')
      })
    },
    submit(dataForm) {
      console.log(this.article, '===========submit========')
      this.$refs[dataForm].validate((valid) => {
        console.log('valid', valid)
        if (valid) {
          const params = Object.assign({}, this.article)
          console.log(params, '===========11submit11========')
          if (this.article.id) {
            API.updateArticle(params, this.article.id).then((res) => {
              this.$message.success('保存成功')
              this.getList()
              this.dialogTableVisible = false
            })
          } else {
            API.addArticle(params).then((res) => {
              this.$message.success('保存成功')
              this.getList()
              this.dialogTableVisible = false
            })
          }
        }
      })
    },
    close() {
      this.dialogTableVisible = false
    },
    getDetail(id) {
      API.getDetail(id).then((res) => {
        console.log(res, 225)
        this.dialogArticleTitle = '编辑文章'
        this.dialogTableVisible = true
        this.resetTemp()
        this.article = res
        this.$nextTick(() => {
          this.$refs.editor.setValue(res.content)
        })
      })
    },
    deleteItem(id) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return API.deleteArticle(id)
        })
        .then((res) => {
          this.$message.success('删除成功')
          this.getList()
        })
        .catch((e) => {})
        .finally((res) => {})
    }
  }
}
</script>
