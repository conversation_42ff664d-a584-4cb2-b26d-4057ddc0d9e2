<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.hospitalId"
        clearable
        class="filter-item"
        placeholder="医院ID"
        style="width: 150px;"
        onkeyup="value=value.replace(/[^\d]/g,'')"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.hospitalName"
        clearable
        class="filter-item"
        placeholder="医院名称"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button
        v-waves
        v-permission="['base:partner:hospital:save']"
        type="primary"
        icon="el-icon-add"
        @click="addHospital"
      >添加</el-button>
    </div>

    <el-table
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" prop="hospitalId" align="center" width="90" />
      <el-table-column label="医院名称" prop="hospitalName" align="center" width="250" />
      <el-table-column label="联系方式" prop="phone" align="center" width="150" />
      <el-table-column label="地址信息" prop="address" align="center" />
      <el-table-column label="合作协议" prop="filename" align="center">
        <template slot-scope="scope">
          <span style="color: #4285ff; cursor: pointer;" @click="previewPdf(scope.row.agreementFileUrl)">{{
            scope.row.filename
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-permission="['base:partner:hospital:save']"
            type="primary"
            size="mini"
            @click="editRow(scope.row)"
          >编辑</el-button>
          <el-button
            v-permission="['base:partner:hospital:delete']"
            type="danger"
            size="mini"
            @click="deleteRow(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchList"
    />

    <!-- 添加医院 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogTableVisible"
      :close-on-click-modal="false"
      width="30%"
      @close="handleCancle"
    >
      <el-form ref="dataForm" v-loading="uploadLoading" :model="form" :rules="rules" label-position="right" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="选择合作医院" prop="hospital">
              <el-select
                v-model="form.hospital"
                filterable
                remote
                reserve-keyword
                placeholder="输入医院名称"
                :remote-method="remoteMethod"
                :disabled="dialogTitle === '编辑' ? true : false"
              >
                <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="会诊合作协议">
              <el-upload
                ref="upload"
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :on-remove="handleRemove"
                :limit="1"
                accept=".pdf"
                :file-list="fileList"
                :on-change="handleFileChange"
              >
                <el-button size="small"><i class="el-icon-upload el-icon--right"></i>上传协议</el-button>
                <div slot="tip" class="el-upload__tip">支持扩展名：pdf</div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" style="text-align: center;">
            <el-button v-waves @click="handleCancle('dataForm')">取消</el-button>
            <el-button v-waves type="primary" :disabled="!(form.hospital && form.fileUrl)" @click="submit('dataForm')">确定</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>

    <!-- pdf预览 -->
    <el-dialog title="合作协议" :visible.sync="pdfDialogVisible" class="pdf-dialog">
      <div class="dialog-content">
        <iframe ref="pdfIframe" :src="pdfsrc" width="100%" height="800px" style="border: 0;"></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="pdfDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { getHospList, upload, editHospital, deleteHospital } from '@/api/baseConfig/consultation.js'
import { getList } from '@/api/baseConfig/index'
export default {
  name: 'Consultation',
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        hospitalId: '',
        hospitalName: '',
        pageNo: 1,
        pageSize: 10
      },
      dialogTitle: '添加',
      dialogTableVisible: false,
      form: {
        hospital: '',
        fileUrl: ''
      },
      rules: {
        hospital: [{ required: true, message: '请选择合作医院', trigger: 'blur' }],
        file: [{ required: true, message: '请上传协议', trigger: 'change' }]
      },
      fileList: [],
      options: [],
      filename: '',
      pdfDialogVisible: false,
      pdfsrc: '',
      hospitalId: '',
      uploadLoading: false
    }
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    // 获取列表数据
    async fetchList() {
      try {
        const response = await getHospList(this.listQuery)
        this.list = response.list
        this.total = response.totalCount
      } catch (error) {
        throw new Error(error)
      }
    },
    // 查询
    handleFilter() {
      this.listQuery.pageNo = 1
      this.fetchList()
    },
    // 重置
    handleReset() {
      this.listQuery.hospitalId = ''
      this.listQuery.hospitalName = ''
      this.handleFilter()
    },
    // 添加医院
    addHospital() {
      this.dialogTitle = '添加'
      this.dialogTableVisible = true
      this.fileList = []
      this.form.hospital = ''
      this.form.fileUrl = ''
    },
    // 编辑
    editRow(row) {
      console.log(row)
      this.dialogTitle = '编辑'
      this.dialogTableVisible = true
      // this.form.hospital = row.hospitalName
      // this.hospitalId = row.hospitalId
      this.fileList = [{ name: row.filename, url: row.agreementFileUrl }]
      this.form.fileUrl = row.agreementFileUrl
      this.filename = row.filename
      this.options = [{ id: row.hospitalId, name: row.hospitalName }]
      this.form.hospital = row.hospitalId
    },
    // 删除
    async deleteRow(row) {
      try {
        await deleteHospital(row.hospitalId)
        this.$message.success('删除成功')
        this.fetchList()
      } catch (error) {
        throw new Error(error)
      }
    },
    // 关闭
    handleCancle() {
      this.dialogTableVisible = false
      this.$refs['dataForm'].resetFields()
    },
    handleRemove(file, fileList) {
      this.fileList = []
      this.form.fileUrl = ''
    },
    // 远程搜索
    async remoteMethod(query) {
      if (query !== '') {
        const { list } = await getList({ name: query })
        console.log(list)
        this.options = list
      } else {
        this.options = []
      }
    },
    async getHospId(name) {
      if (name) {
        const { list } = await getList({ name: name })
        console.log(list)
      }
    },
    async handleFileChange(file, filelist) {
      console.log(file)
      // 上传前的文件检查
      const isPDF = file.raw.type === 'application/pdf'
      if (!isPDF) {
        this.$message.error('只能上传PDF文件!')
        this.fileList = []
        return false
      }
      // 例如限制文件大小小于 10MB
      const isLt10M = file.size / 1024 / 1024 < 5
      if (!isLt10M) {
        this.$message.error('上传的PDF文件大小不能超过5MB!')
        this.fileList = []
        return false
      }
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('filename', file.name)
      this.uploadLoading = true
      try {
        const response = await upload(formData)
        console.log(response)
        this.form.fileUrl = response
        this.filename = file.name
        this.uploadLoading = false
        this.$message.success('上传成功')
      } catch (error) {
        throw new Error(error)
      } finally {
        this.uploadLoading = false
      }
    },
    // 确定
    submit(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (!this.form.fileUrl) {
          this.$message.warning('请上传合作协议')
          return
        }
        if (valid) {
          const params = {
            agreementFileUrl: this.form.fileUrl,
            hospitalId: this.form.hospital,
            filename: this.filename
          }
          editHospital(params)
            .then((result) => {
              console.log(result)
              this.dialogTableVisible = false
              this.fetchList()
            })
            .catch((err) => {
              throw new Error(err)
            })
        }
      })
    },
    // 预览pdf
    previewPdf(pdfUrl) {
      this.pdfsrc = pdfUrl
      this.pdfDialogVisible = true
    }
  }
}
</script>

<style lang="scss" scoped></style>
