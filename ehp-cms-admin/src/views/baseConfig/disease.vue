<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        clearable
        class="filter-item"
        placeholder="疾病名称"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button v-waves type="primary" icon="el-icon-add" @click="addDisease">添加</el-button>
    </div>

    <el-table
      :data="list"
      fit
      highlight-current-row
      style="width: 100%;"
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="ID" prop="id" align="center" width="90" />
      <el-table-column label="疾病名称" prop="name" align="center" width="200" />
      <el-table-column label="排序" prop="sort" align="center" />
      <el-table-column label="创建时间" prop="createdAt" align="center" />
      <el-table-column label="修改时间" prop="changedAt" align="center" width="155" />
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" @click="edit(scope.row)">编辑</el-button>
          <el-button type="danger" size="mini" @click="deleteRow(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchList"
    />

    <!-- 添加疾病 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible" width="30%" @close="handleCancle">
      <el-form ref="dataForm" :model="diseaseForm" :rules="rules" label-position="right" label-width="130px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="疾病名称" prop="name">
              <el-input v-model="diseaseForm.name" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="排序号" prop="sort">
              <el-input-number v-model="diseaseForm.sort" style="width: 50%;"></el-input-number><br />
              <span>常见疾病依据排序号从小到大展示</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24" style="text-align: center;">
            <el-button v-waves @click="handleCancle('dataForm')">取消</el-button>
            <el-button v-waves type="primary" @click="submit('dataForm')">确定</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getList, editDisease, deleteDisease } from '@/api/baseConfig/disease'
import waves from '@/directive/waves'
export default {
  name: 'Disease',
  directives: { waves },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        name: '',
        pageNo: 1,
        pageSize: 10
      },
      dialogTitle: '添加疾病',
      dialogTableVisible: false,
      diseaseForm: {
        name: '',
        sort: ''
      },
      rowId: '',
      rules: {
        name: [{ required: true, message: '请输入疾病姓名', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }]
      },
      maxSort: null
    }
  },
  mounted() {
    this.fetchList()
  },
  methods: {
    // 获取列表数据
    async fetchList() {
      try {
        const response = await getList(this.listQuery)
        const numbers = response.list.map(item => item.sort)
        if (numbers.length > 0) {
          this.maxSort = Math.max(...numbers) + 1
        }
        this.list = response.list
        this.total = response.totalCount
      } catch (error) {
        console.log(error)
      }
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.fetchList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.handleFilter()
    },
    // 添加疾病
    addDisease() {
      this.dialogTitle = '添加疾病'
      this.dialogTableVisible = true
      this.diseaseForm.name = ''
      this.diseaseForm.sort = this.maxSort
    },
    edit(row) {
      this.dialogTitle = '编辑疾病'
      this.dialogTableVisible = true
      this.diseaseForm.name = row.name
      this.diseaseForm.sort = row.sort
      this.rowId = row.id
      console.log(this.diseaseForm.rowId)
    },
    async deleteRow(row) {
      try {
        const response = await deleteDisease(row.id)
        console.log(response)
        this.$message.success('删除成功')
        this.fetchList()
      } catch (error) {
        console.log(error)
      }
    },
    // 关闭
    handleCancle() {
      this.dialogTableVisible = false
      this.$refs['dataForm'].resetFields()
    },
    // 确定
    submit(dataForm) {
      this.$refs[dataForm].validate(async(valid) => {
        if (valid) {
          if (this.dialogTitle === '添加疾病') {
            try {
              await editDisease(this.diseaseForm)
              this.dialogTableVisible = false
              this.$message.success('添加成功')
              this.fetchList()
            } catch (error) {
              console.log(error)
            }
          } else {
            try {
              const params = { id: this.rowId, name: this.diseaseForm.name, sort: this.diseaseForm.sort }
              await editDisease(params)
              this.dialogTableVisible = false
              this.$message.success('编辑成功')
              this.fetchList()
            } catch (error) {
              console.log(error)
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
