<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.number"
        placeholder="药品编码"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        placeholder="药品名称"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.status"
        clearable
        style="width: 110px;"
        placeholder="药品状态"
        @change="handleFilter"
      >
        <el-option
          v-for="item in drugStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="listQuery.type"
        clearable
        style="width: 110px;"
        placeholder="药品类型"
        @change="handleFilter"
      >
        <el-option
          v-for="item in nmpaType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="listQuery.addMedicineTag"
        clearable
        style="width: 150px;"
        placeholder="是否添加为商品"
        @change="handleFilter"
      >
        <el-option
          label="否"
          :value="0"
        ></el-option>
        <el-option
          label="是"
          :value="1"
        ></el-option>
      </el-select>
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-permission="['wms:medicine:update']"
        type="primary"
        @click="addSkuDialogShow('isShowSetTime')"
      >同步药品</el-button>
      <el-button
        v-permission="['wms:medicine:update']"
        type="primary"
        @click="addSkuDialogShow('isAddDrug')"
      >添加药品</el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
    >
      <el-table-column
        label="药品编码"
        prop="number"
        width="120px"
        align="center"
      />
      <el-table-column
        label="药品名称"
        prop="name"
        align="center"
        width="180px"
      />
      <el-table-column
        label="通用名"
        prop="commonName"
        width="150px"
        align="center"
      />
      <el-table-column
        label="规格"
        prop="spec"
        align="center"
        width="200px"
      />
      <el-table-column
        label="批文文号"
        prop="approvalNumber"
        width="200px"
        align="center"
      />
      <el-table-column
        label="本位码"
        prop="standardCode"
        align="center"
        width="120px"
      />
      <el-table-column
        label="生产厂商"
        prop="productionEnterprise"
        width="200px"
        align="center"
      />
      <el-table-column
        label="药品类型"
        prop="type"
        width="150px"
        align="center"
      >
        <template slot-scope="{row}">
          <span v-if="row.type == 1">西药</span>
          <span v-if="row.type == 2">中成药</span>
          <span v-if="row.type == 3">中草药</span>
        </template>
      </el-table-column>
      <el-table-column
        label="药品状态"
        prop="status"
        width="150px"
        align="center"
      >
        <template slot-scope="{row}">
          {{ row.status ? '可用': '不可用' }}
        </template>
      </el-table-column>
      <el-table-column
        label="计量单位"
        prop="unit"
        width="200px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="是否添加为商品"
        prop="addMedicineTag"
        width="150px"
        align="center"
      >
        <template slot-scope="{row}">
          {{ row.addMedicineTag ? '是': '否' }}
        </template>
      </el-table-column>
      <el-table-column
        label="更新时间"
        prop="updatedAt"
        width="150px"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        width="150px"
        align="center"
      >
        <template slot-scope="{row}">
          <el-button
            v-permission="['wms:medicine:update']"
            :disabled="row.addMedicineTag ==1"
            type="primary"
            @click="editProduct(row.id)"
          >添加为商品</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加药品 -->
    <el-dialog
      title="添加药品"
      :visible.sync="isAddDrug"
    >
      <el-input
        v-model="numbers"
        type="textarea"
        :autosize="{ minRows: 4, maxRows: 4}"
        placeholder="可添加多个药品，药品编码之间用 | 隔开，每次最多20个"
      />
      <!-- <div style="margin-top:10px">共20条，失败10条</div>
      <div style="margin-top:10px">失败药品编码</div>
      <div style="margin-top:10px">21321321321321</div> -->
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="handleSkuAdd(1)"
        >提交</el-button>
      </div>
    </el-dialog>
    <!-- 同步药品 -->
    <el-dialog
      title="药品更新时间"
      width="300px"
      :visible.sync="isShowSetTime"
    >
      <DatePicker
        gte="start"
        lte="end"
        :query-model="addQuery"
        class="filter-item"
        style="width: 220px"
      />
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="handleSkuAdd(2)"
        >提交</el-button>
      </div>
    </el-dialog>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>
<style>
/*.el-form-item .el-input__inner{width:auto;}*/
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
</style>
<script>
import api_product from '@/api/product/product'
import api_base from '@/api/product/base'
import DatePicker from '@/components/DatePicker'
export default {
  name: 'Productdrug',
  filters: {},
  components: {
    DatePicker
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      isAddDrug: false,
      isShowSetTime: false,
      formLabelWidth: '25%',
      createInfo: {},
      rules: {
        type: [{ required: true, message: '请选择品类', trigger: 'change' }]
      },
      drugStatus: [
        {
          value: 1,
          label: '可用'
        },
        {
          value: 0,
          label: '不可用'
        }
      ],
      nmpaType: [
        {
          value: 1,
          label: '西药'
        },
        {
          value: 2,
          label: '中成药'
        },
        {
          value: 3,
          label: '中草药'
        }
      ],
      value: '',
      drugContent: '',
      numbers: '',
      addQuery: {
        numbers: ''
      }
    }
  },
  created() {
    // this.handleFilter()
    // this.getBaseData()
  },
  activated() {
    this.getBaseData()
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    // 获取数据
    getList() {
      api_product.skuList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getBaseData() {
      api_base.pharmacology().then((response) => {
        this.baseoptions = response
      })
    },
    handleSkuAdd(type) {
      console.log(this.addQuery)
      if (this.numbers) {
        this.addQuery.numbers = this.numbers
          .replace(/\s+/g, '')
          .replace(/\|/g, ',')
      }
      api_product.skuAdd(this.addQuery).then((response) => {
        console.log(response, 361)
        this.$message({
          message: type === 1 ? '添加成功' : '同步成功',
          type: 'success'
        })
        this.handleFilter()
        this.isAddDrug = false
        this.isShowSetTime = false
      })
    },
    addSkuDialogShow(name) {
      this.resetTemp()
      this[name] = true
    },
    handleFilter() {
      console.log(this.listQuery)
      this.listQuery.pageNo = 1
      this.getList()
    },
    editProduct(productId) {
      this.$router.push({
        path: './info/' + productId + '/2'
      })
    },
    editSku(productId) {
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.addQuery = {
          numbers: '',
          end: '',
          start: ''
        }
        this.numbers = ''
      })
    }
  }
}
</script>
