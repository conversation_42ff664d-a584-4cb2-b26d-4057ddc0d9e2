<template>
  <div class="app-container">
    <!-- <div class="title">新增药品</div> -->
    <div class="skuinfo">
      <el-row :gutter="20">
        <el-col class="line" :span="5">商品编码：{{ productData.medicine.number }}</el-col>
        <el-col class="line" :span="7">商品名称：{{ productData.medicine.name }}</el-col>
        <el-col class="line" :span="4">型剂：{{ productData.medicine.agentClassificationName }}</el-col>
        <el-col class="line" :span="4">规格：{{ productData.medicine.specification }}</el-col>
        <el-col class="linelast" :span="5">包装：{{ productData.medicine.packingUnit }}</el-col>
      </el-row>
      <el-row class="infomess" type="flex">
        <el-col style="width:60px;">适应症：</el-col>
        <el-col>{{ productData.medicine.indications }}</el-col>
      </el-row>
    </div>
    <el-row v-if="productData.list.length > 0" :gutter="15" type="flex" style="margin-top: 20px;">
      <el-col>
        <el-table :key="tableKey" :data="productData.list" fit highlight-current-row>
          <el-table-column label="编辑" prop="defaultSku" width="80px" align="center">
            <template slot-scope="scope">
              <label>
                <input v-model="editSkusIndex" type="radio" :value="scope.$index" />
              </label>
            </template>
          </el-table-column>
          <el-table-column label="ID" prop="id" align="center" width="100px" />
          <el-table-column label="sku编码" prop="number" width="160px" align="center" />
          <el-table-column label="sku名称" prop="name" align="center" />
          <el-table-column label="上下架状态" prop="statusDescribe" align="center" width="110px" />
          <!-- <el-table-column label="库存" prop="numberStores" width="80px" align="center" /> -->
          <el-table-column label="默认" prop="defaultSku" width="80px" align="center">
            <template slot-scope="{row}">
              <div class="fixedradio" @click="changeDefault(row)"></div>
              <label>
                <input v-model="defaultSkus" type="radio" :value="row.id" />
                <span v-if="row.defaultSku"></span>
                <span v-else></span>
              </label>
              <!-- <el-radio v-model="defaultSkus" :value="row.id"></el-radio> -->
            </template>
          </el-table-column>
        </el-table>
      </el-col>
      <el-col style="width:130px;">
        <el-button v-permission="['wms:medicine:sku:update']" type="primary" @click="newSku">新增sku</el-button>
      </el-col>
    </el-row>
    <!-- <el-radio-group v-model="slet">
    <el-radio :label="1"></el-radio>
    <el-radio :label="6"></el-radio>
    <el-radio :label="9"></el-radio>
    </el-radio-group>-->
    <el-form ref="dataForm" :model="skuData" :rules="rules" :inline="true" label-width="100px">
      <el-input v-model="skuData.productId" type="hidden" />
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            class="elbottom"
            label="包装规格"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ skuData.packingSpec }}</div>
          </el-form-item>
          <el-form-item class="elbottom" label="sku 名称" prop="brandName" style="display:block;">
            <div>{{ skuData.name }}</div>
          </el-form-item>
          <el-form-item label="是否默认" prop="defaultSku">
            <DictRadio v-model="skuData.defaultSku" type="default_sku" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规格明细" prop="brandName">
            <div style="padding:10px;border:1px solid #DCDFE6;border-radius:5px;">
              <el-row style="margin-bottom: 20px;">
                <el-form-item
                  :label="'每'+productData.medicine.packingUnit"
                  prop="packingUnitNumber"
                >
                  <el-input
                    v-model="skuData.packingUnitNumber"
                    style="width:70px;"
                    @keyup.native="changeName"
                  />
                  <span>{{ productData.medicine.packingUnitLimit }}</span>
                </el-form-item>
              </el-row>
              <el-row>
                <el-form-item label="拆分单位" prop="specUnit">
                  <DictSelect
                    v-model="skuData.specUnit"
                    style="width:100px"
                    placeholder="请选择"
                    type="product_packing_unit_limit"
                    @change="changeName"
                  />
                </el-form-item>
                <el-form-item label="拆分数量" prop="specValue">
                  <el-input
                    v-model="skuData.specValue"
                    style="width:60px;"
                    @keyup.native="changeName"
                  />
                </el-form-item>
              </el-row>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="border-top: 1px solid #DCDFE6;padding-top:20px;">
        <el-col :span="12">
          <el-form-item label="销售价格" prop="salePrice">
            <el-input
              v-model="skuData.salePrice"
              style="width:180px"
              clearable
              @keyup.native="formatPrice"
            >
              <template slot="append">元</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- <el-form-item label="包装重量" prop="weight">
            <el-input v-model="skuData.weight" style="width:150px" clearable>
              <template slot="append">g</template>
            </el-input>
          </el-form-item>-->
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上架状态" prop="status">
            <DictRadio v-model="skuData.status" type="sku_status" />
          </el-form-item>
        </el-col>
        <el-col :span="12"></el-col>
      </el-row>
      <el-row :gutter="20" style="border-top: 1px solid #DCDFE6;padding-top:20px;">
        <el-col :span="24" class="labelitem">
          <el-form-item label="图片列表" label-width="100px" prop="images">
            <div class="imgcontent">
              <div class="imglist">
                <!-- <img-inputer v-model="file" theme="light" size="large"/> -->
                <div v-for="(fileitem,index) in skuData.images" :key="index" class="imgbox">
                  <div class="box">
                    <input
                      :id="'imgfile'+index"
                      :ref="'imgfile'+index"
                      :name="'imgfile'+index"
                      class="imgfile"
                      type="file"
                      multiple="false"
                      accept="image/png, image/gif, image/jpeg"
                      @change="handleFileChange($event,index)"
                    />
                    <label :for="'imgfile'+index"></label>
                    <!-- <i class="el-icon-circle-plus-outline"></i> -->
                    <div class="img">
                      <i v-if="index===0" class="el-icon-star-on start-on"></i>
                      <div>
                        <img :src="fileitem" />
                      </div>
                    </div>
                    <!-- other element-->
                  </div>
                  <div v-if="index===0" class="cz">
                    <i class="el-icon-star-on start-off"></i>
                    <i class="el-icon-d-arrow-left start-off"></i>
                    <i class="el-icon-d-arrow-right start-off"></i>
                    <span style="flex:1;"></span>
                    <i class="el-icon-circle-close start-off"></i>
                  </div>
                  <div v-else class="cz">
                    <i class="el-icon-star-on start-on" @click="checkIndex(index)"></i>
                    <i v-if="index===1" class="el-icon-d-arrow-left start-off"></i>
                    <i v-else class="el-icon-d-arrow-left start-on" @click="leftIndex(index)"></i>
                    <i
                      v-if="index < skuData.images.length - 1"
                      class="el-icon-d-arrow-right start-on"
                      @click="rightIndex(index)"
                    ></i>
                    <i v-else class="el-icon-d-arrow-right start-off"></i>
                    <span style="flex:1;"></span>
                    <i class="el-icon-circle-close icon-close" @click="delIndex(index)"></i>
                  </div>
                </div>
                <div v-if="skuData.images.length < 5" class="box">
                  <input
                    :id="'imgfile_'+skuData.images.length"
                    :ref="'imgfile_'+skuData.images.length"
                    :name="'imgfile_'+skuData.images.length"
                    class="imgfile"
                    type="file"
                    multiple="false"
                    accept="image/png, image/gif, image/jpeg"
                    @change="handleFileChange($event)"
                  />
                  <label :for="'imgfile_'+skuData.images.length"></label>
                  <i class="el-icon-circle-plus-outline"></i>
                </div>
              </div>
              <div class="imgtips">* 商品图片尺寸800X800，最少上传1张，最多可上传5张</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="el-dialog__footer">
      <div slot="footer" class="dialog-footer">
        <el-button
          v-permission="['wms:medicine:sku:update']"
          type="primary"
          @click="saveSku('dataForm')"
        >保存</el-button>
      </div>
    </div>
  </div>
</template>
<style scoped>
.imgfile {
  font-size: 0; /* 为了去掉‘未选择任何文件’这几个字，也可以随便弄到哪里*/
  position: absolute;
  left: -9999px;
}
/* 注意不是直接input > input[type=button] 哦*/
.imgfile::-webkit-file-upload-button {
  background: #efeeee;
  color: #333;
  border: 0;
  padding: 40px 100px;
  border-radius: 5px;
  font-size: 12px;
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1), 0 0 10px rgba(0, 0, 0, 0.12);
}
.box {
  position: relative;
  margin-bottom: 10px;
  width: 120px;
  height: 120px;
  font-size: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ccc;
}
/* 使label充满整个box*/
.box label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10; /* 这个z-index之后说到*/
}

.skuinfo {
  /* background-color: #dfe6ec; */
  padding: 10px;
  color: #606266;
  font-size: 14px;
}
.skuinfo .line {
  border-right: 1px solid #606266;
}
.skuinfo .linelast,
.skuinfo .line {
  padding: 10px 0;
}
.infomess {
  margin-top: 10px;
}
.elbottom {
  margin-bottom: 0;
}
.labelitem .el-form-item {
  display: flex;
}
.labelitem .el-form-item__content {
  flex: 1;
}
.imgcontent {
  /* background-color: #eee; */
  padding: 10px;
}
.imgtips {
  color: #f00;
  text-align: right;
}
.imglist {
  display: flex;
}
.imglist > div {
  margin-right: 10px;
}
.imgbox .img {
  position: relative;
  height: 120px;
  width: 120px;
  display: table;
  text-align: center;
}
.imgbox .img div {
  display: table-cell;
  vertical-align: middle;
}
.imgbox .img img {
  max-width: 118px;
  max-height: 118px;
}
.imgbox .img i {
  font-size: 22px;
  position: absolute;
  left: 0;
  top: 0;
}
.imgbox .cz {
  display: flex;
}
.imgbox .cz i {
  font-size: 22px;
  margin-right: 2px;
}
.imgadd {
  width: 120px;
  height: 120px;
  font-size: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #ccc;
}
.start-off {
  color: #ccc;
}
.start-on {
  color: #42b983;
}
.icon-close {
  color: #f00;
}
.fixedradio {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
</style>
<script>
import api_product from '@/api/product/product'
import DictSelect from '@/components/DictSelect'
import DictRadio from '@/components/DictRadio'
import { Loading } from 'element-ui'
export default {
  name: '',
  components: {
    DictSelect,
    DictRadio
  },
  data() {
    return {
      defaultSkus: 1, //默认id
      changeSku: {},
      editSkusIndex: 0, //编辑id
      dialogIndex: null,
      moveDirection: null,
      productData: {
        list: [],
        medicine: {}
      },
      skuData: {
        images: []
      },
      rules: {
        packingUnitNumber: [
          { required: true, message: '请输入每盒数量', trigger: 'blur' }
        ],
        salePrice: [
          { required: true, message: '请输入销售价格', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择安全类别', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入包装重量', trigger: 'blur' }
        ],
        packingUnitLimit: [
          { required: true, message: '请选择拆分单位', trigger: 'blur' }
        ],
        defaultSku: [
          { required: true, message: '请选择是否默认', trigger: 'blur' }
        ],
        images: [{ required: true, message: '请选择图片', trigger: 'blur' }]
      }
    }
  },
  watch: {
    editSkusIndex: function(newval, oldval) {
      if (newval !== null) {
        this.skuData = this.productData.list[newval]
      }
    }
  },
  created() {
    this.getProductInfo(this.$route.params.productId)
  },
  methods: {
    getProductInfo(productId) {
      api_product.infosku(productId).then(response => {
        this.productData = response
        if (response.list && response.list.length > 0) {
          this.skuData = response.list[0]
          for (var i = 0; i < response.list.length; i++) {
            if (response.list[i].defaultSku === 0) {
              this.defaultSkus = response.list[i].id
            }
          }
        } else if (response.list && response.list.length === 0) {
          this.skuData = {
            productId: this.productData.medicine.productId,
            // specUnit: this.productData.medicine.packingUnitLimit,
            images: []
          }
        }
      })
    },
    changeDefault(skuRow) {
      this.changeSku = skuRow

      this.$confirm('切换默认sku为：' + this.changeSku.name, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.defalutSure()
      })
    },
    defalutSure() {
      this.defaultSkus = this.changeSku.id
      this.changeSku.defaultSku = 1
      api_product
        .updateDefatule(this.changeSku.productId, this.changeSku.id)
        .then(response => {
          this.productData = response
          if (response.list && response.list.length > 0) {
            this.skuData = response.list[0]
          }
        })
    },
    saveSku(dataForm) {
      const loadingInstance = Loading.service({ fullscreen: true })
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          var sku = Object.assign({}, this.skuData)
          sku.weight = 1
          api_product
            .skuupdate(sku)
            .then(response => {
              this.productData = response
              if (this.editSkusIndex === null) {
                this.editSkusIndex = this.productData.list.length - 1
                if (
                  this.productData.list[this.productData.list.length - 1]
                    .defaultSku === 0
                ) {
                  this.defaultSkus = this.productData.list[
                    this.productData.list.length - 1
                  ].defaultSku
                }
              }
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              var x = document.querySelector(
                '.el-scrollbar__view .router-link-active .el-icon-close'
              )
              x.click()
            })
            .finally(function() {
              loadingInstance.close()
            })
        } else {
          loadingInstance.close()
        }
      })
    },
    changeName() {
      let pName
      if (this.skuData.id) {
        if (this.skuData.specValue && this.skuData.specUnit) {
          pName =
            this.skuData.packingUnitNumber / this.skuData.specValue +
            this.productData.medicine.packingUnitLimit +
            '*' +
            this.skuData.specValue +
            this.skuData.specUnit +
            '/' +
            this.productData.medicine.packingUnit
        } else {
          pName =
            this.skuData.packingUnitNumber +
            this.productData.medicine.packingUnitLimit +
            '/' +
            this.productData.medicine.packingUnit
        }
      } else {
        if (this.skuData.specValue && this.skuData.specUnit) {
          pName =
            (this.skuData.packingUnitNumber
              ? this.skuData.packingUnitNumber / this.skuData.specValue
              : '') +
            this.productData.medicine.packingUnitLimit +
            '*' +
            this.skuData.specValue +
            this.skuData.specUnit +
            '/' +
            this.productData.medicine.packingUnit
        } else {
          pName =
            (this.skuData.packingUnitNumber
              ? this.skuData.packingUnitNumber
              : '') +
            this.productData.medicine.packingUnitLimit +
            '/' +
            this.productData.medicine.packingUnit
        }
      }
      this.$set(this.skuData, 'packingSpec', pName)
      this.$set(
        this.skuData,
        'name',
        this.productData.medicine.name + ' ' + pName
      )
    },
    formatPrice(e) {
      // 通过正则过滤小数点后两位
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,2})/g)[0] || null
      this.skuData.salePrice = e.target.value
    },
    handleFileChange(event, index) {
      if (!this.skuData.images) {
        this.skuData.images = []
      }

      const param = new FormData() // 创建form对象
      param.append('file', event.target.files[0]) //对应后台接收图片名
      api_product.upload(param).then(
        response => {
          //成功回调
          if (index !== undefined) {
            this.skuData.images.splice(index, 1, response)
          } else {
            this.skuData.images.push(response)
            this.$refs['imgfile_' + (this.skuData.images.length - 1)].value = ''
          }
          console.log(this.skuData.images, 560)
        },
        error => {
          if (index !== undefined) {
            this.$refs['imgfile' + index][0].value = ''
          } else {
            this.$refs['imgfile_' + this.skuData.images.length].value = ''
          }
        }
      )
      // this.$set(this.skuData,'images',images)
    },
    newSku() {
      this.editSkusIndex = null
      this.skuData = {
        productId: this.productData.medicine.productId,
        // specUnit: this.productData.medicine.packingUnitLimit,
        images: []
      }
    },
    delIndex(index) {
      this.dialogIndex = index
      this.$confirm('删除图片？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.delIndexSure()
      })
    },
    delIndexSure() {
      this.skuData.images.splice(this.dialogIndex, 1)
    },
    checkIndex(index) {
      this.dialogIndex = index
      this.$confirm('设置该图片为商品主图？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.checkIndexSure()
      })
    },
    checkIndexSure() {
      this.changeArray(this.dialogIndex, 0)
    },
    changeArray(index, change) {
      this.skuData.images.splice(
        index,
        1,
        ...this.skuData.images.splice(change, 1, this.skuData.images[index])
      )
    },
    leftIndex(index) {
      if (index > 1) {
        this.dialogIndex = index
        this.moveDirection = -1
        this.$confirm('图片位置迁移？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.moveSure()
        })
        // this.changeArray(index, index - 1)
      } else {
        console.log('no index')
      }
    },
    rightIndex(index) {
      if (index < this.skuData.images.length - 1) {
        this.dialogIndex = index
        this.moveDirection = 1
        this.$confirm('图片位置迁移？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.moveSure()
        })
      } else {
        console.log('no index')
      }
    },
    moveSure() {
      if (this.moveDirection === 1) {
        this.changeArray(this.dialogIndex, this.dialogIndex + 1)
      } else if (this.moveDirection === -1) {
        this.changeArray(this.dialogIndex, this.dialogIndex - 1)
      }
    }
  }
}
</script>
