<template>
  <div class="container">
    <el-form
      ref="dataForm"
      :model="fromData"
      :rules="rules"
      :inline="true"
      class="demo-form-inline"
    >
      <div class="title">
        商品信息
      </div>
      <el-table
        class="preion-table"
        :data="[medicineInfo]"
        border
      >
        <el-table-column
          label="药品SKU ID"
          prop="id"
          align="center"
          width="150"
        />
        <el-table-column
          label="商品名称"
          prop="name"
          width="180"
          align="center"
        />
        <el-table-column
          label="通用名"
          prop="commonName"
          align="center"
          width="180px"
        />
        <el-table-column
          label="默认用法用量"
          prop="defaultUsage"
          align="center"
          width="180px"
        >
          <template slot-scope="{row}">
            <el-tag
              v-if="row.defaultUsage == 1"
              size="medium"
            >已配置</el-tag>
            <el-tag
              v-else
              size="medium"
              type="danger"
            >未配置</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="安全用法用量"
          prop="safeUsage"
          align="center"
          width="120px"
        >
          <template slot-scope="{row}">
            <el-tag
              v-if="row.safeUsage == 1"
              size="medium"
            >已配置</el-tag>
            <el-tag
              v-else
              size="medium"
              type="danger"
            >未配置</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="安全分类"
          prop="nmpaTypeDescribe"
          min-width="200px"
          align="center"
        />
        <el-table-column
          label="最后配置时间"
          prop="lastConfigTime"
          align="center"
          width="150px"
        />
      </el-table>
      <el-divider></el-divider>
      <div class="title">
        药品说明书：{{ medicineInfo.usageDosage }}
      </div>
      <div class="title" style="margin-top:20px">
        默认用法用量：<span>{{ medicineInfo.packagSpec }}{{ medicineInfo.packingUnitLimit }}/{{ medicineInfo.packingUnit }}；</span> <span v-if="medicineInfo && medicineInfo.defaultUsage == 1">每{{ fromData.dosageCycle }}{{ fromData.dosageCycleUnit }}{{ fromData.dosageCount }}次 ；每次{{ fromData.eachDosageCount }}{{ fromData.eachDoseUnit }}；{{ fromData.usageMethod }}</span>
      </div>
      <el-form-item
        style="margin-top:20px"
        label="1、默认频次周期数（几日，几周、几月 、几小时）："
        prop="dosageCycle"
      >
        <el-input
          v-model.number="fromData.dosageCycle"
          :readonly="readonly"
          :maxlength="3"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="2、默认频次单位（日、小时、周、月）："
        prop="dosageCycleUnit"
      >
        <DictSelect
          v-model="fromData.dosageCycleUnit"
          :disabled="readonly"
          placeholder="请选择"
          type="default_dosage_cycle_unit"
          style="width: 150px;"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="3、默认频次数："
        prop="dosageCount"
      >
        <el-input
          v-model.number="fromData.dosageCount"
          :readonly="readonly"
          :maxlength="3"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="4、默认剂量："
        prop="eachDosageCount"
      >
        <el-input
          v-model="fromData.eachDosageCount"
          :readonly="readonly"
          placeholder="请输入内容"
          style="width: 120px"
        />
        <DictSelect
          v-model="fromData.eachDoseUnit"
          :disabled="readonly"
          placeholder="请选择"
          type="product_packing_unit_limit"
          style="width: 150px;"
          @change="onDosage"
        />
        <span
          v-if="fromData.eachDoseUnit && fromData.eachDoseUnit!=medicineInfo.packingUnitLimit"
          style="color:red"
        >*单位与药品规格默认单位不一致，将无法自动计算盒数</span>
      </el-form-item>
      <!-- <br>
      <el-form-item
        style="margin-top:10px"
        label="5、药品规格数量："
        prop="packagSpec"
      >
        <el-input
          v-model.number="fromData.packagSpec"
          :maxlength="3"
          :readonly="readonly"
          placeholder="请输入内容"
          style="width: 120px"
        />
        {{ medicineInfo.packingUnitLimit }}/
        <DictSelect
          v-model="fromData.quantityUnit"
          :disabled="readonly"
          placeholder="请选择"
          type="product_packing_unit"
          style="width: 150px;"
        />
      </el-form-item> -->
      <br>
      <el-form-item
        style="margin-top:10px"
        label="5、服用方式："
        prop="dosage"
      >
        <DictSelect
          v-model="fromData.usageMethod"
          :disabled="readonly"
          placeholder="请选择"
          type="usage_method"
          style="width: 150px;"
        />
      </el-form-item>
      <el-divider></el-divider>
      <div class="title">
        安全用法用量：<span v-if="medicineInfo && medicineInfo.safeUsage == 1">频次{{ fromData.frequencyMin }}~{{ fromData.frequencyMax }}次；{{ fromData.doseMin }}~{{ fromData.doseMax }}{{ medicineInfo.packingUnitLimit }}; {{ fromData.medicationCycleMax }}天</span>
      </div>
      <el-form-item
        style="margin-top:20px"
        label="1、频次最大值（每天最大用药次数）："
        prop="frequencyMax"
      >
        <el-input
          v-model.number="fromData.frequencyMax"
          :readonly="readonly"
          :maxlength="3"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="2、频次最小值（每天最小用药次数）："
        prop="frequencyMin"
      >
        <el-input
          v-model.number="fromData.frequencyMin"
          :readonly="readonly"
          :maxlength="3"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="3、剂量最大值（每次最大用量）："
        prop="doseMax"
      >
        <el-input
          v-model="fromData.doseMax"
          :readonly="readonly"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="4、剂量最小值（每次最小用量）："
        prop="doseMin"
      >
        <el-input
          v-model="fromData.doseMin"
          :readonly="readonly"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
      <br>
      <el-form-item
        style="margin-top:10px"
        label="5、用药周期最大值（天）："
        prop="medicationCycleMax"
      >
        <el-input
          v-model.number="fromData.medicationCycleMax"
          :readonly="readonly"
          :maxlength="3"
          placeholder="请输入内容"
          style="width: 120px"
        />
      </el-form-item>
    </el-form>
    <div class="el-dialog__footer">
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-if="readonly"
          v-permission="['wms:medicine:usage:edit']"
          type="primary"
          @click="readonly = false"
        >编辑</el-button>
        <el-button
          v-if="!readonly"
          type="primary"
          @click="save('dataForm')"
        >保存</el-button>
        <el-button
          type="primary"
          @click="cancel"
        >取消</el-button>
      </div>
    </div>
  </div>

</template>

<script>
import { getDetail, setUsage } from '@/api/product/preion'
import DictSelect from '@/components/DictSelect'
export default {
  components: {
    DictSelect
  },
  data() {
    var checkVal = (rule, value, callback) => {
      setTimeout(() => {
        if (value === 0) {
          callback(new Error('请输入大于1的正整数'))
        }
        if (value) {
          if (value < 1) {
            callback(new Error('请输入大于1的正整数'))
          }
          if (!Number.isInteger(value)) {
            callback(new Error('请输入数字值'))
          }
          if (
            rule.field === 'frequencyMin' &&
          value / 1 >= this.fromData.frequencyMax / 1
          ) {
            callback(new Error('频次最小值不能大于等于频次最大值'))
          }
          if (
            rule.field === 'frequencyMax' &&
          value / 1 <= this.fromData.frequencyMin / 1
          ) {
            callback(new Error('频次最大值不能小于等于频次最小值'))
          }
        }

        callback()
      }, 100)
    }
    var stripscript = (rule, value, callback) => {
      setTimeout(() => {
        var pattern = /^(?!0+(?:\.0+)?$)(?:[1-9]\d*|0)(?:\.\d{1,2})?$/
        if (value === 0) {
          callback(new Error('请输入大于0的整数或小数(保留两位小数)'))
        }
        if (value) {
          if (value < 1 && !pattern.test(value)) {
            callback(new Error('请输入大于0的整数或小数(保留两位小数)'))
          }
          if (!Number.isInteger(parseInt(value))) {
            callback(new Error('请输入数字值'))
          }
          if (Math.ceil(value) > 999) {
            callback(new Error('请输入有效数值'))
          }
          if (!pattern.test(value)) {
            callback(new Error('只保留两位小数'))
          }
          if (
            rule.field === 'doseMin' &&
          value / 1 >= this.fromData.doseMax / 1
          ) {
            callback(new Error('剂量最小值不能大于等于剂最大值'))
          }
          if (
            rule.field === 'doseMax' &&
          value / 1 <= this.fromData.doseMin / 1
          ) {
            callback(new Error('剂量最大值不能小于等于剂最小值'))
          }
        }
        callback()
      }, 100)
    }
    return {
      drugId: '',
      fromData: {},
      readonly: true,
      isSetInfo: false,
      medicineInfo: {},
      medicationDefaultUsage: {},
      rules: {
        dosageCycle: [{ validator: checkVal, trigger: 'blur' }],
        dosageCount: [{ validator: checkVal, trigger: 'blur' }],
        eachDosageCount: [{ validator: stripscript, trigger: 'blur' }],
        packagSpec: [{ validator: checkVal, trigger: 'blur' }],
        frequencyMax: [{ validator: checkVal, trigger: 'blur' }],
        frequencyMin: [{ validator: checkVal, trigger: 'blur' }],
        doseMax: [{ validator: stripscript, trigger: 'blur' }],
        doseMin: [{ validator: stripscript, trigger: 'blur' }],
        medicationCycleMax: [{ validator: checkVal, trigger: 'blur' }]
      }
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getDetail(this.$route.params.id)
  },
  mounted() {},
  methods: {
    // 药品详情
    getDetail(id) {
      getDetail({ id: id }).then((response) => {
        this.medicineInfo = response.medicineInfo
        this.fromData = response.medicationDefaultUsage || {}
        this.fromData.skuId = this.medicineInfo.id
        this.readonly = !response.medicationDefaultUsage ? false : true
      })
    },
    // 保存信息
    save(dataForm) {
      this.isSetInfo = true
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          setUsage(this.fromData).then((response) => {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.getDetail(this.fromData.skuId)
          })
        }
      })
    },
    // 取消返回
    cancel() {
      this.$router.go(-1)
    },
    onDosage(e) {
      if (e !== this.medicineInfo.packingUnitLimit) {
        this.$message({
          message: '单位与药品规格默认单位不一致，将无法自动计算盒数',
          type: 'warning'
        })
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    if (!this.isSetInfo && !this.readonly) {
      this.$confirm('填写内容尚未保存, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          next()
        })
        .catch(() => {
          console.log('取消')
        })
    } else {
      next()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 30px;
  .title {
    font-size: 20px;
  }
  .preion-table {
    margin-top: 20px;
  }
  .dialog-footer {
    padding-right: 100px;
  }
}
</style>
