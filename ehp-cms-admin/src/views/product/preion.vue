<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        type="number"
        clearable
        class="filter-item"
        placeholder="药品id"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        class="filter-item"
        placeholder="商品名称/通用名"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.defaultUsage"
        clearable
        style="width: 150px"
        placeholder="默认用法用量"
        @change="handleFilter"
      >
        <el-option
          v-for="item in defaultOpt"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="listQuery.safeUsage"
        clearable
        style="width: 150px"
        placeholder="安全用法用量"
        @change="handleFilter"
      >
        <el-option
          v-for="item in defaultOpt"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <DictSelect
        v-model="listQuery.nmpaType"
        placeholder="安全类型"
        type="product_nmpa_type"
        style="width: 150px;"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        gte="startTime"
        lte="endTime"
        start-placeholder="配置开始时间"
        end-placeholder="配置结束时间"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >查询</el-button>
      <el-button
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-upload
        style="display: inline-block;"
        :action="uploadPath"
        :headers="headers"
        :on-success="handleExcelSuccess"
        :on-error="handleExcelError"
        :on-remove="handleRemoveExcel"
        :file-list="fileList"
        :limit="1"
        :show-file-list="false"
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      >
        <el-button
          v-permission="['wms:medicine:usage:import']"
          type="primary"
        >批量上传</el-button>
        <div
          slot="tip"
          class="el-upload__tip"
        ></div>
      </el-upload>
      <el-link
        v-permission="['user:doctor:expert:expert']"
        :href="tempPath"
        type="info"
        target="_blank"
      >
        <el-button type="primary">批量上传模板下载</el-button>
      </el-link>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="药品SKU ID"
        fixed
        prop="id"
        width="120px"
        align="center"
      />
      <el-table-column
        label="商品名称"
        prop="name"
        width="260px"
        align="center"
      />
      <el-table-column
        label="通用名"
        prop="commonName"
        width="150px"
        align="center"
      />
      <el-table-column
        label="默认用法用量"
        prop="defaultUsage"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag v-if="row.defaultUsage == 1" size="medium">已配置</el-tag>
          <el-tag v-else size="medium" type="danger">未配置</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="安全用法用量"
        prop="safeUsage"
        width="130px"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag v-if="row.safeUsage == 1" size="medium">已配置</el-tag>
          <el-tag v-else size="medium" type="danger">未配置</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="安全分类"
        prop="nmpaTypeDescribe"
        width="130px"
        align="center"
      />
      <el-table-column
        label="最后配置时间"
        prop="lastConfigTime"
        width="160px"
        align="center"
      />

      <el-table-column
        label="操作"
        prop="createdAt"
        width="120px"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="getpreionDetail(scope.row.id)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  getList,
  excelURL,
  tempUrl
} from '@/api/product/preion'
import { getToken, getTokenName } from '@/utils/auth'
import DatePicker from '@/components/DatePicker'
import DictSelect from '@/components/DictSelect'
export default {
  name: 'SearchDoctordoctor',
  filters: {},
  components: {
    DatePicker,
    DictSelect

  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc',
        id: '',
        name: '',
        defaultUsage: '',
        safeUsage: '',
        nmpaType: ''
      },
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: [],
      // 默认用法用量
      defaultOpt: [
        {
          value: 0,
          label: '未配置'
        },
        {
          value: 1,
          label: '已配置'
        }
      ],
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      }
    }
  },
  created() {
    this.uploadPath = excelURL()
    this.headers[getTokenName()] = getToken()
    this.tempUrl()
    this.getList()
  },
  activated() {},
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    tempUrl() {
      tempUrl().then((response) => {
        this.tempPath = response
      })
    },
    handleFilter() {
      console.log(this.listQuery, 258)
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.id = ''
      this.listQuery.name = ''
      this.listQuery.defaultUsage = ''
      this.listQuery.safeUsage = ''
      this.listQuery.nmpaType = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    //停用启用弹出层
    getpreionDetail(id) {
      this.$router.push({
        path: './setPreion/' + id
      })
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.getList()
        this.$alert(`已上传${response.data.num}项内容，点击查看上传明细`, '操作成功', {
          confirmButtonText: '确定'
        }).then(res => {
          console.log('确认')
          window.open(response.data.url, '_blank')
        })
      }
    },
    handleRemoveExcel(file, fileList) {}
  }
}
</script>
