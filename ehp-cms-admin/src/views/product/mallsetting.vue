<template>
  <!-- 商城设置 -->
  <div class="mall-setting">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <!-- v-permission="['wms:category:list']" -->
      <el-tab-pane v-if="$store.getters && $store.getters.permissions.includes('wms:category:list')" label="展示分类" name="1"></el-tab-pane>
      <!-- v-permission="['wms:section:list']"  -->
      <el-tab-pane v-if="$store.getters && $store.getters.permissions.includes('wms:section:list')" label="专题营销" name="2"></el-tab-pane>
    </el-tabs>
    <div class="mall-setting-content">
      <div v-if="activeName === '1'" v-permission="['wms:category:list']">
        <Classify></Classify>
      </div>
      <div v-if="activeName === '2'" v-permission="['wms:section:list']">
        <Mallgroup></Mallgroup>
      </div>
    </div>
  </div>
</template>
<script>
import Classify from '@/views/product/components/classify.vue'
import Mallgroup from '@/views/product/components/mallgroup.vue'
export default {
  components: { Classify, Mallgroup },
  data() {
    return {
      activeName: '1'
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab.name)
    }
  }
}
</script>
<style scoped>
.mall-setting{
  margin: 20px;
}
.mall-setting .el-tabs__active-bar{
  width: 60px!important;
}
</style>
