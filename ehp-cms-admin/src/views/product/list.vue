<template>
  <div class="app-container">

    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        placeholder="商品名称"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      />
      <!-- <el-input
        v-model="listQuery.disease"
        placeholder="诊断"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
      /> -->
      <DictSelect
        v-model="listQuery.type"
        placeholder="请选择品类"
        class="filter-item"
        type="product_type"
        style="width: 110px;"
        @change="handleFilter"
      />
      <el-cascader
        v-model="listQuery.classificationId"
        placeholder="请选择药理分类"
        :options="baseoptions"
        :props="props"
        clearable
        class="filter-item"
        style="width: 150px;"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.relationSku"
        placeholder="是否关联SKU"
        class="filter-item"
        style="width: 120px;"
        type="product_relation_sku"
        @change="handleFilter"
      />
      <DictSelect
        v-model="listQuery.dataIntegrity"
        placeholder="资料是否完整"
        class="filter-item"
        style="width: 120px;"
        type="product_data_integrity"
        @change="handleFilter"
      />
      <DatePicker
        ref="datePickerRef"
        :query-model="listQuery"
        class="filter-item"
        style="width: 220px"
        start-placeholder="建码开始时间"
        end-placeholder="建码结束时间"
        @change="handleFilter"
      />
      <!-- todo -->
      <DictSelect
        v-model="listQuery.nmpaType"
        placeholder="安全分类"
        class="filter-item"
        style="width: 120px;"
        type="product_nmpa_type"
        @change="handleFilter"
      />
      <!-- todo -->
      <!-- <DictSelect
        v-model="listQuery.featured"
        placeholder="是否首页推荐"
        class="filter-item"
        style="width: 120px;"
        type="product_data_integrity"
        @change="handleFilter"
      /> -->
      <el-cascader
        v-model="listQuery.categoryId"
        placeholder="展示分类"
        :options="materialTypeData"
        :props="props"
        clearable
        class="filter-item"
        style="width: 120px;"
        @change="handleFilter"
      />
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button
        v-permission="['wms:medicine:update']"
        type="primary"
        icon="el-icon-plus"
        @click="handleCreate"
      >新建商品</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="50px" />
      <el-table-column label="商品编码" prop="number" width="120px" align="center" />
      <el-table-column label="品类" prop="typeDescribe" align="center" width="60px" />
      <!-- todo -->
      <el-table-column label="展示分类" prop="categoryName" align="center" width="160px" />
      <el-table-column label="商品名称" prop="name" align="center" width="180px" />
      <el-table-column label="通用名" prop="commonName" width="150px" align="center" />
      <el-table-column label="安全分类" prop="nmpaTypeDescribe" align="center" width="70px" />
      <el-table-column label="药理分类" prop="classificationName" min-width="200px" align="center" />
      <el-table-column label="批准文号/注册码" prop="approvalNumber" align="center" width="150px" />
      <el-table-column label="建码时间" prop="createdAt" width="135px" align="center" />
      <el-table-column label="生产企业" prop="productionEnterprise" min-width="150px" align="center"></el-table-column>
      <el-table-column label="关联sku" prop="relationSku" width="80px" align="center" fixed="right">
        <template slot-scope="{row}">
          <el-checkbox v-model="row.relationSku" :true-label="1" :false-label="0" disabled />
        </template>
      </el-table-column>
      <el-table-column label="资料完整" prop="dataIntegrity" width="80px" align="center" fixed="right">
        <template slot-scope="{row}">
          <el-checkbox v-model="row.dataIntegrity" :true-label="1" :false-label="0" disabled />
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="280px">
        <template slot-scope="{row}">
          <el-button
            v-permission="['wms:medicine:update']"
            type="primary"
            @click="editProduct(row.id)"
          >编辑</el-button>
          <el-button
            v-permission="['wms:medicine:sku:list']"
            type="primary"
            @click="editSku(row.id)"
          >管理SKU</el-button>
          <el-button
            v-if="row.featured === 1"
            type="danger"
            @click="cancelRecommend(row.id)"
          >取消推荐</el-button>
          <el-button
            v-else
            type="primary"
            @click="recommend(row.id)"
          >设为推荐</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog title="选择品类" :visible.sync="dialogFormVisible">
      <el-form ref="dataForm" :model="createInfo" :rules="rules">
        <el-form-item label="品类" :label-width="formLabelWidth" prop="type">
          <DictSelect v-model="createInfo.type" placeholder="请选择" type="product_type" />
        </el-form-item>
        <el-form-item label="品牌" :label-width="formLabelWidth">
          <el-input
            v-model="createInfo.brandName"
            class="brandinput"
            placeholder="品牌名简称"
            maxlength="6"
          />
          <div class="tips">非必填，最多6个字符</div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button type="primary" @click="nextCreate('dataForm')">下一步</el-button>
      </div>
    </el-dialog>
    <DrugForm ref="drugForm" :create-info="createInfo" @refresh="getList"></DrugForm>
  </div>
</template>
<style scoped>
/*.el-form-item .el-input__inner{width:auto;}*/
.el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
  background-color: #1890ff;
  border-color: #1890ff;
}
.brandinput {
  width: 193px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
</style>
<script>
import api_product from '@/api/product/product'
import api_base from '@/api/product/base'
import DictSelect from '@/components/DictSelect'
import DatePicker from '@/components/DatePicker'
import DrugForm from './info.vue'
export default {
  name: 'Productlist',
  filters: {},
  components: {
    DictSelect,
    DatePicker,
    DrugForm
  },
  data() {
    return {
      tableKey: 0,
      baseoptions: null,
      list: null,
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        name: '',
        type: '',
        classificationId: '',
        relationSku: '',
        dataIntegrity: '',
        nmpaType: '' // 安全分类
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      createInfo: {},
      rules: {
        type: [{ required: true, message: '请选择品类', trigger: 'change' }]
      },
      status: '',
      productId: '',
      materialTypeData: null
    }
  },
  created() {
    // this.handleFilter()
    this.getBaseData()
    this.getCategoryList()
  },
  activated() {
    this.getList()
    console.log('============activated=============')
  },
  methods: {
    recommend(id) {
      api_product.changeFeatured({
        featured: 1, // 	推荐状态 0：未推荐，1：推荐
        productId: id
      }).then(rs => {
        this.$message.success('设为推荐成功！')
        this.getList()
      })
    },
    cancelRecommend(id) {
      api_product.changeFeatured({
        featured: 0, // 	推荐状态 0：未推荐，1：推荐
        productId: id
      }).then(rs => {
        this.$message.success('取消推荐成功！')
        this.getList()
      })
    },
    // 获取数据
    getList() {
      api_product.list(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getBaseData() {
      api_base.pharmacology().then(response => {
        this.baseoptions = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.listQuery.type = ''
      this.listQuery.classificationId = ''
      this.listQuery.relationSku = ''
      this.listQuery.dataIntegrity = ''
      this.listQuery.nmpaType = ''
      this.listQuery.featured = ''
      this.listQuery.categoryId = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleCreate() {
      this.dialogFormVisible = true
      this.resetTemp()
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          this.dialogFormVisible = false
          console.log(this.createInfo, 232)
          this.$refs['drugForm'].handleCreate(this.createInfo)
        }
      })
    },
    editProduct(productId) {
      this.$refs['drugForm'].handleUpdate(productId)
      // this.$router.push({
      //   path: './info/' + productId
      // })
    },
    editSku(productId) {
      this.productId = productId
      this.$router.push({
        path: './sku/' + productId
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.createInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    },
    getCategoryList() {
      api_product.categorylist().then(res => {
        this.materialTypeData = res
      })
    }
  }
}
</script>
