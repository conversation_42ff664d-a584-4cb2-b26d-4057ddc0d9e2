<template>
  <div class="app-container goods-info">
    <!-- <div class="title">新增药品</div> -->
    <el-dialog title="" :visible.sync="dialogVisible" width="90%" :before-close="dialogBeforeClose">
      <el-alert
        title="重要提示：互联网医院管理办法规定，不得在互联网上开具麻醉药品、精神类药品处方以及其他用药风险较高、有其他特殊管理规定的药品处方。系统禁止添加麻醉药品、精神类药品。"
        type="warning"
        :closable="false"
        center
        style="color: #6D000E; background-color: #ffeb3b; margin-bottom: 20px;"
      >
      </el-alert>
      <el-form
        ref="dataForm"
        :model="productData"
        :rules="rules"
        :inline="true"
        label-width="150px"
        class="demo-form-inline"
      >
        <el-input v-model="productData.productId" type="hidden" />
        <el-row :gutter="20">
          <el-col :span="24">
            <!-- <el-form-item label="首页推荐">
              <el-radio-group v-model="productData.featured">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item> -->
          </el-col>
          <!-- <el-col :span="8"> -->
          <!-- <el-form-item label="卫健委药品编码" prop="wjwNumber">
              <el-input v-model="productData.wjwNumber" clearable />
            </el-form-item> -->
          <!-- </el-col> -->
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商品名称" prop="name">
              <el-input v-model="productData.name" disabled clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="品牌名" prop="brandName">
              <el-input v-model="productData.brandName" clearable @keyup.native.capture.prevent="productName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品名" prop="drugName">
              <el-input v-model="productData.drugName" clearable @keyup.native.capture.prevent="productName" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="药品通用名" prop="commonName">
              <el-input v-model="productData.commonName" clearable @keyup.native.capture.prevent="productName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文名称" prop="englishName">
              <el-input v-model="productData.englishName" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="汉语拼音" prop="pinyin">
              <el-input v-model="productData.pinyin" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="20">
          <!-- todo -->
          <el-col :span="8">
            <el-form-item label="展示分类" prop="categoryId">
              <el-cascader
                v-model="productData.categoryId"
                :options="materialTypeData"
                :props="props"
                clearable
                :show-all-levels="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="药品分类" prop="drugClassificationId">
              <el-cascader
                v-model="productData.drugClassificationId"
                :options="drugClassData"
                :props="props"
                clearable
                :show-all-levels="false"
                @change="handleChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="药理分类" prop="pharmacologyClassificationId">
              <el-cascader
                v-model="productData.pharmacologyClassificationId"
                :options="pharmacologyData"
                :props="props"
                clearable
                :show-all-levels="false"
                @change="handleChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="安全类别" prop="nmpaType">
              <DictSelect v-model="productData.nmpaType" placeholder="请选择" type="product_nmpa_type" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="剂型" prop="agentClassificationId">
              <el-cascader v-model="productData.agentClassificationId" :options="agentsData" :props="props" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原料分类" prop="materialType">
              <DictSelect v-model="productData.materialType" placeholder="请选择" type="product_material_type" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="规格" prop="specification">
              <el-input v-model="productData.specification" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="包装" prop="packingUnit">
              <DictSelect
                v-model="productData.packingUnit"
                placeholder="请选择"
                type="product_packing_unit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="最小包装单位" prop="packingUnitLimit">
              <DictSelect
                v-model="productData.packingUnitLimit"
                placeholder="请选择"
                type="product_packing_unit_limit"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="批准文号/注册证号" prop="approvalNumber">
              <el-input v-model="productData.approvalNumber" clearable placeholder="批准文号、进口注册证号、医药产品注册证号三选一" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="药品本位码" prop="standardCode">
              <el-input v-model="productData.standardCode" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="贮藏" prop="storage">
              <el-input v-model="productData.storage" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="适应症" prop="indications">
            <el-col :span="24">
              <el-input v-model="productData.indications" type="textarea" autosize width="100%" placeholder="参考商品说明书填写" />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="用法用量" prop="usageDosage">
            <el-col :span="24">
              <el-input v-model="productData.usageDosage" type="textarea" autosize placeholder="参考商品说明书填写" />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="成份" prop="ingredients">
            <el-col :span="24">
              <el-input v-model="productData.ingredients" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="性状" prop="phenotypicTrait">
            <el-col :span="24">
              <el-input
                v-model="productData.phenotypicTrait"
                type="textarea"
                autosize
                width="50%"
                placeholder="参考商品说明书填写"
              />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="不良反应" prop="adverseEffects">
            <el-col :span="24">
              <el-input
                v-model="productData.adverseEffects"
                type="textarea"
                autosize
                width="50%"
                placeholder="参考商品说明书填写"
              />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="禁忌" prop="contraindications">
            <el-col :span="24">
              <el-input
                v-model="productData.contraindications"
                type="textarea"
                autosize
                width="50%"
                placeholder="参考商品说明书填写"
              />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-form-item label="注意事项" prop="mattersNeedingAttention">
            <el-col :span="24">
              <el-input
                v-model="productData.mattersNeedingAttention"
                type="textarea"
                autosize
                width="50%"
                placeholder="参考商品说明书填写"
              />
            </el-col>
          </el-form-item>
        </el-row>

        <el-row :gutter="20" class="labelitem">
          <el-form-item label="化学名称" prop="chemicalName">
            <el-col :span="24">
              <el-input v-model="productData.chemicalName" type="textarea" autosize width="50%" placeholder="参考商品说明书填写" />
            </el-col>
          </el-form-item>
        </el-row>
        <el-row :gutter="20" class="">
          <el-col :span="8">
            <el-form-item label="生产企业" prop="productionEnterprise">
              <el-input
                v-model="productData.productionEnterprise"
                type="textarea"
                autosize
                width="60%"
                placeholder="参考商品说明书填写"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行标准:" prop="executionStandard">
              <el-input v-model="productData.executionStandard" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="有效期" prop="periodValidity">
              <el-input v-model="productData.periodValidity" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
        </el-row>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveProduct('dataForm')">保 存</el-button>
      </div>
    </el-dialog>

  </div>
</template>
<style scoped>
  .title {
    padding-bottom: 10px;
  }

  .tips {
    display: inline-block;
    margin-left: 10px;
  }

  .el-dialog__footer {
    padding-right: 20%;
  }

  .labelitem .el-form-item {
    display: flex;
  }

  .labelitem /deep/.el-form-item__content {
    width: 84.2%;
    /* flex: 1; */
  }
  .goods-info .el-divider--horizontal{
    margin-top: 0;
    margin-bottom: 20px;
  }

</style>
<script>
import api_product from '@/api/product/product'
import api_base from '@/api/product/base'
import DictSelect from '@/components/DictSelect'
import {
  Loading
} from 'element-ui'
export default {
  name: '',
  filters: {},
  components: {
    DictSelect
  },
  props: {
    createInfo: {
      type: Object,
      default: null
    }
  },
  data() {
    // const validateName = async(rule, value, callback) => {
    //   // 仅判断有值的情况
    //   if (!value) {
    //     return callback()
    //   }
    //   api_base.validateName({ value }).then((error) => {
    //     if (error) {
    //       callback(new Error('禁止添加麻醉药品、精神类药品'))
    //     } else {
    //       callback()
    //     }
    //   }).catch((err) => {
    //     callback(new Error('校验失败，请重试'))
    //   })
    // }
    return {
      productData: {},
      drugClassData: null,
      pharmacologyData: null, //药理分类数据
      materialTypeData: null, //展示分类数据
      agentsData: null, //药剂分类
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      rules: {
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        commonName: [
          { required: true, message: '请输入商品通用名', trigger: 'blur' }
          // { validator: validateName, trigger: 'blur' }
        ],
        // brandName: [
        //   { validator: validateName, trigger: 'blur' }
        // ],
        // drugName: [
        //   { validator: validateName, trigger: 'blur' }
        // ],
        // englishName: [
        //   { validator: validateName, trigger: 'blur' }
        // ],
        // chemicalName: [
        //   { validator: validateName, trigger: 'blur' }
        // ],
        // ingredients: [
        //   { validator: validateName, trigger: 'blur' }
        // ],
        nmpaType: [
          { required: true, message: '请选择安全类别', trigger: 'blur' }
        ],
        standardCode: [
          { required: true, message: '请输入药品本位码', trigger: 'blur' }
        ],
        approvalNumber: [
          {
            required: true,
            message: '请输入批准文号/注册证号',
            trigger: 'blur'
          }
        ],
        categoryId: [
          { required: true, message: '请选择展示分类', trigger: 'blur' }
        ],
        agentClassificationId: [
          { required: true, message: '请选择型剂', trigger: 'blur' }
        ],
        packingUnit: [
          { required: true, message: '请选择包装', trigger: 'blur' }
        ],
        packingUnitLimit: [
          { required: true, message: '请选择最小包装单位', trigger: 'blur' }
        ],
        specification: [
          { required: true, message: '请输入规格', trigger: 'blur' }
        ],
        storage: [{ required: true, message: '请输入贮藏', trigger: 'blur' }],
        indications: [
          { required: true, message: '请输入适应症', trigger: 'blur' }
        ],
        usageDosage: [
          { required: true, message: '请输入用法用量', trigger: 'blur' }
        ],
        productionEnterprise: [
          { required: true, message: '请输入生产企业', trigger: 'blur' }
        ],
        periodValidity: [
          { required: true, message: '请输入有效期', trigger: 'blur' }
        ],
        executionStandard: [
          { required: true, message: '请输入执行标准', trigger: 'blur' }
        ],
        drugClassificationId: [
          { required: true, message: '请选择药品分类', trigger: 'blur' }
        ]
      },
      dialogVisible: false
    }
  },
  watch: {
    createInfo: function(val) {
      this.productData = val
      // localStorage.setItem('PRODUCTDATA', JSON.stringify(this.productData))
    },
    dialogVisible(val) {
      console.log(val, 'dialogVisible')
      if (!val && !this.productId) {
        localStorage.setItem('PRODUCTDATA', JSON.stringify(this.productData))
      }
      if (val && localStorage.getItem('PRODUCTDATA') && !this.productId) {
        this.productData = JSON.parse(localStorage.getItem('PRODUCTDATA'))
      }
    }
  },
  created() {
    // if (this.productId) {
    //   this.getProductInfo(this.productId)
    // } else {
    //   this.productName()
    // }
    this.getPharmacologyData()
    this.getCategoryList()
    this.getAgentsData()
    this.getDrugData()
  },
  methods: {
    productName() {
      var second = '',
        three = ''
      if (this.productData.drugName && this.productData.brandName) {
        second = ' ' + this.productData.drugName
      }
      if (this.productData.commonName) {
        three = ' ' + this.productData.commonName
      }
      if (this.productData.brandName) {
        this.productData.name = this.productData.brandName + second + three
      } else {
        this.productData.name = second + three
      }
      this.productData = Object.assign({}, this.productData)
    },
    getDrugData() {
      api_base.drug().then(response => {
        this.drugClassData = response
      })
    },
    getProductInfo(productId) {
      api_product.info(productId).then(response => {
        this.productData = response
      })
    },
    getPharmacologyData() {
      api_base.pharmacology().then(response => {
        this.pharmacologyData = response
      })
    },
    getAgentsData() {
      api_base.agents().then(response => {
        this.agentsData = response
      })
    },
    getCategoryList() {
      api_product.categorylist().then(response => {
        this.materialTypeData = response
      })
    },
    saveProduct(dataForm) {
      const loadingInstance = Loading.service({
        fullscreen: true
      })
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          api_product
            .update(this.productData)
            .then(response => {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              this.dialogVisible = false
              this.$emit('refresh')
              // 保存成功之后清理
              localStorage.removeItem('PRODUCTDATA')
              // this.$router.push({
              //   path: '/product/sku/' + response
              // })
            })
            .finally(function() {
              loadingInstance.close()
            })
        } else {
          loadingInstance.close()
        }
      })
    },
    handleChange(value) {},
    dialogBeforeClose() {
      this.dialogVisible = false
    },
    handleCreate(createInfo) {
      console.log(createInfo, '传过来的品牌，如果需要处理在处理不需要就不处理了')
      this.dialogVisible = true
      this.productName()
    },
    handleUpdate(productId) {
      this.productId = productId
      this.dialogVisible = true
      this.resetTemp()
      this.getProductInfo(productId)
    },
    resetTemp() {
      this.$nextTick(() => {
        this.productData = {}
        this.$refs['dataForm'].clearValidate()
      })
    }
  }

}

</script>
