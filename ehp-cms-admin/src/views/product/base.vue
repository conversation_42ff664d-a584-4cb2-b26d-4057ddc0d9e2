<template>
  <div class="app-container">
    <el-tabs @tab-click="handleClick">
      <el-tab-pane label="药理分类">
        <el-table row-key="id" :data="listpharmacology" fit highlight-current-row>
          <el-table-column label="类型" prop="name" align="left" />
          <el-table-column label="创建时间" prop="createdAt" width="155px" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="剂型分类">
        <el-table row-key="id" :data="listagents" fit highlight-current-row>
          <el-table-column label="类型" prop="name" align="left" />
          <el-table-column label="创建时间" prop="createdAt" width="155px" align="center" />
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="药品分类">
        <el-table row-key="id" :data="listdrug" fit highlight-current-row>
          <el-table-column label="类型" prop="name" align="left" />
          <el-table-column label="编码" prop="code" width="80px" />
          <el-table-column label="创建时间" prop="createdAt" width="155px" align="center" />
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<style>
</style>

<script>
import API from '@/api/product/base'
export default {
  name: '',
  data() {
    return {
      listagents: [],
      listpharmacology: [],
      listdrug: []
    }
  },
  created() {
    this.getPharmacology()
  },
  methods: {
    handleClick(tab, event) {
      if (tab.index === '0') {
        this.getPharmacology()
      } else if (tab.index === '1') {
        this.getBase()
      } else if (tab.index === '2') {
        this.getDrug()
      }
    },
    getBase() {
      API.agents().then(response => {
        this.listagents = response
      })
    },
    getPharmacology() {
      API.pharmacology().then(response => {
        this.listpharmacology = response
      })
    },
    getDrug() {
      API.drug().then(response => {
        this.listdrug = response
      })
    }
  }
}
</script>
