<template>
  <div class="app-container">
    <div class="time-warpper">
      <div class="time_t">统计时间：</div>
      <el-date-picker
        v-model="date"
        size="small"
        type="month"
        value-format="yyyy-MM"
        :picker-options="pickerOptions"
        placeholder="请选择"
        @change="dateChange"
      >
      </el-date-picker>
    </div>

    <!-- 医生等级分布 -->
    <div class="level-warpper">
      <div class="title">医生等级分布</div>
      <div class="level-content">
        <div class="left">
          <div
            v-for="(item, index) in levelList"
            :key="index"
            class="item"
            :class="!item.bgColor ? 'item1' : ''"
          >
            <div v-if="item.bgColor" class="item-icon" :style="{ background: item.bgColor }"></div>
            <div class="item-detail">
              <div class="t1">
                <span>{{ item.title }}</span>
                <el-tooltip class="item" effect="dark" :content="item.info" placement="top-start">
                  <i class="el-icon-info icon_info"></i>
                </el-tooltip>
              </div>
              <div class="num" :style="{ color: !item.bgColor ? '#315fff' : '#333333' }">
                {{ item.count }}
              </div>
              <div class="compare">
                <span class="s1">较上月</span>
                <div v-if="item.compare >= 0" class="compare-num">
                  <span class="s2">+{{ item.compare }}</span>
                  <img style="width: 8px; margin-left: 4px;" src="@/assets/images/ic_data_rise.png" />
                </div>
                <div v-else class="compare-num">
                  <span class="s3">{{ item.compare }}</span>
                  <img style="width: 8px; margin-left: 4px;" src="@/assets/images/ic_data_decline.png" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="right">
          <div id="levelEchart" ref="levelEchart"></div>
        </div>
      </div>
    </div>

    <!-- 医生销售排行/患者数分析 -->
    <div class="level-warpper-doc">
      <!-- 医生销售额排行 -->
      <div class="wrap" style="margin-right: 16px;">
        <span class="title">医生销售额排行</span>
        <div id="doctorSaleChart"></div>
      </div>
      <!-- 患者分析 -->
      <div class="wrap">
        <span class="title">患者数分析</span>
        <div id="patientChart"></div>
      </div>
    </div>

    <!-- 医生数据明细 -->
    <div class="level-warpper">
      <div class="filter-content">
        <div class="title">医生数据明细</div>
        <div class="filter">
          <el-input
            v-model="listQuery.departmentName"
            clearable
            class="filter-item"
            placeholder="全部科室"
            style="width: 150px; margin-right: 10px;"
            @clear="searchEnterFun"
            @keyup.enter.native="searchEnterFun"
          />
          <el-select
            v-model="listQuery.levels"
            placeholder="医生等级"
            multiple
            clearable
            collapse-tags
            @change="doctorLevelChange"
          >
            <el-option
              v-for="item in doctorLevelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </div>

      <el-table
        class="customer-no-border-table"
        fit
        :data="tableData"
        :row-style="{ height: '42px' }"
        :header-row-style="{ height: '42px' }"
        :header-cell-style="{
          background: '#F8F9FB'
        }"
        style="width: 100%; margin-top: 23px;"
      >
        <el-table-column label="医生ID" prop="doctorId" align="center" />
        <el-table-column label="医生姓名" prop="doctorName" align="center" />
        <el-table-column label="科室" prop="departmentName" align="center" />
        <el-table-column label="手机号" prop="doctorPhone" align="center" width="130" />
        <el-table-column label="医生等级" prop="level" align="center" />
        <el-table-column label="较上月" align="center">
          <template slot-scope="scope">
            <img
              style="width: 8px; vertical-align: middle;"
              :src="scope.row.levelCompare >= 0 ? increaseImgSrc : reduceImgSrc"
            />
            <span>{{ Math.abs(scope.row.levelCompare) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本月购药患者" prop="purchasingPatientCount" align="center" />
        <el-table-column label="较上月" align="center">
          <template slot-scope="scope">
            <img
              style="width: 8px; vertical-align: middle;"
              :src="scope.row.purchasingPatientCountCompare >= 0 ? increaseImgSrc : reduceImgSrc"
            />
            <span>{{ Math.abs(scope.row.purchasingPatientCountCompare) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本月互动患者" prop="consultPatientCount" align="center" />
        <el-table-column label="本月新增关注" prop="followPatientCount" align="center" />
        <el-table-column label="患者总数" prop="patientCount" align="center" />
        <el-table-column label="销售额(元)" prop="sales" align="center" />
        <el-table-column label="较上月" prop="salesCompare" align="center">
          <template slot-scope="scope">
            <img
              style="width: 8px; vertical-align: middle;"
              :src="scope.row.salesCompare >= 0 ? increaseImgSrc : reduceImgSrc"
            />
            <span>{{ Math.abs(scope.row.salesCompare) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :auto-scroll="false"
        :page.sync="listQuery.pageNo"
        :limit.sync="listQuery.pageSize"
        @pagination="fetchDoctorDetail"
      />
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { getDoctorLevel, getDoctorSalesRank, getPatient, getDoctorDetail } from '@/api/dataStatistics'

// 医生等级
const doctorLevelList = [
  {
    value: 1,
    label: '1级'
  },
  {
    value: 2,
    label: '2级'
  },
  {
    value: 3,
    label: '3级'
  },
  {
    value: 4,
    label: '4级'
  },
  {
    value: 5,
    label: '5级'
  },
  {
    value: 6,
    label: '6级'
  },
  {
    value: 7,
    label: '7级'
  }
]
const levelMap = {
  1: '1级医生',
  2: '2级医生',
  3: '3级医生',
  4: '4级医生',
  5: '5级医生',
  6: '6级医生',
  7: '7级医生'
}
const colorMap = {
  1: '#315FFF',
  2: '#209BFF',
  3: '#00B876',
  4: '#FDE622',
  5: '#F97A0F',
  6: '#544DF1',
  7: '#E9290E'
}
const tooltipMap = {
  1: '购药患者数为1～4个，为1级医生',
  2: '购药患者数为5～9个，为2级医生',
  3: '购药患者数为10～19个，为3级医生',
  4: '购药患者数为20～29个，为4级医生',
  5: '购药患者数为30～49个，为5级医生',
  6: '购药患者数为50～79个，为6级医生',
  7: '购药患者数为80个以上，为7级医生'
}
export default {
  name: 'DoctorOperationsAnalysis',
  data() {
    return {
      date: this.getPreviousMonth(),
      levelList: [],
      pickerOptions: this.handelFixDate(),
      chart: null,
      doctorSaleChart: null, //医生销售额chart
      patientChart: null, //患者数分析
      doctorLevelList,
      total: 0,
      listQuery: {
        departmentName: '', //科室名称
        levels: [], //医生等级
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      increaseImgSrc: require('@/assets/images/ic_increase.png'),
      reduceImgSrc: require('@/assets/images/ic_reduce.png')
    }
  },
  mounted() {
    // 初始化医生等级分布
    this.fetchDoctorLevel()
    // 医生销售排行
    this.fetchDoctorSalesRank()
    // 患者数分析
    this.fetchPatient()
    // 医生数据明细
    this.fetchDoctorDetail()
    // 监听窗口大小变化，重新渲染图表
    window.addEventListener('resize', this.handleResize)
  },
  destroyed() {
    // 组件销毁时，移除窗口大小变化的事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 当月之前月份可选
    handelFixDate() {
      return {
        disabledDate: (time) => {
          const currentMonth = new Date()
          // 设为当月第一天的00:00:00
          currentMonth.setDate(1)
          currentMonth.setHours(0, 0, 0, 0)
          // 传入的时间小于当前月的1号则可选(之前的月份)，否则禁选(当月及以后)
          return time.getTime() >= currentMonth.getTime()
        }
      }
    },
    getPreviousMonth() {
      const now = new Date()
      now.setMonth(now.getMonth() - 1)
      return `${now.getFullYear()}-${this.padZero(now.getMonth() + 1)}`
    },
    padZero(num) {
      return num.toString().padStart(2, '0')
    },
    // 在窗口大小变化时重新渲染每个图表
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
      if (this.doctorSaleChart) {
        this.doctorSaleChart.resize()
      }
      if (this.patientChart) {
        this.patientChart.resize()
      }
    },
    // 医生数据明细
    fetchList() {},
    // 等级分布chart
    initLevelEchart(thisData) {
      this.chart = echarts.init(document.getElementById('levelEchart'))
      this.chart.setOption({
        color: ['#315FFF', '#209BFF', '#00B876', '#FDE622', '#F97A0F', '#544DF1', '#E9290E'],
        tooltip: {
          trigger: 'item',
          formatter(params) {
            // console.log(params)
            return (
              params.marker +
              params.name +
              '<br/>' +
              '&nbsp;&nbsp;&nbsp;' +
              params.value +
              '&nbsp;&nbsp;&nbsp;' +
              params.percent +
              '%'
            )
          }
        },
        title: {
          text: `有效医生数`,
          subtext: thisData.effectiveCount,
          left: 'center', //对齐方式居中
          top: 'center', //距离顶部
          textStyle: {
            color: '#999999', //文字颜色
            fontSize: 14, //字号
            align: 'center' //对齐方式
          },
          subtextStyle: {
            color: '#000000',
            fontSize: 28,
            fontFamily: 'DINPro, DINPro',
            fontWeight: 500
          }
        },
        series: [
          {
            type: 'pie',
            radius: ['50%', '73%'],
            center: ['50%', '55%'],
            avoidLabelOverlap: false,
            hoverAnimation: true,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true
            },
            labelLine: {
              show: true
            },
            // eslint-disable-next-line no-dupe-keys
            avoidLabelOverlap: true,
            // minAngle: 5,
            data: thisData.levels.map((v) => {
              return {
                value: v.count,
                name: levelMap[v.level]
              }
            })
          }
        ]
      })
    },
    // 日期change
    dateChange(val) {
      this.fetchDoctorLevel()
      this.fetchDoctorSalesRank()
      this.fetchPatient()
      this.fetchDoctorDetail()
    },
    // 获取医生等级数据
    async fetchDoctorLevel() {
      try {
        const params = { date: this.date }
        const response = await getDoctorLevel(params)
        const { lastData, thisData } = response

        // 初始化 levelList 并将“有效医生数”作为第一个元素
        const effectObj = {
          title: '有效医生数',
          count: thisData.effectiveCount,
          compare: thisData.effectiveCount - lastData.effectiveCount,
          info: '统计时间内，购药患者数大于等于1的医生总数'
        }

        this.levelList = [effectObj]

        // 处理其他级别数据并追加到 levelList
        thisData.levels.forEach((ele) => {
          const lastLevel = lastData.levels.find((last) => last.level === ele.level) || {}

          const obj = {
            title: levelMap[ele.level],
            bgColor: colorMap[ele.level],
            count: ele.count,
            compare: ele.count - (lastLevel.count || 0), // 防止 lastLevel 未找到时count为空
            info: tooltipMap[ele.level]
          }

          this.levelList.push(obj)
        })
        console.log(this.levelList)
        //初始化医生等级分布环形图
        this.$nextTick(() => {
          this.initLevelEchart(thisData)
        })
      } catch (error) {
        throw new Error(error)
      }
    },
    // 获取销售额总数
    getTotalValue(data) {
      let total = 0
      for (let i = 0; i < data.length; i++) {
        total += parseFloat(data[i].sales) || 0
      }
      return total
    },
    // 医生销售额排行chart
    initDoctorSalesChart(response) {
      this.doctorSaleChart = echarts.init(document.getElementById('doctorSaleChart'))
      const totalValue = this.getTotalValue(response)
      this.doctorSaleChart.setOption({
        color: ['#3175FF'],
        tooltip: {
          trigger: 'axis',
          formatter(params) {
            // console.log(params)
            return (
              params[0].marker +
              params[0].name +
              '<br/>' +
              '&nbsp;&nbsp;&nbsp;' +
              (params[0].value / 100) * totalValue +
              '元' +
              '&nbsp;&nbsp;&nbsp;' +
              (params[0].value).toFixed(2) +
              '%'
            )
          }
        },
        grid: {
          left: '0%',
          right: '6%',
          bottom: '5%',
          top: 30,
          containLabel: true
        },
        xAxis: {
          max: '100',
          type: 'value',
          axisLabel: {
            formatter: '{value}%',
            textStyle: {
              color: '#888A98',
              fontSize: 12,
              fontWeight: 400
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D4D7DD'
            }
          }
        },
        yAxis: {
          type: 'category',
          data: response.map((v) => levelMap[v.level]),
          axisLabel: {
            textStyle: {
              color: '#888A98',
              fontSize: 12,
              fontWeight: 400
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D4D7DD'
            }
          }
        },
        series: [
          {
            type: 'bar',
            barWidth: 20,
            data: response.map((v) => (v.sales / totalValue) * 100),
            label: {
              show: true,
              position: ['88%', '-80%'],
              textStyle: {
                color: '#333',
                fontSize: 14,
                fontWeight: 400
              },
              formatter(params) {
                return (params.value / 100) * totalValue + '元'
              }
            }
          }
        ]
      })
    },
    // 医生销售额排行
    async fetchDoctorSalesRank() {
      try {
        const params = { date: this.date }
        const response = await getDoctorSalesRank(params)
        //初始化医生销售额排行chart
        this.$nextTick(() => {
          this.initDoctorSalesChart(response)
        })
      } catch (error) {
        throw new Error(error)
      }
    },
    // 患者数分析chart
    initPatientChart(response) {
      this.patientChart = echarts.init(document.getElementById('patientChart'))
      this.patientChart.setOption({
        color: ['#3175FF', '#00B876', '#F97A0F'],
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '2%',
          right: '2%',
          bottom: '5%',
          containLabel: true
        },
        legend: {
          x: 'right',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: '#888A98',
            fontSize: 14,
            fontFamily: 'PingFangSC, PingFang SC',
            fontWeight: 400
          },
          itemGap: 24,
          data: ['本月新增关注', '本月互动患者', '本月购药患者']
        },
        xAxis: [
          {
            type: 'category',
            data: response.map((v) => levelMap[v.level]),
            axisLabel: {
              textStyle: {
                color: '#888A98',
                fontSize: 12,
                fontWeight: 400
              }
            },
            axisLine: {
              lineStyle: {
                color: '#D4D7DD'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位：人',
            axisLabel: {
              textStyle: {
                color: '#888A98',
                fontSize: 12,
                fontWeight: 400
              }
            },
            axisLine: {
              lineStyle: {
                color: '#D4D7DD'
              }
            }
          }
        ],
        series: [
          {
            name: '本月新增关注',
            type: 'bar',
            barWidth: 20,
            data: response.map((v) => v.followersSum)
          },
          {
            name: '本月互动患者',
            type: 'bar',
            barWidth: 20,
            data: response.map((v) => v.consultPatientSum)
          },
          {
            name: '本月购药患者',
            type: 'bar',
            barWidth: 20,
            data: response.map((v) => v.purchasingPatientSum)
          }
        ]
      })
    },
    // 患者数分析
    async fetchPatient() {
      try {
        const params = { date: this.date }
        const response = await getPatient(params)
        // 初始化患者数分析
        this.$nextTick(() => {
          this.initPatientChart(response)
        })
      } catch (error) {
        throw new Error(error)
      }
    },
    // 医生数据明细
    async fetchDoctorDetail() {
      try {
        const params = { date: this.date, ...this.listQuery }
        const response = await getDoctorDetail(params)
        const { list, totalCount } = response
        this.total = totalCount
        this.tableData = list.map((v) => {
          return {
            doctorId: v.thisData.doctorId, // 医生id
            doctorName: v.thisData.doctorName, //医生姓名
            departmentName: v.thisData.departmentName, // 科室
            doctorPhone: v.thisData.doctorPhone, // 手机号
            level: v.thisData.level + '级', // 医生等级
            levelCompare: v.lastComparedData.level, // 医生等级较上月
            patientCount: v.thisData.patientCount, // 患者数
            consultPatientCount: v.thisData.consultPatientCount, // 互动患者数
            purchasingPatientCount: v.thisData.purchasingPatientCount, // 购药患者数
            purchasingPatientCountCompare: v.lastComparedData.purchasingPatientCount, // 购药患者数较上月
            followPatientCount: v.thisData.followPatientCount, // 本月新增关注
            sales: v.thisData.sales, // 销售额
            salesCompare: v.lastComparedData.sales // 销售额较上月
          }
        })
      } catch (error) {
        throw new Error(error)
      }
    },
    doctorLevelChange(val) {
      if (!val) return
      this.fetchDoctorDetail()
    },
    //回车搜索
    searchEnterFun() {
      this.fetchDoctorDetail()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-pagination {
  text-align: right;
}
.app-container {
  background: #f8f9fb;
  .time-warpper {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 4px;
    padding: 20px;
    display: flex;
    align-items: center;
    .time_t {
      font-size: 14px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #091033;
    }
  }
  .level-warpper {
    width: 100%;
    // height: 362px;
    height: auto;
    background: #ffffff;
    border-radius: 4px;
    margin-top: 16px;
    padding: 20px;
    .title {
      font-size: 16px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #091033;
    }
    .level-content {
      display: flex;
      justify-content: space-between;
      .right {
        flex: 1;
        width: 100%;
        #levelEchart {
          height: 100%;
        }
      }
      .left {
        display: flex;
        justify-content: flex-start;
        flex: 2;
        flex-wrap: wrap;
        margin-top: 40px;
        .item1 {
          border-left: 3px solid rgba(49, 117, 255, 0.2) !important;
          border-radius: 4px 0px 0px 4px !important;
          padding-left: 30px !important;
        }
        .item {
          width: calc((100% - 48px) / 4);
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #edf2f8;
          display: flex;
          padding: 10px;
          margin: 0 16px 20px 0;
          &:nth-child(4n) {
            margin-right: 0;
          }
          .item-icon {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            background: #315fff;
            margin: 3px 10px 0 0;
          }
          .item-detail {
            .t1 {
              font-size: 14px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 400;
              color: #888a98;
              display: flex;
              align-items: center;
              .icon_info {
                margin-left: 3px;
                font-size: 12px;
                padding: 0px;
                margin: 0px 0px 0px 3px;
              }
            }
            .num {
              font-size: 25px;
              font-family: DINPro, DINPro;
              font-weight: bold;
              color: #333333;
              margin: 8px 0px;
            }
            .compare {
              display: flex;
              align-items: center;
              .compare-num {
                display: flex;
                align-items: center;
              }
              .s1 {
                font-size: 14px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                color: #555866;
              }
              .s2 {
                font-size: 14px;
                font-family: DINPro, DINPro;
                font-weight: 500;
                color: #f05542;
                margin-left: 8px;
              }
              .s3 {
                font-size: 14px;
                font-family: DINPro, DINPro;
                font-weight: 500;
                color: #315fff;
                margin-left: 8px;
              }
            }
          }
        }
      }
    }
    .filter-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .level-warpper-doc {
    display: flex;
    height: 404px;
    border-radius: 4px;
    margin-top: 16px;
    .wrap {
      flex: 1;
      padding: 20px;
      background: #fff;
      .title {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #091033;
        line-height: 22px;
      }
      #doctorSaleChart,
      #patientChart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
