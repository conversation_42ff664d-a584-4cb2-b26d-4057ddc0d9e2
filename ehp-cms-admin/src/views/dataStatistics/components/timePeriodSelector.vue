<template>
  <div>
    <el-select v-model="selectedTime" placeholder="请选择" style="width: 100px;" @change="selectChanged">
      <el-option v-for="item in timeOptions" :key="item.value" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
    <span class="time_text">{{ dateRangeText }}</span>
  </div>
</template>

<script>
const timeOptions = [
  {
    value: '7',
    label: '近7天'
  },
  {
    value: '30',
    label: '近一个月'
  },
  {
    value: '90',
    label: '近三个月'
  },
  {
    value: '180',
    label: '近半年'
  },
  {
    value: '13',
    label: '月趋势'
  }
]
export default {
  name: 'TimePeriodSelector',
  props: {
    date: {
      type: Array,
      default: () => [],
      require: true
    }
  },
  data() {
    return {
      timeOptions,
      selectedTime: '7' // 默认选择近7天
    }
  },
  computed: {
    dateRangeText() {
      // console.log(this.date)
      return `${this.date[0]} 至 ${this.date[this.date.length - 1]}`
    }
  },
  methods: {
    // 辅助函数，将数字转为两位数的字符串
    formatNumber(num) {
      return num < 10 ? '0' + num : String(num)
    },
    // 获取近13个月
    getLast13MonthsDateRange() {
      const today = new Date() // 获取当前日期
      const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0) // 上个月的最后一天

      // 获得13个月前的日期，从上个月的最后一天往前数
      const startOfPeriod = new Date(
        endOfLastMonth.getFullYear(),
        endOfLastMonth.getMonth() - 12,
        1 // 月份的第一天
      )

      // 格式化日期为 YYYY-MM-DD
      const formatDate = (date) =>
        date.getFullYear() + '-' + (date.getMonth() + 1).toString().padStart(2, '0')

      // 获得格式化后的日期范围
      const dateRange = formatDate(startOfPeriod) + '至' + formatDate(endOfLastMonth)

      return dateRange
    },
    selectChanged(value) {
      console.log(value)
      this.$emit('timeSelectChange', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.time_text {
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #091033;
  margin-left: 15px;
}
</style>