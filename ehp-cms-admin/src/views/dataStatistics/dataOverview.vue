<template>
  <div v-loading="loading" class="app-container">
    <!-- 实施概况 -->
    <div class="real-time">
      <div class="refresh">
        <div class="refresh-btn">
          <h2>数据概况</h2>
          <p v-if="overTime == 'today'">{{ dateTime }}</p>
          <div v-if="overTime == 'today'" @click="refresh">刷新</div>
        </div>
        <div class="refresh-select">
          <el-date-picker
            v-model="overTime"
            type="daterange"
            align="right"
            unlink-panels
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            value-format="yyyy-MM-dd"
            @change="handleDateChange"
          >
          </el-date-picker>
        </div>
      </div>
      <div class="data">
        <div class="item">
          <!-- 问诊量 -->
          <div class="inquiry">
            问诊量
            <el-tooltip class="item" effect="dark" content="发起的图文、视频问诊数" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="volume">{{ consultCount }}</div>
        </div>
        <div class="item pl">
          <!-- 问诊收入 -->
          <div class="inquiry">
            问诊收入(元)
            <el-tooltip class="item" effect="dark" content="已支付的问诊收入" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="volume">{{ consultIncome }}</div>
        </div>
        <div class="item pl">
          <!-- 成交订单量 -->
          <div class="inquiry">
            成交订单量
            <el-tooltip class="item" effect="dark" content="已支付的订单数" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="volume">{{ orderCount }}</div>
        </div>
        <div class="item pl br">
          <!-- 订单销售额 -->
          <div class="inquiry">
            订单销售额(元)
            <el-tooltip class="item" effect="dark" content="已支付的订单销售额" placement="top-start">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
          <div class="volume">{{ orderIncome }}</div>
        </div>
      </div>
    </div>

    <!-- 问诊/处方趋势 -->
    <div class="overview">
      <div class="overview-left">
        <div class="header">
          <div class="title">问诊/处方趋势</div>
          <div class="select-time">
            <el-date-picker
              v-model="consultTimeRange"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="consultPickerOptions"
              value-format="yyyy-MM-dd"
              @change="handleConsultChange"
            >
            </el-date-picker>
          </div>
        </div>
        <div id="myChart1" ref="myChart1"></div>
      </div>
      <!-- 问诊科室排行 -->
      <div class="overview-right">
        <div class="overview-right-header">
          <div class="title">问诊科室排行</div>
          <div class="switch">
            <span class="switch_text" :class="getClass(1)" @click="departmentTab(1)"> 图文 </span>
            <span class="line">丨</span>
            <span class="switch_text" :class="getClass(2)" @click="departmentTab(2)"> 视频 </span>
          </div>
        </div>
        <el-table
          v-loading="loadingDepartment"
          class="customer-no-border-table"
          :data="tableData"
          :row-style="{ height: '53px' }"
          :header-row-style="{ height: '53px' }"
          :header-cell-style="{
            background: '#F8F9FB'
          }"
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column label="排行" type="index" align="left">
            <template slot-scope="scope">
              <img v-if="scope.row.ranking === 1" src="@/assets/images/ic_01.png" style="width: 22px;" />
              <img v-else-if="scope.row.ranking === 2" src="@/assets/images/ic_02.png" style="width: 22px;" />
              <img v-else-if="scope.row.ranking === 3" src="@/assets/images/ic_03.png" style="width: 22px;" />
              <div v-else class="rank">{{ scope.row.ranking }}</div>
            </template>
          </el-table-column>
          <el-table-column label="科室名称" width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="department_name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="问诊患者数" width="80" align="center" prop="patientCount">
            <template slot-scope="scope">
              <span>{{ scope.row.patientCount }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="consultCount" width="80" label="问诊量" align="right">
            <template slot-scope="scope">
              <span :class="scope.row.ranking === 1 ? 'consultation_volume' : 'consultation_volume_normal'">{{
                scope.row.consultCount
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 商品订单分析 -->
    <div class="overview">
      <div class="overview-left">
        <div class="header">
          <div class="title">商品订单分析</div>
          <div class="select-time">
            <el-date-picker
              v-model="productTimeRange"
              type="daterange"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="productPickerOptions"
              value-format="yyyy-MM-dd"
              @change="handleProductChange"
            >
            </el-date-picker>
          </div>
        </div>
        <div id="myChart2" ref="myChart2"></div>
      </div>
      <!-- 商品销售排行 -->
      <div class="overview-right">
        <div class="overview-right-header">
          <div class="title">商品销售排行</div>
          <div class="switch">
            <span class="switch_text" :class="getProductClass(1)" @click="productTab(1)"> 销售量 </span>
            <span class="line">丨</span>
            <span class="switch_text" :class="getProductClass(2)" @click="productTab(2)"> 销售额 </span>
          </div>
        </div>
        <el-table
          v-loading="loadingSale"
          class="customer-no-border-table"
          :data="tableDataGood"
          :row-style="{ height: '53px' }"
          :header-row-style="{ height: '53px' }"
          :header-cell-style="{
            background: '#F8F9FB'
          }"
          style="width: 100%; margin-top: 20px;"
        >
          <el-table-column label="排行" type="index" align="left">
            <template slot-scope="scope">
              <img v-if="scope.row.ranking === 1" src="@/assets/images/ic_01.png" style="width: 22px;" />
              <img v-else-if="scope.row.ranking === 2" src="@/assets/images/ic_02.png" style="width: 22px;" />
              <img v-else-if="scope.row.ranking === 3" src="@/assets/images/ic_03.png" style="width: 22px;" />
              <div v-else class="rank">{{ scope.row.ranking }}</div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" width="200" show-overflow-tooltip>
            <template slot-scope="scope">
              <span class="department_name">{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="productType === 1" width="80" prop="quantity" label="销售量" align="right">
            <template slot-scope="scope">
              <span :class="scope.row.ranking === 1 ? 'consultation_volume' : 'consultation_volume_normal'">{{
                scope.row.quantity
              }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="productType === 2" width="80" prop="quantity" label="销售额" align="right">
            <template slot-scope="scope">
              <span :class="scope.row.ranking === 1 ? 'consultation_volume' : 'consultation_volume_normal'">{{
                scope.row.sales
              }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
// import TimePeriodSelector from './components/timePeriodSelector'
import { getDashboard, getConsult, getConsultRank, getProducts, getProductOrder } from '@/api/dataStatistics'
export default {
  name: 'DataOverview',
  components: {
    // TimePeriodSelector
  },
  data() {
    return {
      loading: false,
      loadingDepartment: false,
      loadingSale: false,
      dateTime: '',
      chart: null,
      tableData: [], //科室排行榜
      tableDataGood: [], // 商品销售排行
      chartGoods: null,
      consultCount: '', // 今日访问量
      consultIncome: '', // 今日问诊收入
      orderCount: '', // 今日成交订单量
      orderIncome: '', // 今日订单销售额
      consultNum: '', // 问诊趋势数据范围
      consultDate: [], // 问诊趋势数据
      productType: 1,
      productDate: [], //商品订单分析
      productNum: '', // 商品订单分析数据范围
      selectedWord: 1,
      overviewOption: [
        {
          value: 'today',
          label: '今天'
        },
        {
          value: 'yesterday',
          label: '昨天'
        },
        {
          value: 'last3Days',
          label: '近3天'
        },
        {
          value: 'last7Days',
          label: '近7天'
        },
        {
          value: 'lastMonth',
          label: '近1个月'
        },
        {
          value: 'last3Months',
          label: '近3个月'
        }
      ],
      overTime: [], // 修改为数组类型
      overRangetime: '',
      start: '',
      end: '',
      pickerOptions: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const today = new Date()
              picker.$emit('pick', [today, today])
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const yesterday = new Date()
              yesterday.setDate(yesterday.getDate() - 1)
              picker.$emit('pick', [yesterday, yesterday])
            }
          },
          {
            text: '近3天',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date()
              start.setDate(start.getDate() - 3) // 从3天前开始
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date()
              start.setDate(start.getDate() - 7) // 从7天前开始
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近1个月',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end) // 从结束日期复制一个新日期
              start.setDate(start.getDate() - 29) // 往前推30天（包括开始日期）
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近3个月',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end) // 从结束日期复制一个新日期
              start.setDate(start.getDate() - 89) // 往前推90天（包括开始日期）
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      consultTimeRange: [], // 问诊趋势时间范围
      productTimeRange: [], // 商品订单时间范围
      consultPickerOptions: {
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 6) // 从昨天往前推6天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近一个月',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 29) // 从昨天往前推29天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近三个月',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 89) // 从昨天往前推89天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近半年',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 179) // 从昨天往前推179天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '月趋势',
            onClick(picker) {
              const now = new Date()
              // 获取13个月前的第一天
              const start = new Date(now.getFullYear(), now.getMonth() - 13, 1)
              // 获取当前月的最后一天
              const end = new Date(now.getFullYear(), now.getMonth(), 0)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      productPickerOptions: {
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 6) // 从昨天往前推6天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近一个月',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 29) // 从昨天往前推29天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近三个月',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 89) // 从昨天往前推89天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近半年',
            onClick(picker) {
              const end = new Date()
              end.setDate(end.getDate() - 1) // 截止到昨天
              const start = new Date(end)
              start.setDate(start.getDate() - 179) // 从昨天往前推179天
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '月趋势',
            onClick(picker) {
              const now = new Date()
              // 获取13个月前的第一天
              const start = new Date(now.getFullYear(), now.getMonth() - 13, 1)
              // 获取当前月的最后一天
              const end = new Date(now.getFullYear(), now.getMonth(), 0)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  created() {
    this.refresh()
    // 实时概况数据
    this.fetchRealData()
    // 问诊科室排行榜
    this.fetchConsultRank(1)
    // 商品销售排行榜
    this.fetchProductRank(1)
  },
  mounted() {
    // 设置默认日期为今天
    const today = new Date()
    const todayStr = this.formatDate(today)
    this.overTime = [todayStr, todayStr]

    // 设置问诊趋势默认时间范围（近7天）
    const consultEnd = new Date()
    consultEnd.setDate(consultEnd.getDate() - 1) // 截止到昨天
    const consultStart = new Date(consultEnd)
    consultStart.setDate(consultStart.getDate() - 6) // 从昨天往前推6天
    this.consultTimeRange = [this.formatDate(consultStart), this.formatDate(consultEnd)]

    // 设置商品订单默认时间范围（近7天）
    const productEnd = new Date()
    productEnd.setDate(productEnd.getDate() - 1) // 截止到昨天
    const productStart = new Date(productEnd)
    productStart.setDate(productStart.getDate() - 6) // 从昨天往前推6天
    this.productTimeRange = [this.formatDate(productStart), this.formatDate(productEnd)]

    // 问诊处方趋势
    this.fetchConsult()
    // 商品订单分析
    this.fetchProductOrder()
    // 监听窗口大小变化，重新渲染图表
    window.addEventListener('resize', this.handleResize)
    this.handleDateChange(this.overTime)
  },
  destroyed() {
    // 组件销毁时，移除窗口大小变化的事件监听
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 初始化问诊/处方趋势折线图
    initChart(data) {
      this.chart = echarts.init(document.getElementById('myChart1'))
      // 滚动条插件
      const dataZoom = [
        {
          type: 'inside',
          start: 0, //数据窗口范围的起始百分比
          end: 50, //数据窗口范围的结束百分比
          zoomLock: false
        }
      ]
      this.chart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          left: 'left',
          itemGap: 24, // 图例项之间的间距
          data: ['问诊量', '处方量']
        },
        grid: {
          left: 0,
          right: 0,
          top: '15%',
          bottom: '10%',
          containLabel: true
        },
        dataZoom: this.consultNum > 7 ? dataZoom : [],
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: data.map((v) => v.date),
          axisLabel: {
            textStyle: {
              color: '#888A98',
              fontSize: 12,
              fontWeight: 400
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D4D7DD',
              fontSize: 12,
              fontWeight: 400
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            textStyle: {
              color: '#888A98'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#D4D7DD',
              fontSize: 12,
              fontWeight: 400
            }
          }
        },
        series: [
          {
            name: '问诊量',
            type: 'line',
            data: data.map((v) => v.consultCount),
            itemStyle: {
              color: '#3175FF'
            }
          },
          {
            name: '处方量',
            type: 'line',
            data: data.map((v) => v.prescriptionCount),
            itemStyle: {
              color: '#00B876'
            }
          }
        ]
      })
    },
    // 求和
    sum(arr) {
      return arr.reduce((prev, curr, idx, arr) => {
        return prev + curr
      })
    },
    // 初始化商品订单分析柱状图
    initChartGoods(data) {
      var _this = this
      _this.chartGoods = echarts.init(document.getElementById('myChart2'))
      // 滚动条插件
      const dataZoom = [
        {
          type: 'inside',
          start: 0, //数据窗口范围的起始百分比
          end: 50, //数据窗口范围的结束百分比
          zoomLock: false
        }
      ]
      _this.chartGoods.setOption({
        // color: ['rgba(49,117,255,0.4)', '#00B876'],
        color: ['#00B876', 'rgba(49,117,255,0.4)'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          },
          formatter(params) {
            const sum = _this.sum([params[0].value, params[1].value]) //求和
            return (
              params[0].axisValue +
              '<br/>' +
              params[0].marker +
              params[0].seriesName +
              '&nbsp;&nbsp;&nbsp;' +
              params[0].value +
              '<br/>' +
              params[1].marker +
              params[1].seriesName +
              '&nbsp;&nbsp;&nbsp;' +
              params[1].value +
              '<br/>' +
              '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:#3175FF;"></span>' +
              '总订单' +
              '&nbsp;&nbsp;&nbsp;' +
              sum
            )
          }
        },
        legend: {
          left: 'left',
          selectedMode: false,
          itemGap: 24, // 图例项之间的间距
          data: ['已支付', '未支付']
        },
        grid: {
          left: '1%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          data: data.map((v) => v.date),
          axisLine: {
            lineStyle: {
              color: '#D4D7DD',
              fontSize: 12,
              fontWeight: 400
            }
          },
          axisLabel: {
            textStyle: {
              color: '#888A98',
              fontSize: 12,
              fontWeight: 400
            }
          }
        },
        dataZoom: this.productNum > 7 ? dataZoom : [],
        yAxis: [
          {
            type: 'value',
            axisLine: {
              lineStyle: {
                color: '#D4D7DD',
                fontSize: 12,
                fontWeight: 400
              }
            },
            axisLabel: {
              textStyle: {
                color: '#888A98',
                fontSize: 12,
                fontWeight: 400
              }
            }
          }
        ],
        series: [
          {
            name: '已支付',
            stack: 'a',
            type: 'bar',
            barWidth: 24,
            data: data.map((v) => v.paidCount)
          },
          {
            name: '未支付',
            stack: 'a',
            type: 'bar',
            barWidth: 24,
            data: data.map((v) => (v.orderCount - v.paidCount))
          }
        ]
      })
    },
    // 在窗口大小变化时重新渲染每个图表
    handleResize() {
      if (this.chart) {
        this.chart.resize()
      }
      if (this.chartGoods) {
        this.chartGoods.resize()
      }
    },
    // 实时刷新
    refresh() {
      const currentDate = new Date()
      const year = currentDate.getFullYear()
      const month = String(currentDate.getMonth() + 1).padStart(2, '0')
      const day = String(currentDate.getDate()).padStart(2, '0')
      const hours = String(currentDate.getHours()).padStart(2, '0')
      const minutes = String(currentDate.getMinutes()).padStart(2, '0')
      const seconds = String(currentDate.getSeconds()).padStart(2, '0')

      this.dateTime = `更新于 ${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      // 刷新实况数据
      this.fetchRealData()
    },
    // 实况数据
    fetchRealData() {
      this.loading = true
      const params = {
        start: this.start,
        end: this.end
      }
      getDashboard(params)
        .then((resp) => {
          // console.log('resp:>', resp)
          this.loading = false
          this.consultCount = resp.consultCount.toLocaleString()
          this.consultIncome = resp.consultIncome.toLocaleString()
          this.orderCount = resp.orderCount.toLocaleString()
          this.orderIncome = resp.orderIncome.toLocaleString()
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 问诊处方趋势切换
    consultChange(num) {
      this.consultNum = num
      const type = num === '7' || num === '30' || num === '90' || num === '180' ? 1 : 2
      this.fetchConsult(num, type)
    },
    // 问诊处方趋势
    fetchConsult(num = '7', type = 1) {
      getConsult({ num, type })
        .then((resp) => {
          // console.log(resp)
          if (resp && resp.length > 0) {
            this.consultDate = resp.map((v) => v.date)
            this.initChart(resp)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 问诊科室排行榜
    fetchConsultRank(type) {
      this.loadingDepartment = true
      getConsultRank({ type })
        .then((resp) => {
          this.loadingDepartment = false
          this.tableData = resp
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 图文/视频tab
    departmentTab(value) {
      this.selectedWord = value
      this.fetchConsultRank(value)
    },
    // 商品销售排行
    fetchProductRank(type) {
      this.loadingSale = true
      getProducts({ type })
        .then((resp) => {
          this.loadingSale = false
          this.tableDataGood = resp
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 销售量/销售额tab
    productTab(value) {
      this.productType = value
      this.fetchProductRank(value)
    },
    // 商品订单分析
    fetchProductOrder(num = '7', type = 1) {
      getProductOrder({ num, type })
        .then((resp) => {
          this.productDate = resp.map((v) => v.date)
          this.initChartGoods(resp)
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 选择商品订单分析change
    productChange(num) {
      this.productNum = num
      const type = num === '7' || num === '30' || num === '90' || num === '180' ? 1 : 2
      this.fetchProductOrder(num, type)
    },
    getClass(wordId) {
      return {
        selected: this.selectedWord === wordId
      }
    },
    getProductClass(value) {
      return {
        selected: this.productType === value
      }
    },
    handleDateChange(dates) {
      if (!dates) {
        return
      }
      this.start = dates[0]
      this.end = dates[1]
      this.overRangetime = `${this.start} 至 ${this.end}`
      this.fetchRealData()
    },
    // 格式化日期为 yyyy-MM-dd
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    // 处理问诊趋势时间变化
    handleConsultChange(dates) {
      if (!dates) return
      // 判断是否是月趋势
      const start = new Date(dates[0])
      const end = new Date(dates[1])
      if (start.getDate() === 1 && end.getDate() === new Date(end.getFullYear(), end.getMonth() + 1, 0).getDate()) {
        // 如果开始日期是1号，结束日期是月末，说明是月趋势
        this.consultChange('13')
      } else {
        const diffDays = Math.floor((end - start) / (1000 * 60 * 60 * 24)) + 1
        this.consultChange(String(diffDays))
      }
    },
    // 处理商品订单时间变化
    handleProductChange(dates) {
      if (!dates) return
      // 判断是否是月趋势
      const start = new Date(dates[0])
      const end = new Date(dates[1])
      if (start.getDate() === 1 && end.getDate() === new Date(end.getFullYear(), end.getMonth() + 1, 0).getDate()) {
        // 如果开始日期是1号，结束日期是月末，说明是月趋势
        this.productChange('13')
      } else {
        const diffDays = Math.floor((end - start) / (1000 * 60 * 60 * 24)) + 1
        this.productChange(String(diffDays))
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-table {
  border: 0;
  th,
  tr,
  td {
    border: 0;
    background-color: #fff;
  }
  &::before {
    height: 0px;
  }
  &::after {
    width: 0;
  }
  .el-table__fixed:before {
    height: 0;
  }
}
.app-container {
  background: #f8f9fb;
  .real-time {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 4px;
    padding: 20px;
    .refresh {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .refresh-btn {
        display: flex;
        align-items: center;
      }
      h2 {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #091033;
      }
      p {
        font-size: 12px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #888a98;
        margin: 0px 16px;
      }
      div {
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #3175ff;
        cursor: pointer;
      }
    }
    .data {
      margin-top: 20px;
      display: flex;
      .pl {
        padding-left: 20px;
      }
      .br {
        border-right: none !important;
      }
      .item {
        border-right: 1px solid #eee;
        flex: 1;
        .inquiry {
          font-size: 14px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #888a98;
        }
        .volume {
          font-size: 25px;
          font-family: DINPro, DINPro;
          font-weight: bold;
          color: #333333;
          margin-top: 10px;
        }
      }
    }
  }
  .overview {
    border-radius: 4px;
    margin-top: 16px;
    display: flex;
    .overview-left {
      flex: 2.5;
      padding: 20px;
      height: 395px;
      margin-right: 16px;
      background: #ffffff;
      #myChart1,
      #myChart2 {
        width: 100%;
        height: 100%;
      }
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 20px;
        .title {
          font-size: 16px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          color: #091033;
        }
        .select-time {
          display: flex;
          align-items: center;
          span {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #091033;
            margin-left: 12px;
          }
        }
      }
    }
    .overview-right {
      flex: 0.7;
      max-width: 374px;
      padding: 20px;
      background: #ffffff;
      .overview-right-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .switch {
          display: flex;
          .line {
            color: #888a98;
          }
          .switch_text {
            font-size: 14px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #888a98;
            cursor: pointer;
          }
          .selected {
            color: #315fff !important;
          }
        }
      }
      .title {
        font-size: 16px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #091033;
      }
      .rank {
        width: 16px;
        height: 16px;
        text-align: center;
        line-height: 16px;
        border-radius: 50%;
        background: #d9d9d9;
        font-size: 10px;
        font-family: DINPro, DINPro;
        font-weight: bold;
        color: #ffffff;
        margin-left: 3px;
      }
      .department_name {
        font-size: 14px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #091033;
      }
      .consultation_volume {
        font-size: 14px;
        font-family: DINPro, DINPro;
        font-weight: bold;
        color: #ffa20b;
      }
      .consultation_volume_normal {
        font-size: 14px;
        font-family: DINPro, DINPro;
        font-weight: bold;
        color: #091033;
      }
    }
  }
}
.time_text {
  font-size: 14px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #091033;
  margin-left: 15px;
}

// 添加日期选择器的样式
.select-time {
  ::v-deep .el-date-editor {
    width: 350px;
  }
}
</style>
