<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.title"
        clearable
        placeholder="标题"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-select
        v-model="listQuery.type"
        clearable
        placeholder="请选择"
        style="width:150px"
        @change="handleFilter"
      >
        <el-option label="院务公开" :value="1"></el-option>
        <el-option label="新闻资讯" :value="2"></el-option>
      </el-select>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        v-permission="['notice:message:save']"
        type="primary"
        @click="addNoticet"
      >添加
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
      <el-table-column label="标题" prop="title" align="center" width="150px" />
      <el-table-column
        label="类型"
        prop="createdAt"
        width="155px"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type == 1">
            院务公开
          </el-tag>
          <el-tag v-else type="success">新闻资讯</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" prop="releaseTime" align="center" />
      <el-table-column label="操作" width="200px" align="center">
        <template slot-scope="scope">
          <el-button
            v-permission="['notice:message:update']"
            type="primary"
            size="mini"
            @click="record(scope.row)"
          >编辑信息
          </el-button>
          <el-button
            v-permission="['notice:message:delete']"
            type="danger"
            size="mini"
            @click="deleteItem(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogNoticetTitle"
      :visible.sync="dialogTableVisible"
      width="80%"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="notice"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="标题" prop="title">
              <el-input v-model="notice.title" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="视频ID" prop="videoId">
              <el-input v-model="notice.videoId" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <wangEditor ref="editor" :text-value.sync="notice.content" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="10">
            <el-form-item label="文案类型" prop="type">
              <el-select
                v-model="notice.type"
                clearable
                placeholder="请选择"
                style="width:200px"
              >
                <el-option label="院务公开" :value="1"></el-option>
                <el-option label="新闻资讯" :value="2"></el-option>
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="发布时间" prop="releaseTime">
              <el-date-picker
                v-model="notice.releaseTime"
                style="width:200px"
                placeholder="选择发布时间"
                :value-format="dateFormat"
              >
              </el-date-picker>
            </el-form-item> -->
          </el-col>
          <el-col :span="10">
            <!-- <el-form-item label="发布时间" prop="releaseTime">
              <el-date-picker
                v-model="notice.releaseTime"
                style="width:200px"
                placeholder="选择发布时间"
                :value-format="dateFormat"
              >
              </el-date-picker>
            </el-form-item> -->
          </el-col>
          <el-col :span="10">
            <el-form-item
              v-if="notice.type == 2"
              label="选择分组"
              prop="groupIds"
            >
              <el-select
                v-model="typeList"
                style="width:200px"
                multiple
                collapse-tags
                clearable
                placeholder="请选择"
                @change="onGroupchange"
              >
                <el-option
                  v-for="item in group"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item v-if="notice.type == 2" label="封面图" prop="imgUrl">
            <uploadImage
              :value="notice.imgUrl"
              action="/notice/message/upload"
              list-type="picture-card"
              :show-file-list="false"
              accept="image/png, image/gif, image/jpeg, image/jpg"
              @success="onSuccess"
              @remove="onRemove"
            >
            </uploadImage>
          </el-form-item>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="close">取消</el-button>
        <el-button
          v-waves
          v-permission="['notice:message:save']"
          type="primary"
          @click="submit('dataForm')"
        >发布</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  updateNotice,
  addNotice,
  deleteNotice,
  groupList
} from '@/api/notice/index'
import waves from '@/directive/waves' // Waves directive
import wangEditor from '@/components/wangEditor'
import uploadImage from '@/components/uploadImage'
export default {
  name: 'DoctorTable',
  directives: { waves },
  filters: {},
  components: {
    wangEditor,
    uploadImage
  },
  data() {
    return {
      dialogTableVisible: false,
      dialogNoticetTitle: '详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      notice: {
        id: null,
        title: '',
        type: '',
        content: '',
        releaseTime: '',
        imgUrl: [],
        groupIds: []
      },
      typeList: [],
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }],
        type: [{ required: true, message: '请选择文案类型', trigger: 'blur' }],
        imgUrl: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.notice.imgUrl.length) {
                callback(new Error('请上传文案封面图'))
              } else {
                callback()
              }
            }
          }
        ],
        groupIds: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (!this.notice.groupIds.length) {
                callback(new Error('请上选择文案分组'))
              } else {
                callback()
              }
            }
          }
        ]
      },
      dateFormat: 'yyyy-MM-dd',
      group: []
    }
  },
  created() {
    this.handleFilter()
    this.getGroupList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getGroupList() {
      groupList().then(response => {
        console.log(response, 240)
        this.group = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    addNoticet() {
      this.dialogNoticetTitle = '添加'
      this.dialogTableVisible = true
      this.resetTemp()
      this.notice = {
        id: null,
        title: '',
        type: '',
        content: '',
        releaseTime: '',
        imgUrl: [],
        groupIds: []
      }
      this.typeList = []
      this.$nextTick(() => {
        this.$refs.editor.setValue('')
      })
    },
    submit(dataForm) {
      const that = this
      that.$refs[dataForm].validate(valid => {
        if (valid) {
          const params = Object.assign({}, that.notice)
          params.imgUrl = that.notice.imgUrl.map(item => item.url).join()
          if (that.notice.id) {
            updateNotice(params, that.notice.id).then(res => {
              that.$message.success('保存成功')
              that.getList()
              that.dialogTableVisible = false
            })
          } else {
            addNotice(params).then(res => {
              that.$message.success('保存成功')
              that.getList()
              that.dialogTableVisible = false
            })
          }
        }
      })
    },
    close() {
      const that = this
      that.dialogTableVisible = false
    },
    record(item) {
      this.dialogNoticetTitle = '编辑'
      this.dialogTableVisible = true
      this.resetTemp()
      this.notice = Object.assign({}, item)
      this.notice.imgUrl = this.notice.imgUrl
        ? [{ url: this.notice.imgUrl }]
        : []
      this.notice.groupIds = this.notice.groups
        ? this.notice.groups.map(val => val.id)
        : []
      this.typeList = this.notice.groupIds
      delete this.notice.groups
      this.$nextTick(() => {
        this.$refs.editor.setValue(item.content)
      })
    },
    deleteItem(item) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const params = {}
          params.id = item.id
          return deleteNotice(params)
        })
        .then(res => {
          this.$message.success('删除成功')
          this.getList()
        })
        .catch(e => {})
        .finally(res => {})
    },
    onSuccess(data) {
      this.notice.imgUrl.push({
        url: data.response.data,
        uid: data.uid
      })
      console.log(this.notice.imgUrl, 'onSuccess', 278)
    },
    onRemove(data) {
      this.notice.imgUrl = data
      console.log(this.notice.imgUrl, 'onRemove', 301)
    },
    onGroupchange(val) {
      this.notice.groupIds = val
      console.log(val, 339)
    }
  }
}
</script>
