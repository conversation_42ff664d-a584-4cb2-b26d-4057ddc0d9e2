<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-waves
        type="primary"
        @click="addGroupt"
      >添加
      </el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row style="width: 100%">
      <el-table-column label="ID" fixed prop="id" width="90px" align="center" />
      <el-table-column label="排序" prop="sort" align="center" width="100px" />
      <el-table-column label="分组名称" prop="name" align="center" width="100px" />
      <el-table-column label="分组类型" prop="createdAt" width="155px" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type == 1">
            院务公开
          </el-tag>
          <el-tag v-else type="success">新闻资讯</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdAt" align="center" />
      <el-table-column label="修改时间" prop="changedAt" align="center" />
      <el-table-column label="操作" width="200px" align="center">
        <template slot-scope="scope">
          <el-button
            v-permission="['notice:message:update']"
            type="primary"
            size="mini"
            @click="record(scope.row)"
          >编辑信息
          </el-button>
          <el-button
            v-permission="['notice:message:delete']"
            type="danger"
            size="mini"
            @click="deleteItem(scope.row)"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    /> -->
    <el-dialog
      :title="dialogNoticetTitle"
      :visible.sync="dialogTableVisible"
      width="500px"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="group"
        :rules="rules"
        label-position="right"
        label-width="100px"
        style="width:90%"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="group.name" placeholder="请输入分组名称" maxlength="20" style="width:310px" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select
            v-model="group.type"
            clearable
            placeholder="请选择"
            style="width:310px"
          >
            <!-- <el-option label="院务公开" :value="1"></el-option> -->
            <el-option label="新闻资讯" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序号" prop="sort">
          <el-input-number v-model="group.sort" :min="0" style="width:150px" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取消</el-button>
        <el-button type="primary" @click="submit('dataForm')">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import {
  getList,
  updateGroup,
  addGroup,
  deleteGroup
} from '@/api/notice/group'
import waves from '@/directive/waves' // Waves directive
export default {
  name: 'DoctorTable',
  directives: { waves },
  filters: {},
  data() {
    return {
      dialogTableVisible: false,
      dialogNoticetTitle: '分组详情',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'id',
        orderBy: 'desc'
      },
      group: {
        sort: 0
      },
      rules: {
        name: [{ required: true, message: '请输入分组名称', trigger: 'blur' }],
        type: [{ required: true, message: '请选择分组类型', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
        console.log(this.list, 151)
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    addGroupt() {
      this.group.id = null
      this.dialogNoticetTitle = '添加分组'
      this.dialogTableVisible = true
      this.resetTemp()
      this.group = {}
    },
    submit(dataForm) {
      const that = this
      console.log(that.notice, '===========submit========')
      that.$refs[dataForm].validate(valid => {
        console.log('valid', valid)
        if (valid) {
          const params = Object.assign({}, that.group)
          console.log(params, '===========11submit11========')
          if (that.group.id) {
            updateGroup(params, that.group.id).then(res => {
              this.$message.success('保存成功')
              that.getList()
              that.dialogTableVisible = false
            })
          } else {
            addGroup(params).then(res => {
              this.$message.success('保存成功')
              that.getList()
              that.dialogTableVisible = false
            })
          }
        }
      })
    },
    record(item) {
      this.dialogNoticetTitle = '编辑公告'
      this.dialogTableVisible = true
      this.resetTemp()
      this.group = Object.assign({}, item)
      this.$nextTick(() => {
        // this.$refs.editor.setValue(item.content)
      })
    },
    deleteItem(item) {
      this.$confirm('是否确认删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {}
        params.id = item.id
        return deleteGroup(params)
      }).then(res => {
        this.$message.success('删除成功')
        this.getList()
      }).catch(e => {

      }).finally(res => {

      })
    }
  }
}
</script>
