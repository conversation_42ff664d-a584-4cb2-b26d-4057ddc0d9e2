<template>
  <div class="app-container">
    <div class="filter-container">
      <!-- <el-select
        v-model="listQuery.status"
        clearable
        style="width: 150px"
        placeholder="任务状态"
        @change="handleFilter"
      >
        <el-option
          v-for="item in appStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select> -->
      <el-button
        type="primary"
        @click="handleAdd"
      >添加
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="任务名"
        prop="name"
        width="150"
        align="center"
      />
      <el-table-column
        label="任务组"
        prop="group"
        width="150"
        align="center"
      />
      <el-table-column
        label="状态"
        prop="status"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag size="small">{{ statusOptions[row.status] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="cron表达式"
        prop="cronExpression"
        width="150"
        align="center"
      />
      <el-table-column
        label="请求地址"
        prop="url"
        width="300"
        align="center"
      />
      <el-table-column
        label="描述"
        prop="description"
        width="150"
        align="center"
      />
      <el-table-column
        label="操作"
        width="300"
        align="center"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button
            v-if="row.status == 'PAUSED'"
            type="primary"
            size="mini"
            @click="handleEnable(row)"
          >启用</el-button>
          <el-button
            v-if="row.status == 'NORMAL'"
            size="mini"
            type="danger"
            @click="handleDisabled(row)"
          >停用</el-button>
          <el-button
            size="mini"
            type="danger"
            @click="handleCancel(row)"
          >取消任务</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(row)"
          >编辑信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="任务名"
              prop="name"
            >
              <el-input v-model="formData.name" :readonly="!isAdd" style="width: 200px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="任务组"
              prop="group"
            >
              <el-input v-model="formData.group" :readonly="!isAdd" style="width: 200px" />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="cron表达式"
              prop="cronExpression"
            >
              <el-input v-model="formData.cronExpression" style="width: 200px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="地址"
              prop="url"
            >
              <el-input v-model="formData.url" type="textarea" style="width: 200px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="描述"
            >
              <el-input v-model="formData.description" type="textarea" style="width: 200px" />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row :gutter="20">
          <el-col
            :span="24"
            style="text-align: right;"
          >
            <el-button
              v-waves
              type="primary"
              @click="handlesubmit('dataForm')"
            >提交
            </el-button>
            <el-button
              v-waves
              type="primary"
              @click="dialogVisible = false"
            >返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  scheduleAdd,
  scheduleCancel,
  scheduleRestart,
  scheduleList,
  scheduleStop,
  scheduleUpdate
} from '@/api/ops/index'
import waves from '@/directive/waves'

export default {
  name: 'Versions',
  directives: { waves },
  filters: {},
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '上传',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      formData: {},
      isAdd: true,
      statusOptions: {
        NORMAL: '正常',
        PAUSED: '暂停',
        COMPLETE: '完成',
        ERROR: '错误',
        NONE: '无',
        BLOCKE堵塞: '无'
      },
      rules: {
        url: [{ required: true, message: '请选输入请求地址', trigger: 'blur' }],
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        group: [{ required: true, message: '请输入任务组', trigger: 'blur' }],
        cronExpression: [{ required: true, message: '请输入cron表达式', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    getList() {
      scheduleList(this.listQuery).then((response) => {
        console.log(response, 'response')
        this.list = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleAdd() {
      this.isAdd = true
      this.dialogTitle = '上传'
      this.dialogVisible = true
      this.resetTemp()
    },
    handleEdit(item) {
      this.isAdd = false
      this.formData = item
      this.dialogTitle = '编辑'
      this.dialogVisible = true
    },
    handlesubmit(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          if (!this.isAdd) {
            this.update()
          } else {
            this.save()
          }
          this.getList()
        } else {
          this.$message.error('请检查表单是否填写完整')
        }
      })
    },
    save() {
      scheduleAdd(this.formData).then((res) => {
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.dialogVisible = false
      })
    },
    update() {
      scheduleUpdate(this.formData).then((res) => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.dialogVisible = false
      })
    },
    start(row) {
      scheduleRestart({
        group: row.group,
        name:	row.name
      }).then((res) => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    cancel(row) {
      scheduleCancel({
        group: row.group,
        name:	row.name
      }).then((res) => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    disabled(row) {
      scheduleStop({
        group: row.group,
        name:	row.name
      }).then((res) => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    handleEnable(row) {
      this.$confirm('是否启用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.start(row)
      })
    },
    handleDisabled(row) {
      this.$confirm('是否停用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.disabled(row)
      })
    },
    handleCancel(row) {
      this.$confirm('是否取消？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.cancel(row)
      })
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.$message({
          message: '上传成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.uploading = false
        this.formData.url = response.data
      }
    },
    resetTemp() {
      this.$nextTick(() => {
        this.formData = {}
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>

<style>
</style>
