<template>
  <div class="app-container">
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="名称"
        prop="title"
        width="150"
        align="center"
      />
      <el-table-column
        label="状态"
        align="center"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.status===1"
            size="small"
          >已检测</el-tag>
          <el-tag
            v-else
            size="small"
            type="danger"
          >未检测</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="100"
        align="center"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button
            type="primary"
            size="mini"
            @click="handletest(row)"
          >检测</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form
        ref="dataForm"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="100px"
      >
        <div v-if="formData.id == 4">
          <div>步骤1:</div>
          <div class="mgt10">前往微信公众平台： <el-link
            class="color-primary"
            @click="goUrl('https://mp.weixin.qq.com/')"
          >https://mp.weixin.qq.com/</el-link>
          </div>
          <div class="mgt10">步骤2:</div>
          <div class="mgt10">点击左侧菜单“版本管理”体验版页面路径修改为：<span class="color-primary">pages/payment/payment</span> </div>
          <el-image
            class="mgt10"
            mode="aspectFill"
            src="/static/images/1.jpg"
          ></el-image>
          <div class="mgt10">步骤3:</div>
          <div class="mgt10">修改完成之后微信扫码进入小程序完成支付、退款流程</div>
          <div class="mgt10">步骤4:</div>
          <div class="mgt10">自测完成之后点击检测通过、并恢复“版本管理”体验版页面：<span class="color-primary">pages/home/<USER>/span></div>
        </div>

        <!-- <el-image
          v-if="formData.id == 4"
          :src="qrcodeUrl"
        ></el-image> -->
        <el-row
          v-if="formData.id == 5"
          :gutter="20"
        >
          <el-col :span="20">
            <el-form-item
              label="手机号"
              prop="phone"
            >
              <el-input
                v-model="formData.phone"
                style="width:150px"
                placeholder="请输入手机号"
              />
              <el-button
                type="primary"
                @click.stop="handleSendCode('dataForm')"
              >{{ sms.message }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          v-if="formData.id == 11"
          :gutter="20"
        >
          <el-col :span="20">
            <el-form-item
              label="物流公司"
              prop="companyCode"
            >
              <el-select
                v-model="formData.companyCode"
                style="width:150px"
                class="elinput"
                placeholder="选择物流公司"
              >
                <el-option
                  v-for="item in logisticsOption"
                  :key="item.id"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            v-if="formData.id == 11"
            :span="20"
          >
            <el-form-item
              label="物流单号"
              prop="num"
            >
              <el-input
                v-model="formData.num"
                style="width:150px"
                placeholder="请输入物流单号"
              />
              <el-button
                type="primary"
                size="mini"
                @click="handleSubmit('dataForm')"
              >检测</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <div v-if="formData.id == 6">收到APP推送通知即为检测通过</div>
        <el-form-item
          v-if="formData.id == 13"
          label="图片地址"
          prop="num"
        >
          <el-input
            v-model="formData.fileUrl"
            style="width:160px"
            readonly
            placeholder=""
          />
          <el-button
            type="primary"
            size="mini"
            @click="handleOcr"
          >OCR识别</el-button>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSuccess"
        >检测通过</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="配置详情"
      :visible.sync="successdialogVisible"
      width="40%"
    >
      <div
        v-for="(val, keys, index) in responseData"
        :key="index"
        class="mgt10"
      >
        {{ keys }}：{{ val }}
      </div>
      <div slot="footer">
        <el-button
          type="primary"
          @click="successdialogVisible = false"
        >确 定</el-button>
      </div>
    </el-dialog>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  inspectList,
  systemCheck,
  inspectSuccess,
  inspectOcr
} from '@/api/ops/index'
import api from '@/api/exchange/exchange'
import waves from '@/directive/waves'

export default {
  name: 'Versions',
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      formData: {
        fileUrl: '/doctor/images/idcard.png'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      sms: {
        intervalId: 0,
        countDown: 0,
        message: '发送'
      },
      responseData: '',
      cardUrl: '/doctor/images/idcard.png',
      dialogTitle: '系统检测',
      dialogVisible: false,
      successdialogVisible: false,
      logisticsOption: [],
      rules: {
        phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        companyCode: [
          { required: true, message: '请选择物流公司', trigger: 'blur' }
        ],
        num: [{ required: true, message: '请填写物流单号', trigger: 'blur' }]
      }
    }
  },
  created() {
    console.log(this.cardUrl, 'cardUrl')
    this.handleFilter()
    this.getLogistics()
  },
  methods: {
    getList() {
      inspectList(this.listQuery).then(response => {
        console.log(response, 'response')
        this.list = response
      })
    },
    getLogistics() {
      api.logistics().then(response => {
        this.logisticsOption = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handletest(row) {
      this.dialogTitle = row.title
      this.formData.invokeUrl = row.invokeUrl
      this.formData.id = row.id
      if (row.id == 4 || row.id == 5 || row.id == 6 || row.id == 11 || row.id == 13) {
        this.dialogVisible = true
      } else {
        this.detection(this.formData)
      }
    },
    handleSubmit(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          this.detection(this.formData)
        } else {
          this.$message.error('请填写检测信息')
        }
      })
    },
    handleSendCode(dataForm) {
      this.$refs[dataForm].validate(async valid => {
        if (valid) {
          if (this.sms.countDown > 0) {
            return false
          }
          const res = await systemCheck(this.formData)
          if (res.code == 0) {
            this.sms.countDown = 60
            this.sms.message = `${this.sms.countDown}秒`
            this.sms.intervalId = setInterval(() => {
              --this.sms.countDown
              this.sms.message =
                this.sms.countDown === 0 ? '发送' : `${this.sms.countDown}秒`
              if (this.sms.countDown === 0) {
                clearInterval(this.sms.intervalId)
              }
            }, 1000)
          } else {
            this.responseData = res.data
            this.successdialogVisible = true
            this.$message({
              type: 'error',
              message: '检测失败'
            })
          }
        } else {
          this.$message.error('请填写检测信息')
        }
      })
    },
    handleOcr() {
      inspectOcr({
        fileUrl: this.cardUrl,
        type: 0
      }).then(response => {
        if (response.code == 0) {
          this.$message({
            type: 'success',
            message: '检测成功'
          })
        } else {
          this.responseData = response.data
          this.successdialogVisible = true
          this.$message({
            type: 'error',
            message: '检测失败'
          })
        }
      })
    },
    handleSuccess() {
      inspectSuccess(this.formData.id).then(response => {
        this.dialogVisible = false
        this.responseData = response
        this.dialogVisible = true
        this.$message({
          type: 'success',
          message: '检测成功'
        })
        this.getList()
      })
    },
    detection(params) {
      systemCheck(params).then(response => {
        console.log(response, 'response')
        if (response.code == 0) {
          this.$message({
            type: 'success',
            message: '检测成功'
          })
          this.getList()
        } else {
          this.responseData = response.data
          this.successdialogVisible = true
          this.$message({
            type: 'error',
            message: '检测失败'
          })
        }
      })
    },
    goUrl(url) {
      window.open(url)
    }
  }
}
</script>

<style lang="scss" scoped>
.mgt10 {
  margin-top: 10px;
}
.color-primary {
  color: #1990ff;
}
</style>
