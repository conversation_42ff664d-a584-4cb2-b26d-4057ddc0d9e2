<template>
  <div class="app-container">
    <div class="filter-container">
      <el-select
        v-model="listQuery.status"
        clearable
        style="width: 150px"
        placeholder="启用状态"
        @change="handleFilter"
      >
        <el-option
          v-for="item in appStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button
        type="primary"
        @click="handleAdd"
      >添加
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="ID"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column
        label="应用类型"
        prop="name"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag size="small">{{ row.appType == 0 ? '医生':'药师' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="平台"
        prop="name"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag size="small">{{ row.appPlat == 0 ? 'Android':'IOS' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="最新版本号"
        prop="lastVerNum"
        width="150"
        align="center"
      />
      <el-table-column
        label="最新版本大小"
        prop="lastVerSize"
        width="250"
        align="center"
      >
        <template slot-scope="{row}">
          <span>{{ row.lastVerSize }}MB</span>
        </template>
      </el-table-column>
      <el-table-column
        label="更新方式"
        prop="busRoute"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag size="small">{{ row.result == 1 ? '建议':'强更' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="最低支持版本号"
        prop="forceVersion"
        width="150"
        align="center"
      />
      <el-table-column
        label="提示内容"
        prop="content"
        width="150"
        align="center"
      />
      <el-table-column
        label="更新地址"
        prop="url"
        width="300"
        align="center"
      />
      <el-table-column
        label="操作"
        width="200"
        align="center"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button
            v-if="row.status == 0"
            type="primary"
            size="mini"
            @click="handleEnable(row.id)"
          >启用</el-button>
          <el-button
            v-else
            size="mini"
            type="danger"
            @click="handleDisabled(row.id)"
          >停用</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(row)"
          >编辑信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="appInfo"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item
              label="地址"
              prop="url"
            >
              <el-input v-model="appInfo.url" type="textarea" autosize style="width: 350px" />
              <el-upload
                style="display: inline-block;margin-left: 10px;"
                :action="uploadPath"
                :headers="headers"
                :on-success="handleExcelSuccess"
                :on-error="handleExcelError"
                :on-progress="handleUploadProgress"
                :file-list="fileList"
                :limit="1"
                :show-file-list="false"
                accept=".apk"
              >
                <el-button
                  :loading="uploading"
                  :disabled="uploading"
                  type="primary"
                >上传</el-button>
                <div
                  slot="tip"
                  class="el-upload__tip"
                ></div>
              </el-upload>
              <el-progress
                v-if="progressFlag"
                :percentage="loadProgress"
              />
            </el-form-item>

          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="平台"
              prop="appPlat"
            >
              <el-select
                v-model="appInfo.appPlat"
                clearable
                style="width: 200px"
                placeholder="请选择平台"
                @change="handleFilter"
              >
                <el-option
                  v-for="item in appPlat"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="更新方式"
              prop="result"
            >
              <el-select
                v-model="appInfo.result"
                clearable
                style="width: 200px"
                placeholder="请选择更新方式"
              >
                <el-option
                  v-for="item in result"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>

          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="新版本号"
              prop="lastVerNum"
            >
              <el-input v-model="appInfo.lastVerNum" style="width: 200px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="最低支持版本号"
              prop="forceVersion"
            >
              <el-input v-model="appInfo.forceVersion" style="width: 200px" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item
              label="新版本大小"
              prop="lastVerSize"
            >
              <el-input v-model="appInfo.lastVerSize" placeholder="MB" style="width: 200px" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="应用类型"
              prop="appPlat"
            >
              <el-select
                v-model="appInfo.appType"
                clearable
                style="width: 200px"
                placeholder="请选择平台"
              >
                <el-option
                  v-for="item in appType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item
              label="更新提示内容"
              prop="content"
            >
              <el-input v-model="appInfo.content" type="textarea" autosize style="width:100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col
            :span="24"
            style="text-align: right;"
          >
            <el-button
              v-waves
              type="primary"
              @click="handlesubmit('dataForm')"
            >提交
            </el-button>
            <el-button
              v-waves
              type="primary"
              @click="dialogVisible = false"
            >返回</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  save,
  update,
  enable,
  disabled,
  uploadUrl
} from '@/api/ops/index'
import { getToken, getTokenName } from '@/utils/auth'
import waves from '@/directive/waves'

export default {
  name: 'Versions',
  directives: { waves },
  filters: {},
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '上传',
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      appInfo: {},
      appStatus: [
        {
          value: 0,
          label: '停用'
        },
        {
          value: 1,
          label: '启用'
        }
      ],
      appPlat: [
        {
          value: 0,
          label: 'Android'
        },
        {
          value: 1,
          label: 'IOS'
        }
      ],
      result: [
        {
          value: 1,
          label: '建议'
        },
        {
          value: 2,
          label: '强更'
        }
      ],
      appType: [
        {
          value: 0,
          label: '医生'
        },
        {
          value: 1,
          label: '药师'
        }
      ],
      rules: {
        url: [{ required: true, message: '请选择填写下载地址', trigger: 'blur' }],
        appPlat: [{ required: true, message: '请选择平台', trigger: 'blur' }],
        appType: [{ required: true, message: '请选择应用类型', trigger: 'blur' }],
        lastVerNum: [
          { required: true, message: '请输入最新版本号', trigger: 'blur' }
        ],
        lastVerSize: [
          { required: true, message: '请输入最新版本大小', trigger: 'blur' }
        ],
        forceVersion: [
          { required: true, message: '请输入最低支持版本号', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入更新提示内容', trigger: 'blur' }
        ],
        result: [{ required: true, message: '请选择更新方式', trigger: 'blur' }]
      },
      uploadPath: '',
      headers: {},
      fileList: [],
      uploading: false,
      fileLength: 0, // 一次上传多个文件的数量
      loadProgress: 0, // 进度条值
      progressFlag: false
    }
  },
  created() {
    this.uploadPath = uploadUrl()
    this.headers[getTokenName()] = getToken()
    this.handleFilter()
  },
  methods: {
    getList() {
      getList(this.listQuery).then((response) => {
        console.log(response, 'response')
        this.list = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleAdd() {
      this.dialogTitle = '上传'
      this.dialogVisible = true
      this.resetTemp()
    },
    handleEdit(item) {
      this.appInfo = item
      this.dialogTitle = '编辑'
      this.dialogVisible = true
    },
    handlesubmit(dataForm) {
      this.$refs[dataForm].validate((valid) => {
        if (valid) {
          if (this.appInfo.id) {
            this.update()
          } else {
            this.save()
          }
        } else {
          this.$message.error('请检查表单是否填写完整')
        }
      })
    },
    save() {
      save(this.appInfo).then((res) => {
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    update() {
      update(this.appInfo).then((res) => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    enable(id) {
      enable(id).then((res) => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    disabled(id) {
      disabled(id).then((res) => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    handleEnable(id) {
      this.$confirm('是否启用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.enable(id)
      })
    },
    handleDisabled(id) {
      this.$confirm('是否停用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.disabled(id)
      })
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.$message({
          message: '上传成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.uploading = false
        this.appInfo.url = response.data
      }
    },
    handleUploadProgress(event) {
      this.uploading = true
      this.progressFlag = true
      this.loadProgress = parseInt(event.percent)
      if (this.loadProgress >= 100) {
        this.loadProgress = 100
        setTimeout(() => {
          this.progressFlag = false
          this.uploading = false
          this.loadProgress = 0
        }, 2000)
      }
    },
    resetTemp() {
      this.$nextTick(() => {
        this.appInfo = {}
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>

<style>
</style>
