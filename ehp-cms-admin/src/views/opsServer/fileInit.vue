<template>
  <div class="app-container">
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="ID"
        prop="id"
        width="150"
        align="center"
      />
      <el-table-column
        label="名称"
        prop="name"
        width="180"
        align="center"
      />
      <el-table-column
        label="配置文件地址"
        prop="path"
        width="350"
        align="center"
      />
      <el-table-column
        label="检测状态"
        align="center"
        width="150"
      >
        <template slot-scope="{row}">
          <el-tag v-if="row.checked===1" size="small">已检测</el-tag>
          <el-tag v-else size="small" type="danger">未检测</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="remarks"
        align="center"
      />
      <el-table-column
        label="操作"
        width="100"
        align="center"
        fixed="right"
      >
        <template slot-scope="{row}">
          <el-button
            type="primary"
            size="mini"
            @click="handletest(row)"
          >检测</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import {
  initConfigList,
  initConfigCheck
} from '@/api/ops/index'
import waves from '@/directive/waves'

export default {
  name: 'Versions',
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    getList() {
      initConfigList(this.listQuery).then((response) => {
        console.log(response, 'response')
        this.list = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handletest(row) {
      this.$confirm('确认检测？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        initConfigCheck(row.id).then((response) => {
          this.$message({
            type: 'success',
            message: '检测成功'
          })
          this.getList()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        })
      })
    }
  }
}
</script>

<style>
</style>
