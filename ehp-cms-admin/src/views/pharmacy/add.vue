<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :model="warehouseData"
      :rules="rules"
      label-width="120px"
      class="pharmacy-form"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span class="section-title">基本信息</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商家名称" prop="name">
              <el-input v-model="warehouseData.name" placeholder="请输入商家名称" clearable />
            </el-form-item>
          </el-col>
          <el-col v-if="!warehouseId" :span="8">
            <el-form-item label="密码" prop="password">
              <el-input v-model="warehouseData.password" placeholder="请输入密码" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商家联系人" prop="linkman">
              <el-input v-model="warehouseData.linkman" placeholder="请输入联系人" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="warehouseData.phone" placeholder="请输入联系电话" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系邮箱" prop="email">
              <el-input v-model="warehouseData.email" placeholder="请输入邮箱地址" type="email" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商家地址" prop="cityId">
              <el-cascader
                v-model="warehouseData.cityId"
                :options="cityData"
                :props="props"
                placeholder="请选择地区"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="详细地址" prop="address">
              <el-input v-model="warehouseData.address" placeholder="请输入详细地址" clearable />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注信息" prop="remark">
              <el-input
                v-model="warehouseData.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入备注信息"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 订单分配 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span class="section-title">订单分配</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="工作时间">
              <div class="work-time-container">
                <div class="work-time-controls">
                  <el-checkbox
                    v-model="allWorkDaysSelected"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                  >
                    全选
                  </el-checkbox>
                </div>
                <el-checkbox-group v-model="warehouseData.workDays" @change="handleWorkDaysChange">
                  <el-checkbox :label="1">周一</el-checkbox>
                  <el-checkbox :label="2">周二</el-checkbox>
                  <el-checkbox :label="3">周三</el-checkbox>
                  <el-checkbox :label="4">周四</el-checkbox>
                  <el-checkbox :label="5">周五</el-checkbox>
                  <el-checkbox :label="6">周六</el-checkbox>
                  <el-checkbox :label="7">周日</el-checkbox>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="sort">
              <span slot="label">
                排序号
                <el-tooltip content="多个药店都匹配订单需求时，订单先分配给优先级高的药店数字越小，优先级越高" placement="top">
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-input-number v-model="warehouseData.sort" :min="-999" :max="999" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item prop="workAreaCodes">
              <span slot="label">
                关联地区
                <el-tooltip placement="top">
                  <div slot="content">
                    按地区分配订单，若用户下单的收货地址与药店关联地区相匹配，订单即可分配至药店<br />
                    例:药店A只关联了河北省、北京市则用户收货地址为河北省或北京市的订单，均可分配给药店A，其他地区不可分配给药店A
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>
              <el-cascader
                v-model="warehouseData.workAreaCodes"
                :options="relatedAreasData"
                :props="relatedAreaProps"
                placeholder="请选择关联地区"
                clearable
                collapse-tags
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 邮费售后 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span class="section-title">邮费售后</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="邮费类型" prop="freightConfig.freightType">
              <el-radio-group v-model="warehouseData.freightConfig.freightType">
                <el-radio :label="0">包邮</el-radio>
                <el-radio :label="1">统一邮费</el-radio>
                <el-radio :label="2">满额包邮</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="warehouseData.freightConfig.freightType === 1 || warehouseData.freightConfig.freightType === 2" :gutter="20">
          <el-col :span="8">
            <el-form-item label="邮费(元)" prop="freightConfig.freight">
              <el-input v-model="warehouseData.freightConfig.freight" placeholder="请输入邮费" />
            </el-form-item>
          </el-col>
          <el-col v-if="warehouseData.freightConfig.freightType === 2" :span="8">
            <el-form-item label="订单满额(元)" prop="freightConfig.freeFreightThreshold">
              <el-input v-model="warehouseData.freightConfig.freeFreightThreshold" placeholder="满额包邮金额" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 药品类型 -->
      <el-card class="form-section" shadow="never">
        <div slot="header" class="section-header">
          <span class="section-title">药品类型</span>
        </div>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="支持药品类型" prop="config">
              <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox0">
                <el-checkbox :label="1">中药</el-checkbox>
                <el-checkbox :label="3">西药</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 中药相关配置 -->
        <div v-if="warehouseData.config.indexOf(1) > -1" class="medicine-config">
          <el-divider content-position="left">中药配置</el-divider>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="支持中药剂型">
                <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox1">
                  <el-checkbox :label="9">内服中药</el-checkbox>
                  <el-checkbox :label="10">滋补膏方</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 代煎服务 -->
          <el-row v-show="warehouseData.config.indexOf(9) > -1" :gutter="20">
            <el-col :span="8">
              <el-form-item label="是否代煎">
                <el-checkbox-group v-model="warehouseData.config" @change="changeCheckBox2">
                  <el-checkbox ref="childCheckBox" :label="8">提供代煎服务</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col v-show="warehouseData.config.indexOf(8) > -1" :span="8">
              <el-form-item label="代煎费(元)" prop="processFee">
                <el-input
                  v-model.number="warehouseData.processFee"
                  placeholder="请输入代煎费"
                  onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 膏方制作 -->
          <div v-if="warehouseData.config.indexOf(10) > -1" class="gao-fang-config">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="膏方制作费(元)" prop="productionFee">
                  <el-input
                    v-model.number="warehouseData.productionFee"
                    placeholder="请输入制作费"
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="制作周期(天)" prop="productionCycle">
                  <el-input
                    v-model.number="warehouseData.productionCycle"
                    placeholder="请输入制作周期"
                    onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 膏方辅料 -->
            <div class="accessories-section">
              <el-divider content-position="left">膏方辅料</el-divider>
              <el-row v-for="(item, index) in Accessories" :key="index" :gutter="20">
                <el-col :span="24">
                  <el-form-item :label="item.name">
                    <el-checkbox-group v-model="warehouseData.accessoriesIds">
                      <el-checkbox v-for="(accessory, idx) in item.accessories" :key="idx" :label="accessory.id">
                        {{ accessory.name }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </el-card>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions fixed-bottom">
      <el-button type="primary" @click="savePartner('dataForm')">保存</el-button>
      <el-button type="primary" @click="savePartner('dataForm', 'product')">保存并维护商品信息</el-button>
      <el-button @click="canelBack">取消</el-button>
    </div>
  </div>
</template>
<script>
import api_pharmacy from '@/api/pharmacy/index'
export default {
  name: '',
  data() {
    return {
      warehouseId: null,
      warehouseData: {
        config: [4],
        accessoriesIds: [],
        address: '',
        processFee: '',
        productionFee: '',
        productionCycle: '',
        freight: '',
        sort: null,
        workAreaCodes: [],
        workDays: [1, 2, 3, 4, 5, 6, 7],
        freightConfig: {
          freightType: null,
          freight: null,
          freeFreightThreshold: null
        }
      },
      merchantOptions: [],
      Accessories: [],
      cityData: null,
      relatedAreasData: null,
      allWorkDaysSelected: true,
      isIndeterminate: false,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      relatedAreaProps: {
        value: 'code',
        emitPath: false,
        label: 'name',
        multiple: true
      },
      rules: {
        name: [{ required: true, message: '请输入商家名称', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
        linkman: [{ required: true, message: '请输入商家联系人', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        cityId: [{ required: true, message: '请选择商家地址', trigger: 'blur' }],
        address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        config: [{ required: true, message: '请选择支持药品类型', trigger: 'blur' }],
        productionFee: [{ required: true, message: '请输入膏方制作费', trigger: 'blur' }],
        productionCycle: [{ required: true, message: '请输入膏方制作周期', trigger: 'blur' }],
        'freightConfig.freightType': [{ required: true, message: '请选择邮费类型', trigger: 'change' }],
        'freightConfig.freight': [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              const regex = /^\d+(\.\d{1,2})?$/
              if (value.toString().trim() === '') {
                callback(new Error('请输入邮费'))
              } else if (!regex.test(value) || value <= 0) {
                callback(new Error('请输入大于0的整数或小数(保留两位小数)'))
              } else {
                callback()
              }
            }
          }
        ],
        'freightConfig.freeFreightThreshold': [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              const regex = /^\d+(\.\d{1,2})?$/
              if (value.toString().trim() === '') {
                callback(new Error('请输入订单金额'))
              } else if (!regex.test(value) || value <= 0) {
                callback(new Error('请输入大于0的整数或小数(保留两位小数)'))
              } else {
                callback()
              }
            }
          }
        ]
      }
    }
  },
  created() {
    this.getCityList()
    this.getRelatedAreasList()
    if (this.$route.params.warehouseId) {
      this.warehouseId = this.$route.params.warehouseId
      this.getPartnerDetail(this.$route.params.warehouseId)
    }
    if (this.$route.query.sort) {
      this.warehouseData.sort = this.$route.query.sort
    }
    this.getAccessories()
  },
  methods: {
    canelBack() {
      this.$router.push({ path: '/pharmacy/list' })
      this.$store.state.tagsView.visitedViews.pop()
      console.log(this.$store.state.tagsView.visitedViews, 113)
    },
    getCityList() {
      api_pharmacy.citylist().then((response) => {
        this.cityData = response
      })
    },
    getRelatedAreasList() {
      api_pharmacy.citylist({ type: 1 }).then((response) => {
        this.relatedAreasData = response
      })
    },
    getPartnerDetail(id) {
      api_pharmacy.warehouseDetail(id).then((response) => {
        response.config = [...new Set([...response.config, ...this.warehouseData.config])]
        response.freightConfig = response.freightConfig
          ? response.freightConfig
          : { freightType: null, freight: null, freeFreightThreshold: null }

        // 处理工作时间数据
        if (response.workDays && Array.isArray(response.workDays)) {
          // 保持原有的工作时间数据
        } else {
          response.workDays = [1, 2, 3, 4, 5, 6, 7] // 默认全选
        }

        this.warehouseData = response
        this.$nextTick(() => {
          this.updateWorkDaysStatus()
        })
      })
    },
    getAccessories() {
      const params = {}
      params.warehouseId = this.warehouseId
      api_pharmacy.getAccessories(params).then((response) => {
        console.log(response)
        this.Accessories = response
      })
    },
    changeCheckBox0() {
      if (this.warehouseData.config.indexOf(1) === -1) {
        this.warehouseData.processFee = ''
        this.warehouseData.productionFee = ''
        this.warehouseData.productionCycle = ''
        delete this.warehouseData.processFee
        delete this.warehouseData.productionFee
        delete this.warehouseData.productionCycle
        this.removeByValue(this.warehouseData.config, 8)
        this.removeByValue(this.warehouseData.config, 9)
        this.removeByValue(this.warehouseData.config, 10)
      }
    },
    changeCheckBox1() {
      if (this.warehouseData.config.indexOf(9) === -1) {
        this.warehouseData.config.includes(8) && this.$refs.childCheckBox.$el.click()
      }
    },
    changeCheckBox2() {
      // 代煎服务逻辑
    },
    changeCheckBox3() {
      if (this.warehouseData.config.indexOf(9) === -1) {
        this.warehouseData.config.includes(4) && this.$refs.childCheckBox.$el.click()
      }
    },
    changeFree() {
      if (this.warehouseData.freightConfig.freeFreight === 1) {
        this.warehouseData.freightConfig.freight = 0
        this.warehouseData.freightConfig.freeFreightThreshold = null
        this.warehouseData.freightConfig.freightType = null
        delete this.warehouseData.freightConfig.freight
        delete this.warehouseData.freightConfig.freeFreightThreshold
        delete this.warehouseData.freightConfig.freightType
      }
    },
    removeByValue(arr, val) {
      for (var i = 0; i < arr.length; i++) {
        // eslint-disable-next-line eqeqeq
        if (arr[i] == val) {
          arr.splice(i, 1)
          break
        }
      }
    },
    savePartner(dataForm, product) {
      if (this.warehouseData.config.indexOf(8) === -1) {
        delete this.warehouseData.processFee
      }
      if (this.warehouseData.config.indexOf(10) === -1) {
        delete this.warehouseData.productionFee
        delete this.warehouseData.productionCycle
      }
      console.log(this.warehouseData)
      this.$refs[dataForm].validate((valid) => {
        if (this.warehouseData.config.indexOf(1) === -1 && this.warehouseData.config.indexOf(3) === -1) {
          this.$message.error('请选择药品类型！')
          return
        }
        if (
          this.warehouseData.config.indexOf(1) !== -1 &&
          this.warehouseData.config.indexOf(9) === -1 &&
          this.warehouseData.config.indexOf(10) === -1
        ) {
          this.$message.error('请选择中药剂型！')
          return
        }
        if (
          this.warehouseData.config.indexOf(10) !== -1 &&
          (this.warehouseData.productionFee === '' || this.warehouseData.productionCycle === '')
        ) {
          this.$message.error('请填写制作搞方费和制作周期！')
          return
        }
        if (
          this.warehouseData.config.indexOf(8) !== -1 &&
          (this.warehouseData.processFee === undefined || this.warehouseData.processFee === '')
        ) {
          this.$message.error('代煎费不能为空！')
          return
        }
        const params = Object.assign({}, this.warehouseData)
        if (this.warehouseData.config.indexOf(10) === -1) {
          delete params.productionFee
          delete params.productionCycle
        }
        if (valid) {
          console.log(this.warehouseData, 'this.warehouseData')
          if (this.warehouseData.id) {
            api_pharmacy.update(this.warehouseData).then((response) => {
              this.$message({
                message: '更新成功',
                type: 'success'
              })
              if (product) {
                const query = { active: 'second' }
                this.$router.push({
                  path: '/pharmacy/product/' + this.warehouseData.id,
                  query
                })
              } else {
                var x = document.querySelector('.el-scrollbar__view .router-link-active .el-icon-close')
                x.click()
                this.$router.push({
                  path: '/pharmacy/list'
                })
              }
            })
          } else {
            api_pharmacy.save(this.warehouseData).then((response) => {
              this.$message({
                message: '保存成功',
                type: 'success'
              })
              if (product) {
                this.$router.push({
                  path: '/pharmacy/product/' + response
                })
              } else {
                var x = document.querySelector('.el-scrollbar__view .router-link-active .el-icon-close')
                x.click()
                this.$router.push({
                  path: '/pharmacy/list'
                })
              }
            })
          }
        }
      })
    },
    handleCheckAllChange(val) {
      this.warehouseData.workDays = val ? [1, 2, 3, 4, 5, 6, 7] : []
      this.isIndeterminate = false
    },
    handleWorkDaysChange(value) {
      const checkedCount = value.length
      this.allWorkDaysSelected = checkedCount === 7
      this.isIndeterminate = checkedCount > 0 && checkedCount < 7
    },
    updateWorkDaysStatus() {
      const checkedCount = this.warehouseData.workDays.length
      this.allWorkDaysSelected = checkedCount === 7
      this.isIndeterminate = checkedCount > 0 && checkedCount < 7
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  padding-bottom: 100px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.pharmacy-form {
  .form-section {
    margin-bottom: 20px;
    border-radius: 8px;
    background-color: #fff;

    .section-header {
      padding: 0;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: -12px;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background-color: #409eff;
          border-radius: 2px;
        }
      }
    }

    .el-card__body {
      padding: 20px 24px;
    }

    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        color: #606266;
        font-weight: 500;
      }

      .el-input, .el-select, .el-cascader {
        width: 100%;
      }

      .el-textarea {
        .el-textarea__inner {
          border-radius: 4px;
        }
      }
    }

    .el-checkbox-group {
      .el-checkbox {
        margin-right: 20px;
        margin-bottom: 8px;

        .el-checkbox__label {
          color: #606266;
          font-weight: normal;
        }
      }
    }

    .el-radio-group {
      .el-radio {
        margin-right: 20px;

        .el-radio__label {
          color: #606266;
          font-weight: normal;
        }
      }
    }
  }

  .medicine-config {
    margin-top: 20px;

    .el-divider {
      margin: 20px 0;

      .el-divider__text {
        background-color: #fff;
        color: #409eff;
        font-weight: 500;
      }
    }

    .gao-fang-config {
      background-color: #f8f9ff;
      padding: 16px;
      border-radius: 6px;
      margin-top: 12px;
      border-left: 4px solid #409eff;
    }

    .accessories-section {
      background-color: #fafbfc;
      padding: 16px;
      border-radius: 6px;
      margin-top: 12px;
      border: 1px solid #e4e7ed;
    }
  }
}

.form-actions {
  text-align: center;
  padding: 24px 0;
  background-color: #fff;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  &.fixed-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    margin-top: 0;
    border-radius: 0;
    border-top: 1px solid #e4e7ed;
  }

  .el-button {
    margin: 0 8px;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 500;

    &.el-button--primary {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      }
    }

    &:not(.el-button--primary) {
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

@media (max-width: 1200px) {
  .pharmacy-form {
    .el-col {
      &[class*="span-8"] {
        width: 50% !important;
      }

      &[class*="span-16"] {
        width: 100% !important;
      }
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 12px;
    padding-bottom: 80px;
  }

  .pharmacy-form {
    .el-col {
      width: 100% !important;
    }

    .form-section {
      .el-card__body {
        padding: 16px;
      }
    }
  }

  .form-actions {
    padding: 16px 0;

    .el-button {
      margin: 4px;
      width: calc(50% - 8px);

      &:last-child {
        width: 100%;
        margin-top: 8px;
      }
    }
  }
}

::v-deep .el-form-item {
  &.is-error {
    .el-input__inner,
    .el-textarea__inner,
    .el-select .el-input__inner,
    .el-cascader .el-input__inner {
      border-color: #f56c6c;
      box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
    }
  }

  .el-form-item__error {
    font-size: 12px;
    color: #f56c6c;
    padding-top: 4px;
  }
}

.form-section {
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

// 问号提示图标样式
.el-icon-question {
  margin-left: 4px;
  color: #909399;
  cursor: help;

  &:hover {
    color: #409eff;
  }
}

.work-time-container {
  .work-time-controls {
    margin-bottom: 12px;
  }

  .el-checkbox-group {
    .el-checkbox {
      margin-right: 20px;
      margin-bottom: 8px;
    }
  }
}
</style>
