<template>
  <div class="app-container">
    <!-- 商家信息头部 -->
    <div class="merchant-header">
      <div class="header-content">
        <div class="merchant-info">
          <div class="info-item">
            <span class="label">商家名称：</span>
            <span class="value">{{ warehouseDetail.name }}</span>
          </div>
          <div class="info-item">
            <span class="label">商家编码：</span>
            <span class="value">{{ warehouseDetail.code }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品查询与添加区域 -->
    <el-card class="section-card" shadow="hover">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-search header-icon"></i>
          <span class="header-title">商品查询与添加</span>
          <span class="header-subtitle">搜索并选择商品设置为合作商品</span>
        </div>
      </div>

      <!-- 查询表单 -->
      <div class="search-section">
        <el-form ref="dataForm" :model="goodsQuery" class="search-form" :inline="true">
          <el-form-item label="商品代码">
            <el-input
              v-model="goodsQuery.number"
              placeholder="请输入商品代码"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input
              v-model="goodsQuery.name"
              placeholder="请输入商品名称"
              clearable
              style="width: 200px"
              @keyup.enter.native="handleFilter"
            />
          </el-form-item>
          <el-form-item label="商品分类">
            <el-cascader
              v-model="goodsQuery.pharmacologyClassificationId"
              :options="pharmacologyData"
              :props="props"
              :show-all-levels="false"
              placeholder="请选择分类"
              clearable
              style="width: 200px"
              @change="handleFilter"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleFilter">查询</el-button>
            <el-button icon="el-icon-refresh" @click="resetGoodsQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 操作提示和按钮 -->
      <div class="operation-bar">
        <div class="operation-info">
          <span class="selection-text">已选择 <span class="selection-count">{{ goodsSelect.length }}</span> 个商品</span>
        </div>
        <div class="operation-buttons">
          <el-button
            v-permission="['pharmacy:partner:set']"
            type="primary"
            icon="el-icon-plus"
            size="medium"
            :disabled="goodsSelect.length === 0"
            @click="setProduct"
          >设置为合作商品 ({{ goodsSelect.length }})</el-button>
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="table-container">
        <el-table
          ref="goodsTable"
          :data="goodsList"
          fit
          highlight-current-row
          :row-style="{ height: '48px' }"
          :header-row-style="{ height: '48px' }"
          :header-cell-style="{ background: '#F8F9FB', color: '#606266', fontWeight: '600' }"
          @selection-change="goodsSelectionChange"
        >
          <el-table-column type="selection" align="center" width="55"></el-table-column>
          <el-table-column label="商品代码" prop="number" width="200" align="center" />
          <el-table-column label="商品名称" prop="name" align="left" show-overflow-tooltip />
          <el-table-column label="商品基础分类" prop="pharmacologyClassificationName" align="left">
            <template slot-scope="scope">
              <span>{{ scope.row.pharmacologyClassificationName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="售卖价" prop="salePrice" width="100" align="center">
            <template slot-scope="scope">
              <span class="price">¥{{ scope.row.salePrice }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="goodstotal > 0"
          :total="goodstotal"
          :auto-scroll="false"
          :page.sync="goodsQuery.pageNo"
          :limit.sync="goodsQuery.pageSize"
          @pagination="getGoodsList"
        />
      </div>
    </el-card>

    <!-- 合作商品管理区域 -->
    <el-card class="section-card" shadow="hover">
      <div slot="header" class="card-header">
        <div class="header-left">
          <i class="el-icon-goods header-icon"></i>
          <span class="header-title">合作商品管理</span>
          <span class="header-subtitle">已设置的合作商品，共 {{ warehousetotal }} 个</span>
        </div>
      </div>

      <!-- 操作提示和按钮 -->
      <div class="operation-bar">
        <div class="operation-info">
          <span class="selection-text">已选择 <span class="selection-count">{{ warehouseSelect.length }}</span> 个商品</span>
        </div>
        <div class="operation-buttons">
          <el-button
            v-permission="['pharmacy:partner:delete']"
            type="danger"
            icon="el-icon-delete"
            size="medium"
            :disabled="warehouseSelect.length === 0"
            @click="delProduct"
          >删除合作商品 ({{ warehouseSelect.length }})</el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table
          ref="warehouseTable"
          :data="warehouseList"
          fit
          highlight-current-row
          :row-style="{ height: '48px' }"
          :header-row-style="{ height: '48px' }"
          :header-cell-style="{ background: '#F8F9FB', color: '#606266', fontWeight: '600' }"
          @selection-change="warehouseSelectionChange"
        >
          <el-table-column type="selection" align="center" width="55"></el-table-column>
          <el-table-column label="商品代码" prop="number" width="200" align="center" />
          <el-table-column label="商品名称" prop="name" align="left" show-overflow-tooltip />
          <el-table-column label="商品基础分类" prop="pharmacologyClassificationName" align="left">
            <template slot-scope="scope">
              <span>{{ scope.row.pharmacologyClassificationName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="售卖价" prop="salePrice" width="100" align="center">
            <template slot-scope="scope">
              <span class="price">¥{{ scope.row.salePrice }}</span>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="warehousetotal > 0"
          :total="warehousetotal"
          :auto-scroll="false"
          :page.sync="warehouseQuery.pageNo"
          :limit.sync="warehouseQuery.pageSize"
          @pagination="getPartnerList"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import api_base from '@/api/product/base'
import api_pharmacy from '@/api/pharmacy/index'
import api_warehouse from '@/api/pharmacy/warehouse'
export default {
  beforeRouteLeave(to, from, next) {
    this.$store.state.tagsView.visitedViews.pop()
    next()
  },

  name: 'PharmacyProduct',

  data() {
    return {
      warehouseDetail: {},
      goodsList: [],
      warehouseList: [],
      goodsSelect: [],
      warehouseSelect: [],
      pharmacologyData: null,
      goodsQuery: {
        pageNo: 1,
        pageSize: 10
      },
      warehouseQuery: {
        pageNo: 1,
        pageSize: 10
      },
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      goodstotal: 0,
      warehousetotal: 0
    }
  },

  created() {
    this.getPharmacologyData()
    this.warehouseId = this.$route.params.warehouseId
    this.goodsQuery.warehouseId = this.$route.params.warehouseId
    this.getPartnerDetail()
    this.getGoodsList()
    this.getPartnerList()
  },

  methods: {
    getPartnerDetail() {
      api_pharmacy.warehouseDetail(this.warehouseId).then(response => {
        this.warehouseDetail = response
      })
    },
    getGoodsList() {
      api_warehouse.goodslist(this.goodsQuery).then(response => {
        this.goodsList = response.list
        this.goodstotal = response.totalCount
      })
    },
    handleFilter() {
      this.goodsQuery.pageNo = 1
      this.getGoodsList()
    },
    resetGoodsQuery() {
      this.goodsQuery.number = ''
      this.goodsQuery.name = ''
      this.goodsQuery.pharmacologyClassificationId = null
      this.handleFilter()
    },
    getPartnerList() {
      api_warehouse
        .getGoodsList(this.warehouseId, this.warehouseQuery)
        .then(response => {
          this.warehouseList = response.list
          this.warehousetotal = response.totalCount
        })
    },
    getPharmacologyData() {
      api_base.pharmacology().then(response => {
        this.pharmacologyData = response
      })
    },
    setProduct() {
      this.$confirm('是否设置为合作商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_warehouse
          .setGoods(this.warehouseId, this.goodsSelect)
          .then(response => {
            this.$message({
              message: '设置成功',
              type: 'success'
            })
            if (
              this.goodsList.length === this.goodsSelect.length &&
                this.goodsQuery.pageNo > 1
            ) {
              this.goodsQuery.pageNo = this.goodsQuery.pageNo - 1
            }
            this.$refs.goodsTable.clearSelection()
            this.getGoodsList()
            this.getPartnerList()
          })
      })
    },
    delProduct() {
      this.$confirm('是否删除合作商品？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        api_warehouse
          .delGoods(this.warehouseId, this.warehouseSelect)
          .then(response => {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            if (
              this.warehouseList.length === this.warehouseSelect.length &&
                this.warehouseQuery.pageNo > 1
            ) {
              this.warehouseQuery.pageNo = this.warehouseQuery.pageNo - 1
            }
            this.$refs.warehouseTable.clearSelection()
            this.getGoodsList()
            this.getPartnerList()
          })
      })
    },
    goodsSelectionChange(value) {
      if (value.length > 0) {
        var selectarr = []
        value.forEach((currentValue, index, arr) => {
          selectarr.push(currentValue.skuId)
        })
        this.goodsSelect = selectarr
      } else {
        this.goodsSelect = []
      }
    },
    warehouseSelectionChange(value) {
      if (value.length > 0) {
        var selectarr = []
        value.forEach((currentValue, index, arr) => {
          selectarr.push(currentValue.skuId)
        })
        this.warehouseSelect = selectarr
      } else {
        this.warehouseSelect = []
      }
    }
  }
}
</script>

<style scoped>
/* 商家信息头部样式 */
.merchant-header {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
}

.header-content {
  padding: 20px 24px;
}

.merchant-info {
  display: flex;
  gap: 40px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #303133;
}

.info-item .label {
  font-weight: 500;
  color: #606266;
}

.info-item .value {
  font-weight: 600;
  color: #303133;
  margin-left: 8px;
}

/* 卡片样式 */
.section-card {
  margin-bottom: 20px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.card-header {
  background: #fafafa;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 20px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 18px;
  color: #409eff;
  margin-right: 8px;
}

.header-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
}

.header-subtitle {
  font-size: 13px;
  color: #909399;
}

/* 搜索区域样式 - 内联表单 */
.search-section {
  padding: 20px;
  background: #fff;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-right: 16px;
  margin-bottom: 0;
}

.search-form .el-form-item__label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

/* 操作栏样式 */
.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
  border-bottom: 1px solid #e4e7ed;
}

.operation-info {
  display: flex;
  align-items: center;
}

.selection-text {
  font-size: 14px;
  color: #606266;
}

.selection-count {
  color: #409eff;
  font-weight: 600;
  margin: 0 2px;
}

.operation-buttons {
  display: flex;
  gap: 12px;
}

.operation-buttons .el-button {
  font-weight: 500;
}

/* 表格容器样式 */

/* 价格样式 */
.price {
  color: #f05542;
  font-weight: 600;
}

/* 分页样式优化 */
.pagination-container {
  background-color: transparent;
  padding: 16px 0 0 0;
  text-align: center;
}

/* 表格行悬浮效果 */
.el-table tbody tr:hover > td {
  background-color: #f5f7fa !important;
}

/* 按钮样式优化 */
.el-button + .el-button {
  margin-left: 0;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-form {
    display: block;
  }

  .search-form .el-form-item {
    display: inline-block;
    margin-bottom: 16px;
    margin-right: 16px;
  }

  .search-form .el-form-item:last-child {
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .merchant-info {
    flex-direction: column;
    gap: 12px;
  }

  .operation-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .operation-buttons {
    justify-content: center;
  }

  .header-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .search-form .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-form .el-form-item .el-input,
  .search-form .el-form-item .el-cascader {
    width: 100% !important;
  }
}
</style>
