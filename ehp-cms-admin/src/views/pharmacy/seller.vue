<template>
  <div class="app-container">
    <el-row :gutter="20" style="font-size:20px;">
      <el-col :span="8" class="fcolor">商家名称：{{ warehouseData.name }}</el-col>
      <el-col :span="8" class="fcolor">商家编码：{{ warehouseData.code }}</el-col>
    </el-row>
    <el-divider></el-divider>
    <div class="fcolor" style="padding-bottom: 20px;">基本信息</div>
    <div class="box">
      <el-row :gutter="20">
        <el-col :span="24" class="fcolor">
          商家名称:{{ warehouseData.name }}
        </el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:20px;padding-bottom:20px;">
        <el-col :span="8" class="fcolor">
          商家联系人:{{ warehouseData.linkman }}
        </el-col>
        <el-col :span="8" class="fcolor">
          联系电话:{{ warehouseData.phone }}
        </el-col>
        <el-col :span="8" class="fcolor">
          联系邮箱:{{ warehouseData.email }}
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24" class="fcolor">
          商家地址:{{ warehouseData.address }}
        </el-col>
      </el-row>
    </div>
    <div class="fcolor" style="padding-bottom: 20px;padding-top: 20px;">合作商品</div>
    <div class="box">
      <div class="fcolor">合作商品 共{{ warehouseData.number }}个</div>
      <el-table style="margin-top:20px;" :data="goodsListData" fit highlight-current-row>
        <el-table-column label="商品代码" prop="number" align="center" />
        <el-table-column
          label="商品名称"
          prop="name"
          align="center"
        />
        <el-table-column
          label="商品基础分类"
          prop="pharmacologyClassificationName"
          align="center"
        />
        <el-table-column label="售卖价" prop="salePrice" align="center" />
      </el-table>
      <pagination
        v-show="total>0"
        style="margin-top:0;"
        :total="total"
        :page.sync="listQuery.pageNo"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<style scoped>
.fcolor{color: #606266;}
.box{/*background-color: #e8f4ff;*/padding:20px;}
.pagination-container /deep/{background-color:transparent;padding-bottom:10px;}
</style>
<script>
import api_pharmacy from '@/api/pharmacy/index'
import api_warehouse from '@/api/pharmacy/warehouse'
export default {
  'name': '商家信息详情',
  data() {
    return {
      warehouseId: null,
      warehouseData: {},
      goodsListData: [],
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0
    }
  },
  created() {
    this.warehouseId = this.$route.params.warehouseId
    this.getPartnerDetail()
    this.getGoodsList()
  },
  methods: {
    getPartnerDetail() {
      api_pharmacy.warehouseDetail(this.warehouseId).then(response => {
        this.warehouseData = response
      })
    },
    getGoodsList() {
      this.getList(this.warehouseId)
    },
    // 获取数据
    getList() {
      api_warehouse.getGoodsList(this.warehouseId, this.listQuery).then(response => {
        this.goodsListData = response.list
        this.total = response.totalCount
      })
    }
  }
}
</script>
