<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.code"
        class="filter-item"
        clearable
        placeholder="商家编码"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        clearable
        class="filter-item"
        placeholder="商家名称"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
      />
      <el-button
        v-permission="['pharmacy:partner:details']"
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-permission="['pharmacy:partner:details']"
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        v-permission="['pharmacy:partner:save']"
        type="primary"
        icon="el-icon-plus"
        @click="addPharmacy"
      >添加</el-button>
    </div>
    <el-table
      style="margin-top:20px;"
      :data="list"
      fit
      highlight-current-row
      :row-style="{ height: '42px' }"
      :header-row-style="{ height: '42px' }"
      :header-cell-style="{
        background: '#F8F9FB'
      }"
    >
      <el-table-column label="商家编码" width="70px" prop="code" align="center" />
      <el-table-column label="商家名称" width="150px" prop="name" align="center">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="warehouseDetail(scope.row.id)"
          >{{ scope.row.name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="商家联系人" prop="linkman" align="center" />
      <el-table-column label="商家状态" prop="statusDescribe" align="center">
        <template slot-scope="{row}">
          <el-tag size="small" :type="row.status === 0 ? '' : 'danger'">{{ row.statusDescribe }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" prop="phone" align="center" />
      <el-table-column label="邮箱" prop="email" align="center" />
      <el-table-column label="西药合作商品数" prop="westernMedicineCount" align="center" />
      <el-table-column label="中药合作商品数" prop="tcmCount" align="center" />
      <el-table-column label="排序" prop="sort" align="center" />
      <el-table-column label="操作" prop="name" align="center" width="290px">
        <template slot-scope="scope">
          <el-button
            v-permission="['pharmacy:partner:update']"
            type="text"
            size="mini"
            @click="editPartner(scope.row.id)"
          >商家信息维护</el-button>
          <el-button
            v-permission="['pharmacy:partner:update']"
            type="text"
            size="mini"
            @click="warehouseProduct(scope.row.id)"
          >合作商品维护</el-button>
          <el-button
            v-if="scope.row.status===1"
            v-permission="['pharmacy:partner:update']"
            type="text"
            size="mini"
            @click="warehouseStatus(scope.row.id,0,scope.$index)"
          >启用</el-button>
          <el-button
            v-else
            v-permission="['pharmacy:partner:update']"
            type="text"
            size="mini"
            @click="warehouseStatus(scope.row.id,1,scope.$index)"
          >停用</el-button>
          <el-button
            v-permission="['pharmacy:warehouse:manage']"
            type="text"
            size="mini"
            @click="adminuser(scope.row.id,scope.$index,scope.row.manage && scope.row.manage.userName ? scope.row.manage.userName : null,scope.row.phone)"
          >账号管理</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      style="margin-top:0;"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog title="账号管理" :visible.sync="dialogRepass" :before-close="closeRepss" width="450px">
      <el-form ref="passForm" :model="adminUser" :rules="rules" label-width="100px">
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="adminUser.userName"
            :disabled="list.length>0 && list[adminIndex].manage && list[adminIndex].manage.userName ? true :false"
            placeholder="请输入用户名"
          />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input
            v-model="adminUser.password"
            autocomplete="new-password"
            show-password
            placeholder="请输入密码"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeRepss">取 消</el-button>
        <el-button type="primary" @click="sureReset('passForm')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.fcolor {
  color: #606266;
}
.box {
  background-color: #e8f4ff;
  padding: 20px;
}
.pagination-container /deep/ {
  background-color: transparent;
  padding-bottom: 10px;
}
</style>
<script>
import api_pharmacy from '@/api/pharmacy/index'
export default {
  name: '',
  data() {
    return {
      dialogRepass: false,
      adminId: null,
      adminIndex: 0,
      adminUser: {},
      rules: {
        userName: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      },
      list: [],
      total: 0,
      statusId: null, //修改状态id
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        code: '',
        name: ''
      },
      nextSort: null
    }
  },
  watch: {
    //监听相同路由下参数变化的时候，从而实现异步刷新
    $route(to, from) {
      //重新获取数据
      // if (from.path === '/pharmacy/add') {
      if (from.path.indexOf('/pharmacy/add') !== -1) {
        this.handleFilter()
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    addPharmacy() {
      this.$router.push({
        path: './add',
        query: { sort: this.nextSort }
      })
    },
    editPartner(id) {
      this.$router.push({
        path: './add/' + id
      })
    },
    warehouseProduct(id) {
      this.$router.push({
        path: './product/' + id
      })
    },
    warehouseDetail(id) {
      this.$router.push({
        path: './seller/' + id
      })
    },
    //停用启用弹出层
    warehouseStatus(id, status, index) {
      if (status === 0) {
        this.$confirm('是否启用商家？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, 0, index)
        })
      } else {
        this.$confirm('是否停用商家？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sureStatus(id, 1, index)
        })
      }
    },
    //弹出层确认事件
    sureStatus(id, status, index) {
      api_pharmacy.warehouseStatus(id, status).then(response => {
        this.list[index].status = status
        this.getList()
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.code = ''
      this.listQuery.name = ''
      this.handleFilter()
    },
    // 获取数据
    getList() {
      api_pharmacy.list(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
        this.nextSort = response.nextSort
      })
    },
    adminuser(id, index, user, phone) {
      this.adminId = id
      this.adminIndex = index
      this.adminUser = {}
      if (user !== null) {
        this.adminUser.userName = user
      }
      this.adminUser.phone = phone
      this.dialogRepass = true
    },
    sureReset(passForm) {
      this.$refs[passForm].validate(valid => {
        if (valid) {
          this.dialogRepass = false
          api_pharmacy
            .setManage(this.adminId, this.adminUser)
            .then(response => {
              this.$message({
                message: '更新成功',
                type: 'success'
              })
            })
        }
      })
    },
    closeRepss() {
      this.dialogRepass = false
      this.$refs['passForm'].clearValidate()
    }
  }
}
</script>
