<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        class="filter-item"
        clearable
        placeholder="渠道ID"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-input
        v-model="listQuery.channelName"
        class="filter-item"
        maxlength="10"
        clearable
        placeholder="渠道名称"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-input
        v-model="listQuery.channelManagerName"
        class="filter-item"
        clearable
        placeholder="渠道经理"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-input
        v-model="listQuery.channelManagerPhone"
        class="filter-item"
        clearable
        placeholder="经理电话"
        style="width: 150px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button
        v-permission="permissionCode.add"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >添加</el-button>
    </div>
    <el-table :data="list" fit highlight-current-row style="width: 100%;">
      <el-table-column label="渠道ID" prop="id" align="center" />
      <el-table-column label="渠道名称" prop="channelName" align="center" />
      <el-table-column label="渠道经理" prop="channelManagerName" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.channelManagerName || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="经理电话" prop="channelManagerPhone" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.channelManagerPhone || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="150">
        <template slot-scope="{ row }">
          <el-button
            v-permission="permissionCode.edit"
            type="primary"
            @click="handleDetail(row, 'update')"
          >编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      top="2vh"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="渠道名称:" prop="channelName">
          <el-input
            v-model="form.channelName"
            :disabled="dialogStatus === 'detail'"
            maxlength="10"
            placeholder="请输入渠道名称"
            class="inputW"
          />
        </el-form-item>
        <el-form-item v-if="dialogStatus !== 'create'" label="渠道经理:">
          <el-select
            v-model="searchTxt"
            filterable
            remote
            :disabled="dialogStatus === 'detail'"
            reserve-keyword
            placeholder="请输入渠道姓名、id、手机号进行搜素"
            :remote-method="remoteMethod"
            :loading="loading"
            value-key="id"
            class="inputW"
            clearable
            @change="changeSelect"
            @clear="clearSelect"
          >
            <el-option v-for="item in managerOptions" :key="item.id" :label="item.name" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="dialogStatus !== 'create'">
          <div class="sameText">ID: {{ form.channelManagerId }}</div>
          <div class="sameText">姓名: {{ form.channelManagerName }}</div>
          <div class="sameText">手机号: {{ form.channelManagerPhone }}</div>
          <div class="sameText">身份证: {{ form.channelManagerCardNo }}</div>
        </el-form-item>
      </el-form>
      <div v-if="dialogStatus !== 'detail'" slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="updateData()">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getChannelList, addChannel, editChannel, getSalesmanList } from '@/api/channel'
import waves from '@/directive/waves'

export default {
  name: 'ChannelManagement',
  directives: { waves },
  components: {},
  filters: {},

  data() {
    return {
      permissionCode: {
        add: ['agent:area:save'],
        edit: ['agent:area:save'],
        see: ['agent:area:get']
      },
      props: {
        multiple: true,
        value: 'id',
        label: 'name',
        children: 'children'
      },
      list: [],
      rules: {
        channelName: [{ required: true, message: '请输入渠道名称', trigger: 'blur' }]
      },
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        detail: '详情',
        update: '编辑',
        create: '新增'
      },
      form: {
        id: '',
        channelName: '',
        channelManagerId: '',
        channelManagerName: '',
        channelManagerPhone: '',
        channelManagerCardNo: ''
      },
      searchTxt: '',
      managerOptions: [],
      loading: false
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      getChannelList(this.listQuery).then((response) => {
        if (response) {
          this.list = response.list || []
          this.total = response.totalCount
        }
      })
    },
    // 搜索
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.form = {
          id: '',
          channelName: '',
          channelManagerId: '',
          channelManagerName: '',
          channelManagerPhone: '',
          channelManagerCardNo: ''
        }
        this.searchTxt = ''
        this.managerOptions = []
        this.$refs['form'].resetFields()
        this.$refs['form'].clearValidate()
      })
    },
    // 新增
    handleAdd() {
      this.dialogStatus = 'create'
      this.dialogFormVisible = true
      this.resetTemp()
    },
    updateData() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          // eslint-disable-next-line no-self-assign
          this.dialogStatus === 'create' ? (this.form.id = undefined) : (this.form.id = this.form.id)
          const API = this.dialogStatus === 'create' ? addChannel : editChannel
          API(this.form).then(() => {
            this.dialogFormVisible = false
            if (this.dialogStatus === 'create') {
              this.handleFilter()
            } else {
              this.getList()
            }
            this.$message.success('保存成功！')
          })
        }
      })
    },
    // 查询详情
    findAreaDetailsFun(row) {
      console.log(row)
      this.form.id = row.id
      this.form.channelManagerId = row.channelManagerId
      this.form.channelManagerName = row.channelManagerName
      this.form.channelManagerPhone = row.channelManagerPhone
      this.form.channelName = row.channelName
      this.form.channelManagerCardNo = row.channelManagerCardNo
      this.searchTxt = row.channelManagerName
    },
    // 编辑 查看
    handleDetail(row, type) {
      this.dialogStatus = type
      this.dialogFormVisible = true
      this.resetTemp()
      setTimeout(() => {
        this.findAreaDetailsFun(row)
      }, 100)
    },
    handleChange(value) {},
    changeSelect(value) {
      console.log(value)
      this.form.channelManagerId = value.id
      this.form.channelManagerName = value.name
      this.form.channelManagerPhone = value.phone
      this.form.channelManagerCardNo = value.channelManagerCardNo
    },
    clearSelect() {
      this.form.channelManagerId = ''
      this.form.channelManagerName = ''
      this.form.channelManagerPhone = ''
      this.form.channelManagerCardNo = ''
    },
    // 渠道经理搜索
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        getSalesmanList({
          keyword: query,
          channelId: this.form.id
        }).then((res) => {
          console.log(res)
          this.managerOptions = res || []
          this.loading = false
        })
      } else {
        this.managerOptions = []
      }
    }
  }
}
</script>

<style scoped>
.inputW {
  width: 250px;
}
.sameText {
  margin-bottom: 15px;
}
</style>
