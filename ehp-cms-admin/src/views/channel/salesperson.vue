<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        placeholder="ID"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-input
        v-model="listQuery.name"
        placeholder="业务员姓名"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-input
        v-model="listQuery.phone"
        placeholder="业务员手机号"
        clearable
        class="filter-item"
        style="width: 120px;"
        @keyup.enter.native="handleFilter"
        @clear="handleFilter"
      />
      <el-select
        v-model="listQuery.channelId"
        clearable
        style="width: 150px;"
        placeholder="所属渠道"
        @change="handleFilter"
      >
        <el-option v-for="item in channelList" :key="item.id" :label="item.channelName" :value="item.id">
        </el-option>
      </el-select>
      <!-- 所属团队 -->
      <el-select
        v-model="listQuery.teamId"
        clearable
        style="width: 150px;"
        placeholder="所属团队"
        @change="handleFilter"
      >
        <el-option v-for="item in teamOptions" :key="item.id" :label="item.teamName" :value="item.id">
        </el-option>
      </el-select>
      <!-- 职位 -->
      <el-select
        v-model="listQuery.post"
        clearable
        style="width: 150px;"
        placeholder="职位"
        @change="handleFilter"
      >
        <el-option v-for="item in postOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-button type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-upload
        style="display: inline-block;"
        :action="uploadPath"
        :headers="headers"
        :before-upload="handleBeforeUpload"
        :on-success="handleExcelSuccess"
        :on-error="handleExcelError"
        :on-remove="handleRemoveExcel"
        :file-list="fileList"
        accept=".zip"
        :limit="1"
        :show-file-list="false"
      >
        <el-button v-permission="['agent:import']" type="primary">导入</el-button>
        <div slot="tip" class="el-upload__tip"></div>
      </el-upload>
      <el-link v-permission="['agent:import']" :href="tempPath" type="info" target="_blank">
        <el-button type="primary">下载导入业务员模板</el-button>
      </el-link>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row @selection-change="onSelectionChange">
      <el-table-column label="ID" prop="id" align="center" width="80px" />
      <el-table-column label="业务员姓名" prop="name" width="120px" align="center">
        <template slot-scope="{ row }">
          <ChannelDetail :id="row.id" :ref="row.id" :name="row.name" :handle-filter="getList" />
        </template>
      </el-table-column>
      <el-table-column label="经纪人手机号" prop="phone" align="center" width="120px" />
      <el-table-column label="职位" prop="postDescribe" width="120px" align="center" />
      <el-table-column label="所属渠道" prop="areaName" min-width="200px" align="center">
        <template slot-scope="{ row }">
          {{ row.channelName || ' - ' }}
        </template>
      </el-table-column>
      <el-table-column label="管辖团队" prop="leaderName" align="center" width="120px">
        <template slot-scope="{ row }">
          {{ row.governingTeamList && row.governingTeamList.length ? row.governingTeamList.join(',') : ' - ' }}
        </template>
      </el-table-column>
      <el-table-column label="所属团队" prop="leaderName" align="center" width="120px">
        <template slot-scope="{ row }">
          {{ row.teamList && row.teamList.length ? row.teamList.join(',') : ' - ' }}
        </template>
      </el-table-column>
      <el-table-column label="上级领导" prop="leaders" align="center" width="120px">
        <template slot-scope="{ row }">
          {{ row.leaders && row.leaders.length ? row.leaders.join(',') : ' - ' }}
        </template>
      </el-table-column>
      <el-table-column label="加入时间" prop="createdAt" width="135px" align="center" />
    </el-table>

    <div v-show="total > 0" class="download">
      <pagination
        :total="total"
        :page.sync="listQuery.pageNo"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<style>
.download {
  display: flex;
  align-items: center;
  margin-top: 10px;
}
</style>
<script>
import waves from '@/directive/waves' // Waves directive
import { getList, getTeam, importSalesman, getChannelStatistics } from '@/api/channel'
import { getToken, getTokenName } from '@/utils/auth'
import ChannelDetail from './components/channelDetail'
export default {
  name: 'Salesperson',
  directives: { waves },
  filters: {},
  components: {
    ChannelDetail
  },
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name'
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      cityProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'child',
        label: 'name',
        value: 'id'
      },
      uploadPath: '',
      headers: {},
      tempPath: '',
      fileList: [],
      agentId: '',
      downloadUrl: '',
      teamOptions: [],
      postOptions: [
        {
          value: 1,
          label: '业务员'
        },
        {
          value: 2,
          label: '团队经理'
        },
        {
          value: 3,
          label: '渠道经理'
        }
      ],
      channelList: []
    }
  },
  created() {
    this.uploadPath = importSalesman()
    this.headers[getTokenName()] = getToken()
    this.tempUrl()
    this.getList()
    this.getTeamOptions()
    this.getChannelList()
  },
  mounted() {
    this.bus.$on('onUpdateList', (e) => {
      this.getList()
    })
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    getTeamOptions() {
      getTeam({ keyword: '' }).then((response) => {
        this.teamOptions = response
      })
    },
    // 获取渠道列表
    getChannelList() {
      getChannelStatistics().then((response) => {
        this.channelList = response
      })
    },
    tempUrl() {
      this.tempPath = 'https://img-pro.naiterui.com/cms/template/批量导入业务员.zip'
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10,
        reportType: 1
      }
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleCreate() {
      this.dialogFormVisible = true
    },
    handleExcelError() {
      this.fileList = []
    },
    handleExcelSuccess(response, file, fileList) {
      this.fileList = []
      if (response.code !== 0) {
        this.$message.error(response.msg)
      } else {
        this.$message({
          message: '操作成功',
          type: 'success'
        })
        this.getList()
      }
    },
    handleRemoveExcel(file, fileList) {},
    handleBeforeUpload(file) {
      const isZip = file.name.endsWith('.zip')
      if (!isZip) {
        this.$message.error('请选择zip文件!')
        return false
      }
    },
    onSelectionChange(data) {
      this.agentId = data.map((item) => item.id).join(',')
    }
  }
}
</script>
