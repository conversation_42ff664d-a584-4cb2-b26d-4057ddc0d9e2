<template>
  <div>
    <el-input
      v-model="listQuery.id"
      placeholder="ID"
      clearable
      class="filter-item"
      style="width: 150px;"
      @keyup.enter.native="handleFilter"
      @clear="handleFilter"
    />
    <el-input
      v-model="listQuery.name"
      placeholder="业务员姓名"
      clearable
      class="filter-item"
      style="width: 150px;"
      @keyup.enter.native="handleFilter"
      @clear="handleFilter"
    />
    <el-input
      v-model="listQuery.phone"
      placeholder="业务员手机号"
      clearable
      class="filter-item"
      style="width: 150px;"
      @keyup.enter.native="handleFilter"
      @clear="handleFilter"
    />
    <el-select
      v-model="listQuery.post"
      clearable
      style="width: 160px;"
      placeholder="职位"
      @change="handleFilter"
    >
      <el-option v-for="item in postOptions" :key="item.value" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
    <DatePicker
      ref="datePickerRef"
      :query-model="listQuery"
      class="filter-item"
      style="width: 230px;"
      gte="createdAtBegin"
      lte="createdAtEnd"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      @change="handleFilter"
    />
    <el-button type="primary" @click="handleFilter">搜索</el-button>
    <el-button type="primary" @click="handleDownload">导出excel</el-button>

    <!-- 统计 -->
    <el-table :data="[info]" border style="width: 800px; margin: 20px;">
      <el-table-column label="下属业务员总数" prop="subordinateSalesmanNum" align="center">
        <template slot-scope="{ row }">
          {{ row.subordinateSalesmanNum || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="业务员新增患者总人数" prop="patientNum" align="center">
        <template slot-scope="{ row }">
          {{ row.patientNum || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="购药订单总数" prop="orderNum" align="center">
        <template slot-scope="{ row }">
          {{ row.orderNum || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="购药订单总金额" prop="orderTotalPrice" align="center">
        <template slot-scope="{ row }">
          {{ row.orderTotalPrice || '-' }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 下属业务员列表 -->
    <el-table v-loading="listLoading" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="80px" />
      <el-table-column label="业务员姓名" prop="name" align="center" width="120px" />
      <el-table-column label="业务员手机号" prop="phone" align="center" width="120px" />
      <el-table-column label="职位" prop="post" width="120px" align="center">
        <template slot-scope="{ row }">
          {{ row.post === '1' ? '业务员' : row.post === '2' ? '团队经理' : '渠道经理' }}
        </template>
      </el-table-column>
      <el-table-column label="所属团队" prop="teamStr" min-width="150px" align="center" />
      <el-table-column label="加入时间" prop="createdAt" width="135px" align="center" />
      <el-table-column label="新增患者" prop="patientNum" width="135px" align="center" />
      <el-table-column label="订单数" prop="orderNum" width="135px" align="center">
        <template slot="header" slot-scope="scope">
          <span>
            订单数
            <el-tooltip :aa="scope" class="item" effect="dark" placement="top-start">
              <div slot="content">患者已支付的总订单数（包含退款订单）</div>
              <i class="el-icon-question"> </i>
            </el-tooltip>
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="订单总金额"
        prop="orderTotalPrice"
        min-width="150px"
        align="center"
      ></el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="150px">
        <template slot-scope="{ row }">
          <el-button type="primary" size="mini" @click="handleOpen(row.id)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script>
import DatePicker from '@/components/DatePicker'
import { getSubordinateSalesmanList, getSubordinateStatistics, subordinateExport } from '@/api/channel'
import { getToken, getTokenName } from '@/utils/auth'
export default {
  components: {
    DatePicker
  },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      info: {},
      postOptions: [
        {
          value: 1,
          label: '业务员'
        },
        {
          value: 2,
          label: '团队经理'
        },
        {
          value: 3,
          label: '渠道经理'
        }
      ],
      downloadUrl: '',
      listLoading: false
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {},
  mounted() {
    // this.getData()
  },
  methods: {
    // 下属业务员列表
    getData() {
      this.listLoading = true
      this.listQuery.salesmanId = this.id
      getSubordinateSalesmanList(this.listQuery).then((response) => {
        this.listLoading = false
        this.total = response.totalCount
        this.list = response.list
        this.getinfo()
      })
    },
    // 下属业务员统计
    getinfo() {
      const params = {
        createdAtBegin: this.listQuery.createdAtBegin,
        createdAtEnd: this.listQuery.createdAtEnd,
        salesmanId: this.id
      }
      getSubordinateStatistics(params).then((response) => {
        this.info = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getData()
    },
    handleOpen(id) {
      this.$emit('onOpenAgent', { id })
    },
    // 导出excel
    handleDownload() {
      const params = {
        salesmanId: this.id,
        id: this.listQuery.id,
        name: this.listQuery.name,
        phone: this.listQuery.phone,
        post: this.listQuery.post,
        createdAtBegin: this.listQuery.createdAtBegin,
        createdAtEnd: this.listQuery.createdAtEnd
      }
      params[getTokenName()] = getToken()
      console.log(params, 'params')
      this.downloadUrl = subordinateExport(params)
      console.log(params, this.downloadUrl, 'downloadUrl')
      window.location.href = this.downloadUrl
    }
  }
}
</script>
<style></style>
