<template>
  <div class="">
    <el-input
      v-model="listQuery.patientId"
      placeholder="患者ID"
      clearable
      class="filter-item"
      style="width: 150px;"
      @keyup.enter.native="handleFilter"
      @clear="handleFilter"
    />
    <el-input
      v-model="listQuery.name"
      placeholder="姓名"
      clearable
      class="filter-item"
      style="width: 150px;"
      @keyup.enter.native="handleFilter"
      @clear="handleFilter"
    />
    <el-input
      v-model="listQuery.phone"
      placeholder="手机号"
      clearable
      class="filter-item"
      style="width: 150px;"
      @keyup.enter.native="handleFilter"
      @clear="handleFilter"
    />
    <DatePicker
      ref="datePickerRef"
      :query-model="listQuery"
      class="filter-item"
      style="width: 230px;"
      gte="createdAtBegin"
      lte="createdAtEnd"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      @change="handleFilter"
    />
    <el-button type="primary" @click="handleFilter">搜索</el-button>

    <el-table :data="[info]" border style="width: 800px; margin: 20px;">
      <el-table-column label="姓名" prop="salesmanName" align="center">
        <template slot-scope="{ row }">
          {{ row.salesmanName || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="新增患者总人数" prop="patientNum" align="center">
        <template slot-scope="{ row }">
          {{ row.patientNum || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="购药订单总数" prop="orderNum" align="center">
        <template slot-scope="{ row }">
          {{ row.orderNum || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="购药订单总金额" prop="orderTotalPrice" align="center">
        <template slot-scope="{ row }">
          {{ row.orderTotalPrice || '-' }}
        </template>
      </el-table-column>
    </el-table>

    <el-table v-loading="patientLoading" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="patientId" align="center" />
      <el-table-column label="患者姓名" prop="name" align="center">
        <template slot-scope="{ row }">
          {{ row.name || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="手机号" prop="phone" align="center">
        <template slot-scope="{ row }">
          {{ row.phone || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="性别" prop="gender" align="center">
        <template slot-scope="{ row }">
          {{ genderStr[row.gender] }}
        </template>
      </el-table-column>
      <el-table-column label="注册时间" prop="createdAt" align="center" />
      <el-table-column label="订单数" prop="orderNum" align="center" />
      <el-table-column label="订单总金额" prop="orderTotalPrice" align="center"></el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getData"
    />
  </div>
</template>

<script>
import { getAddPatientList, getAddPatientStatistics } from '@/api/channel'
import DatePicker from '@/components/DatePicker'
export default {
  components: {
    DatePicker
  },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      info: {},
      genderStr: ['女', '男', '未知'],
      patientLoading: false
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {},
  mounted() {
    // this.getData()
  },
  methods: {
    getData() {
      this.patientLoading = true
      this.listQuery.salesmanId = this.id
      getAddPatientList(this.listQuery).then((response) => {
        this.patientLoading = false
        this.total = response.totalCount
        this.list = response.list
        this.getPatientInfo()
      })
    },
    getPatientInfo() {
      const params = {
        createdAtBegin: this.listQuery.createdAtBegin,
        createdAtEnd: this.listQuery.createdAtEnd,
        salesmanId: this.id
      }
      getAddPatientStatistics(params).then((response) => {
        this.info = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getData()
    },
    handleOpen(id) {
      this.$emit('onOpenAgent', { id })
    }
  }
}
</script>
<style></style>
