<template>
  <div>
    <el-dialog
      title="业务员详情"
      :visible.sync="dialogInfoVisible"
      width="80%"
      top="2vh"
      append-to-body
      @close="handleCloseDetails"
    >
      <el-tabs v-model="activeTabs" style="min-height: 300px;" type="card" @tab-click="handleTabClick">
        <el-tab-pane label="基本信息" name="tab_1">
          <BaseInfo :id="agentId" ref="tab_1"></BaseInfo>
        </el-tab-pane>
        <el-tab-pane label="下属业务员" name="tab_2">
          <AgentPuisne :id="agentId" ref="tab_2" @onOpenAgent="onOpenAgent"></AgentPuisne>
        </el-tab-pane>
        <el-tab-pane label="新增患者" name="tab_3">
          <AddPatient :id="agentId" ref="tab_3"></AddPatient>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <el-link v-if="name" type="primary" :underline="false" @click="handleAgentDetailsInfo()">{{
      name
    }}</el-link>
  </div>
</template>
<style scoped>
.el-dialog {
  text-align: left;
}
</style>
<script>
import BaseInfo from './BaseInfo'
import AgentPuisne from './AgentPuisne'
import AddPatient from './AddPatient'
export default {
  name: 'AgentDetail',
  components: {
    BaseInfo,
    AgentPuisne,
    AddPatient
  },
  props: {
    id: {
      type: [String, Number],
      required: false,
      default: ''
    },
    name: {
      type: String,
      required: false,
      default: ''
    },
    handleFilter: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      agentId: this.id,
      activeTabs: 'tab_1',
      dialogInfoVisible: false
    }
  },
  watch: {
    id(newVal, oldVal) {
      this.agentId = newVal
    }
  },
  mounten() {},
  methods: {
    handleTabClick(event) {
      this.$nextTick(() => {
        this.$refs[event.name].getData()
      })
    },
    handleAgentDetailsInfo() {
      console.log(this.id, '116')
      this.agentId = this.id
      this.dialogInfoVisible = true
      this.activeTabs = 'tab_1'
      this.$nextTick(() => {
        this.$refs['tab_1'].getData()
      })
    },
    handleCloseDetails() {
      this.dialogInfoVisible = false
    },
    onOpenAgent(data) {
      if (this.agentId === data.id) {
        this.activeTabs = 'tab_3'
        this.$refs['tab_3'].getData()
      } else {
        this.agentId = data.id
        this.activeTabs = 'tab_1'
      }
    }
  }
}
</script>
