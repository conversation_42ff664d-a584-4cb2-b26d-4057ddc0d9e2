<template>
  <div class="">
    <!-- 经纪人基本信息 -->
    <el-form ref="dataForm" :model="agentInfo" label-position="right" label-width="150px">
      <el-row type="flex" :gutter="50">
        <el-col :span="13">
          <el-form-item label="加入时间:" prop="createdAt">
            <div>{{ agentInfo.createdAt }}</div>
          </el-form-item>
          <el-form-item label="姓名:" prop="name">
            <div>{{ agentInfo.name }}</div>
          </el-form-item>
          <el-form-item label="性别:" prop="gender">
            <div>{{ agentInfo.gender == 0 ? '未知' : agentInfo.gender == 1 ? '男' : '女' }}</div>
          </el-form-item>
          <el-form-item label="手机号:" prop="phone">
            <el-input v-model="agentInfo.phone" readonly />
          </el-form-item>
          <el-form-item label="职位:" prop="postDescribe">
            <el-input v-model="agentInfo.postDescribe" readonly />
          </el-form-item>
          <el-form-item label="所属团队:" prop="team">
            <!-- <el-input v-model="agentInfo.team" readonly /> -->
            <span>{{ agentInfo.team }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <div class="canvas-warp">
            <canvas :id="dynamicId"></canvas>
            <p>邀请患者二维码</p>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13">
          <el-form-item label="身份证号:" prop="number">
            <el-input v-model="agentInfo.cardNo" readonly />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13">
          <el-form-item label="身份证正面:">
            <el-upload
              :disabled="!isSave"
              list-type="picture-card"
              :action="uploadPath"
              :headers="headers"
              :file-list="agentInfo.frontUrl"
              :before-upload="handleBeforeUpload"
              :on-success="handlefrontSuccess"
              accept="image/png, image/jpg"
            >
              <div slot="file" slot-scope="{ file }">
                <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePreview(file.url)">
                    <i class="el-icon-zoom-in"></i>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                    <i class="el-icon-download"></i>
                  </span>
                  <span
                    v-if="isSave"
                    class="el-upload-list__item-delete"
                    @click="handleRemoveUrls(file.url, 'front')"
                  >
                    <i class="el-icon-delete"></i>
                  </span>
                </span>
              </div>
              <el-button :disabled="!isSave" size="small" type="primary">点击上传</el-button>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="13">
          <el-form-item label="身份证反面:">
            <el-form-item>
              <el-upload
                :disabled="!isSave"
                list-type="picture-card"
                :action="uploadPath"
                :headers="headers"
                :file-list="agentInfo.backUrl"
                :before-upload="handleBeforeUpload"
                :on-success="handlebackSuccess"
                accept="image/png, image/jpg"
              >
                <div slot="file" slot-scope="{ file }">
                  <el-image class="el-upload-list__item-thumbnail" :src="file.url" fit="fill" alt />
                  <span class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handlePreview(file.url)">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span class="el-upload-list__item-delete" @click="handleDownload(file.url)">
                      <i class="el-icon-download"></i>
                    </span>
                    <span
                      v-if="isSave"
                      class="el-upload-list__item-delete"
                      @click="handleRemoveUrls(file.url, 'back')"
                    >
                      <i class="el-icon-delete"></i>
                    </span>
                  </span>
                </div>
                <el-button :disabled="!isSave" size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
              </el-upload>
            </el-form-item>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24" style="text-align: right;">
          <el-button v-if="isSave" v-permission="['agent:save']" type="primary" @click="handleSave()">保存资料</el-button>
          <el-button v-else v-permission="['agent:save']" type="primary" @click="isSave = true">编辑资料</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog append-to-body :visible.sync="dialogVisible">
      <img width="100%" :src="dialogImageUrl" alt />
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves' // Waves directive
import { getSalesmanInfo, putSalesmanInfoEdit } from '@/api/channel'
import { getToken, getTokenName } from '@/utils/auth'
import qrcode from 'qrcode'
export default {
  directives: { waves },
  props: {
    id: {
      type: Number,
      required: false,
      default: null
    }
  },
  data() {
    return {
      cityProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false,
        children: 'child',
        label: 'name',
        value: 'id'
      },
      agentInfo: {},
      headers: {},
      uploadPath: process.env.VUE_APP_BASE_API + '/storage',
      dialogImageUrl: '',
      dialogVisible: false,
      userStatus: {},
      isSave: false,
      dynamicId: ''
    }
  },
  computed: {},
  watch: {
    id(newVal, oldVal) {
      this.id = newVal
      this.getData()
    }
  },
  created() {
    this.headers[getTokenName()] = getToken()
  },
  methods: {
    getData(id) {
      this.dynamicId = 'canvas_' + this.id
      getSalesmanInfo({ id: this.id }).then((response) => {
        this.isSave = false
        this.agentInfo = response
        this.userStatus.id = this.agentInfo.id
        this.userStatus.areaId = this.agentInfo.areaId
        this.agentInfo.backUrl = [{ url: this.agentInfo.backUrl }]
        this.agentInfo.frontUrl = [{ url: this.agentInfo.frontUrl }]
        this.agentInfo.team = response.teamList && response.teamList.length > 0 ? response.teamList.join('，') : '-'
        const canvas = document.getElementById(this.dynamicId)
        qrcode.toCanvas(canvas, this.agentInfo.qrCode, { width: 200, height: 200 }, error => {
          if (error) {
            console.log(error)
            this.$message.error('加载失败')
          }
        })
      })
    },
    handleSave() {
      const params = {
        backUrl: this.agentInfo.backUrl[0].url,
        frontUrl: this.agentInfo.frontUrl[0].url,
        id: this.id
      }
      putSalesmanInfoEdit(params).then((response) => {
        this.isSave = false
        this.$message({
          type: 'success',
          message: '操作成功!'
        })
      })
    },
    handlefrontSuccess(response) {
      console.log(response, 448)
      this.agentInfo.frontUrl = [{ url: response.data }]
    },
    handlebackSuccess(response) {
      this.agentInfo.backUrl = [{ url: response.data }]
    },
    handleRemoveUrls(file, type) {
      this.$confirm('此操作将删除信息照片, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (type === 'front') {
          this.$set(this.agentInfo, 'frontUrl', [])
        } else {
          this.$set(this.agentInfo, 'backUrl', [])
        }
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['userStatusDataForm'].clearValidate()
        this.userStatus = {
          id: this.agentInfo.id,
          areaId: this.agentInfo.areaId
        }
      })
    },
    handlePreview(file) {
      this.dialogImageUrl = file
      this.dialogVisible = true
    },
    handleDownload(file) {
      this.downloadIamge(file, new Date().getTime())
    },
    handleBeforeUpload(file) {
      const isLt = file.size / 1024 < 500
      if (!isLt) {
        this.$message({
          message: '上传文件大小不能超过 5MB!',
          type: 'warning'
        })
      }
      return isLt
    },
    downloadIamge(imgsrc, name) {
      //下载图片地址和图片名
      var image = new Image()
      image.setAttribute('crossOrigin', 'anonymous')
      image.onload = function() {
        var canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        var context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.width, image.height)
        var url = canvas.toDataURL('image/png') //得到图片的base64编码数据
        var a = document.createElement('a') // 生成一个a元素
        var event = new MouseEvent('click') // 创建一个单击事件
        a.download = name || 'photo' // 设置图片名称
        a.href = url // 将生成的URL设置为a.href属性
        a.dispatchEvent(event) // 触发a的单击事件
      }
      image.src = imgsrc
    }
  }
}
</script>
<style lang="scss" scoped>
.el-upload-list__item.is-ready {
  display: none;
}
.el-upload-list__item {
  transition: none !important;
}
.canvas-warp {
  display: flex;
  flex-direction: column;
  align-items: center;
  p {
    color: #1890ff;
  }
}
</style>
