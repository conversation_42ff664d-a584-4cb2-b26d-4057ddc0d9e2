<template>
  <div class="app-container">
    <img src="@/assets/images/ok.png" />
    <div>成功</div>
  </div>
</template>

<style scoped>
  .app-container {
    text-align: center;
    padding-top: 30%;
  }
  .app-container img {
    width: 100px;
    height: 100px;
    padding-bottom: 10px;
  }
</style>
<script>
export default {
  name: 'Project',
  components: {
  },
  data() {
    return {
    }
  },
  computed: {},
  created() {},
  mounted() {
    window.setTimeout(() => {
      this.closeT()
    }, 1000)
  },
  methods: {
    closeT() {
      this.$nextTick(() => {
        // 找到父窗口的方法close
        console.log('==============closeT============', window.parent.vm.$children[0].$children[1].$children[3].$children[0])
        console.log(window.parent.vm.$children[0].$children[1], 40)
        // window.parent.vm.$children[0].$children[1].$children[1].$children[0].Cfsign()
        // window.parent.vm.$children[0].$children[1].$children[3].$children[0].parentClose()
        // const app = window.top.document.getElementById('app')
        // app.__vue__.$children[1].$children[3].$children[1].parentClose()
        window.top.parentClose && window.top.parentClose()
      })
    }
  }
}
</script>
