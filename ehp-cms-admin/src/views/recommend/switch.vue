<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" style="margin-bottom: 20px;">处方审核设置</el-col>
    </el-row>
    <el-form ref="dataForm" :model="switchData" :rules="rules" class="demo-form-inline">
      <el-row :gutter="20">
        <el-form-item label="处方审核类型" label-width="220px" prop="type">
          <el-radio-group v-model="switchData.auditType">
            <el-radio :label="1">系统审核</el-radio>
            <el-radio :label="2">人工审核</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" label-width="220px" prop="remark">
          <el-input
            v-model="switchData.remark"
            type="textarea"
            placeholder="请输入内容"
            maxlength="50"
            style="width:50%;"
          ></el-input>
        </el-form-item>
        <el-form-item label-width="220px">
          <el-button type="primary" @click="saveSwitch('dataForm')">保存设置</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-divider></el-divider>
    <el-row>
      <el-col :span="24" style="margin-bottom: 20px;">变更记录</el-col>
    </el-row>
    <el-table :key="tableKey" :data="dataList.list" fit highlight-current-row>
      <el-table-column label="序号" prop="id" align="center" width="80px" />
      <el-table-column
        label="审核执行类型"
        prop="auditType"
        width="100px"
        align="center"
        :formatter="statusFor"
      />
      <el-table-column label="操作人" prop="createdBy" align="center" width="100px" />
      <el-table-column label="操作时间" prop="createdAt" align="center" width="140px" />
      <el-table-column label="备注" prop="remark" align="center" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import api_audit from '@/api/recommend/audit'
export default {
  data() {
    return {
      switchData: {
        auditType: 0,
        remark: ''
      },
      total: 0,
      dataList: [],
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      rules: {
        auditType: [
          { required: true, message: '请选择处方审核类型', trigger: 'blur' }
        ],
        remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
      },
      auditTypes: ['系统审核', '人工审核'],
      tableKey: 0
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      api_audit.configlist(this.listQuery).then(response => {
        this.dataList = response
        if (this.dataList.currPage === 1 && this.dataList.list.length > 0) {
          this.switchData.auditType = this.dataList.list[0].auditType
        }
        this.total = response.totalCount
      })
    },
    saveSwitch(dataForm) {
      const that = this
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          api_audit.configset(this.switchData).then(response => {
            that.switchData.remark = ''
            that.getList()
          })
        }
      })
    },
    statusFor(row, column, cellValue, index) {
      return this.auditTypes[cellValue - 1]
    }
  }
}
</script>
