<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :inline="true"
      label-width="150px"
      class="demo-form-inline"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="处方笺编号:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.serialNumber }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="医生姓名:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.doctorName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="医生电话:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.doctorPhone }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者姓名:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.patientName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者性别:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ genders[data.patientGender] }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者年龄:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.patientAgeStr }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者电话:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.patientAge/1 < 15 ? '无' : data.patientPhone }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label="临床诊断:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.diagnosis }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="title">药品信息</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :key="tableKey" :data="data.skus" fit highlight-current-row>
            <el-table-column label="skuID" prop="skuId" width="100px" align="center" />
            <el-table-column label="sku编码" prop="skuNumber" width="150px" align="center" />
            <el-table-column label="sku名称" prop="name" align="center" />
            <el-table-column label="数量" width="100px" align="center">
              <template slot-scope="{row}">{{ row.quantity }}{{ row.quantityUnit }}</template>
            </el-table-column>
            <el-table-column label="用法用量" prop="usages" width="230px" align="center" />
            <el-table-column label="备注" prop="remark" width="160px" align="center" />
          </el-table>
        </el-col>
      </el-row>
      <div class="title">处方单预览</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :key="tableKey" :data="data.files" fit highlight-current-row>
            <el-table-column label="处方单图片名称" prop="name" width="130" align="center" />
            <el-table-column label="处方单地址" prop="url" align="center" />
            <el-table-column label="操作" fixed="right" align="center" width="80">
              <template slot-scope="{row}">
                <el-button type="primary" size="mini" @click="viewFile(row.url)">
                  <span>查看</span>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <div class="title">操作日志</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table :key="tableKey" :data="list.list" fit highlight-current-row>
            <el-table-column label="操作人" prop="createdBy" width="100px" align="center" />
            <el-table-column label="操作时间" prop="createdAt" width="140px" align="center" />
            <el-table-column label="操作内容" prop="remark" align="center" />
          </el-table>
          <pagination
            v-show="list.totalCount > 0"
            :total="list.totalCount"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="getAuditLogs"
          />
        </el-col>
      </el-row>
    </el-form>
    <el-dialog title="请输入验证码" :visible.sync="passdialogFormVisible" width="50%">
      <el-form ref="passdataForm" :model="verInfo" :rules="rules" label-position="left" label-width="auto">
        <el-form-item label="验证码" prop="verpass">
          <el-input
            v-model="verInfo.verpass"
            class="brandinput"
            placeholder="请输入验证码"
          />
        </el-form-item>
        <el-button type="primary" :disabled="verInfoDialog.disabled" @click="getCode">{{ verInfoDialog.msg }}</el-button>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align:right;">
        <el-button type="primary" :loading="loading" @click="surePass('passdataForm')">确定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="dialog.visible" width="30%" :before-close="handleClose">
      <span>{{ dialog.msg }}</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialog.visible = false">取 消</el-button>
        <el-button v-if="dialog.ok!=''" type="primary" @click="dialog.ok">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="查看图片"
      :visible.sync="image.isOpen"
      width="70%"
      :before-close="handleClose"
      class="img_view"
    >
      <el-image :src="image.src" fit="cover"></el-image>
    </el-dialog>
    <el-dialog
      title="E签宝意愿签署页面"
      :visible.sync="dialogSignedVisible"
      width="50%"
    >
      <!-- <iframe name="iframe_e" id="iframe_e" ref="iframe_et" :src="url" frameborder="0" class="iframe_e"></iframe> -->
    </el-dialog>
  </div>
</template>
<style  scoped>
.title {
  padding-bottom: 10px;
  margin-top: 40px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
.el-dialog__footer {
  padding-right: 20%;
}
.labelitem .el-form-item {
  display: flex;
}
.labelitem .el-form-item__content {
  flex: 1;
}
.el-dialog__footer {
  padding-right: 20px;
}
.img_view {
  text-align: center;
}
</style>
<script>
import recommendApi from '@/api/recommend/audit'
import { Message } from 'element-ui'
export default {
  name: '',
  filters: {},
  components: {},
  data() {
    return {
      tableKey: 0,
      auditLevel: null,
      passdialogFormVisible: false,
      verInfo: {
        verpass: null
      },
      verInfoDialog: {
        msg: '获取验证码',
        disabled: false,
        isSendCode: false
      },
      rules: {
        verpass: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      data: {
        audit: {}
      },
      auditType: [
        { value: 1, label: '审核通过' },
        { value: 2, label: '审核不通过' }
      ],
      audit_type: ['待审核', '通过', '不通过'],
      genders: ['女', '男'],
      auditStatus: 1,
      recomId: 0,
      remark: '',
      dialog: {
        msg: '审核不通过原因必填，字数限制1-50字',
        visible: false,
        ok: false
      },
      image: {
        isOpen: false,
        src: ''
      },
      loading: false,
      sub_loading: false,
      list: {
        pageNo: 1,
        totalCount: 1
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogSignedVisible: false
    }
  },
  created() {
    const params = {}
    if (this.$route.params.id) {
      params.recomId = this.$route.params.id
      this.recomId = params.recomId
      this.getAuditDetail(params)
      // this.checkAudit()
    }
  },
  methods: {
    getAuditDetail(params) {
      const that = this
      recommendApi.getDetail(params).then(function(data) {
        if (data) {
          // if (data.audit && data.audit.status !== 0) {
          // data.audits = [data.audit]
          // } else {
          // data.audits = []
          // }
          that.data = data
          that.getAuditLogs()
        }
      })
    },
    checkAudit() {
      recommendApi.veridentity().then(response => {
        this.auditLevel = response.auditLevel
      })
    },
    submitAudit() {
      const that = this
      if (that.auditStatus === 2) {
        if (that.remark.length > 50 || that.remark.length < 1) {
          that.dialog.msg = '审核不通过原因必填，字数限制1-50字'
          that.dialog.ok = ''
          that.dialog.visible = true
          return false
        } else {
          const params = {}
          params.status = that.auditStatus
          params.remark = that.remark
          params.recomId = that.recomId
          that.sub_loading = true
          recommendApi.audit(params, function(res) {
            res = JSON.parse(res)
            if (res.code === 0) {
              res.data = null
            }
            return res
          }).then(function(data) {
            that.sub_loading = false
            if (data === null) {
              Message({
                message: '操作成功',
                type: 'success',
                duration: 5 * 1000
              })
              that.getAuditDetail({ recomId: that.recomId })
            }
          }).finally(function(r) {
            that.sub_loading = false
          })
        }
      } else {
        that.passdialogFormVisible = true
      }
    },
    surePass(dataForm) {
      const that = this
      if (!that.verInfoDialog.isSendCode) {
        Message({
          message: '请先发送验证码',
          type: 'error',
          duration: 5 * 1000
        })
        return false
      }
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          const params = {}
          params.status = that.auditStatus
          params.remark = that.remark
          params.recomId = that.recomId
          params.signPwd = that.verInfo.verpass
          params.force = 0
          that.loading = true
          recommendApi
            .audit(params, function(res) {
              that.loading = false
              res = JSON.parse(res)
              // if (res.code === 13050302) {
              //   res.code = 0
              //   res.data = false
              //   that.dialog.msg = '药师电子签名异常，是否确定继续？'
              //   that.dialog.ok = that.dialogOk
              //   that.dialog.visible = true
              // } else if (res.code === 0) {
              //   res.data = null
              // }
              if (res.code === 0) {
                res.data = null
              }
              return res
            })
            .then(function(data) {
              if (data === null) {
                Message({
                  message: '操作成功',
                  type: 'success',
                  duration: 5 * 1000
                })
                that.getAuditDetail({ recomId: that.recomId })
              }
            }).finally(function() {
              that.loading = false
              that.verInfo.verpass = ''
            })
          this.passdialogFormVisible = false
        }
      })
    },
    statusFor(row, column, cellValue, index) {
      return this.audit_type[cellValue]
    },
    dialogOk() {
      const that = this
      that.dialog.visible = false
      const params = {}
      params.status = that.auditStatus
      params.remark = that.remark
      params.recomId = that.recomId
      params.force = 1
      that.loading = true
      recommendApi.audit(params).then(function(data) {
        that.loading = false
        if (data === null) {
          Message({
            message: '操作成功',
            type: 'success',
            duration: 5 * 1000
          })
          that.getAuditDetail({ recomId: that.recomId })
        }
      })
    },
    viewFile(src) {
      // const that = this
      // if (src.toLocaleUpperCase().indexOf('.PDF') === -1) {
      //   that.image.src = src
      //   that.image.isOpen = true
      // } else {
      window.open(src)
      // }
    },
    handleClose() {
    },
    getAuditLogs() {
      this.listQuery.recomId = this.recomId
      recommendApi.getAuditLogs(this.listQuery).then(res => {
        this.list = res
      })
    },
    getCode() {
      const that = this
      const params = {}
      params.recomId = that.recomId
      that.verInfoDialog.disabled = true
      recommendApi.getCode(params).then(res => {
        that.verInfoDialog.isSendCode = true
        that.verInfoDialog.msg = '60s'
        let time = 60
        const intval = window.setInterval(() => {
          time--
          that.verInfoDialog.msg = time + 's'
          if (time === 0) {
            that.verInfoDialog.disabled = false
            that.verInfoDialog.msg = '获取验证码'
            window.clearInterval(intval)
          }
        }, 1000)
      }).catch(e => {
        that.verInfoDialog.disabled = false
      })

    }
  }
}
</script>
