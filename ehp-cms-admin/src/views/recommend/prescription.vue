<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.id"
        clearable
        placeholder="处方ID"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.doctorId"
        clearable
        placeholder="医生ID"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.patientId"
        clearable
        placeholder="患者ID"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <el-input
        v-model="listQuery.orderSn"
        clearable
        placeholder="订单号"
        class="filter-item"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >{{ $t("table.search") }}</el-button>
    </div>

    <el-table :key="tableKey" :data="list" border fit highlight-current-row style="width: 100%">
      <el-table-column label="处方笺编号" prop="serialNumber" width="150px" align="center" />
      <el-table-column label="处方笺标题" prop="hospitalName" width="230px" align="center" />
      <el-table-column label="医生姓名" width="100px" prop="doctorName" align="center" />
      <el-table-column label="患者姓名" width="100px" prop="patientName" align="center" />
      <el-table-column label="临床诊断" prop="diagnosis" align="center" />
      <el-table-column
        label="医生电子签名状态"
        prop="doctorSignStatusDescribe"
        width="140px"
        align="center"
      />
      <el-table-column label="药师姓名" prop="pharmacistName" width="100px" align="center" />
      <el-table-column
        label="$药师电子签名状态"
        prop="pharmacistSignStatusDescribe"
        width="140px"
        align="center"
      />
      <el-table-column label="药师电子签名时间" prop="pharmacistSignTime" width="155px" align="center" />
      <el-table-column label="订单号" width="130px" prop="orderSn" align="center" />
      <el-table-column label="订单金额" prop="orderAmount" width="80px" align="center" />
      <el-table-column label="创建时间" prop="createdAt" width="140px" align="center" />
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <!-- 详情 -->
    <el-dialog
      :title="textMap[dialogStatus]"
      :visible.sync="dialogFormVisible"
      top="2vh"
      width="90%"
    >
      <PrescriptionDetails :id="productId" />
    </el-dialog>
  </div>
</template>
<script>
import { getList } from '@/api/recommend/medication'
import waves from '@/directive/waves'
import PrescriptionDetails from './components/prescriptionDetails'
import DatePicker from '@/components/DatePicker'

export default {
  name: 'MedicationTable',
  directives: { waves },
  components: { PrescriptionDetails, DatePicker },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: null,
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderByField: 'createdAt',
        orderBy: 'desc'
      },
      dialogFormVisible: false,
      dialogStatus: '',
      textMap: {
        update: '更新',
        create: '新增'
      },
      roleList: {},
      user: {
        status: 1,
        deptId: null,
        deptName: null,
        roleIdList: []
      }
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    }
  }
}
</script>
