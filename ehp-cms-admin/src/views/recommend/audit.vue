<template>
  <div>
    <div v-if="part" class="app-container" style="height:700px;display:flex;justify-content:center;align-items:center;flex-direction:column">
      <div>请登陆药师端APP扫码授权</div>
      <div v-if="status==2" style="width:200px;height:200px;margin:20px 0;">
        <canvas id="canvas"></canvas>
      </div>
      <div v-if="status==3" style="width:200px;height:200px;background:#eee;display:flex;justify-content:center;align-items:center;margin:20px 0;">
        <el-button type="primary" icon="el-icon-refresh" @click="refurbishCode">刷新二维码</el-button>
      </div>
      <div style="width:250px;font-size:14px;line-height:30px;">请先在药师端APP进行认证，认证完成后使用药师端APP进行扫码绑定，确认绑定后即可在PC端进行审方工作。</div>
    </div>
    <div v-else class="app-container">
      <div class="filter-container">
        <el-input
          v-model="listQuery.serialNumber"
          clearable
          placeholder="处方笺编号"
          class="filter-item"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <DictSelect
          v-model="listQuery.status"
          class="filter-item"
          style="width:100px"
          placeholder="审核状态"
          type="medication_audit_type"
          @change="handleFilter"
        />
        <el-input
          v-model="listQuery.doctorName"
          clearable
          placeholder="医生姓名"
          class="filter-item"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.doctorPhone"
          clearable
          placeholder="医生电话"
          class="filter-item"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.patientName"
          clearable
          placeholder="患者姓名"
          class="filter-item"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <DatePicker
          ref="datePickerRef"
          :query-model="listQuery"
          class="filter-item"
          style="width: 210px"
          @change="handleFilter"
        />
        <el-button v-waves type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
        <el-button v-waves type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      </div>
      <div style="display:flex;flex-direction:row-reverse;">
        <el-button v-if="status==1" type="primary" @click="removeBind">解除绑定</el-button>
        <el-button v-if="status==1" type="primary" style="margin-right:10px">{{ value }} 药师</el-button>
      </div>
      <el-table :key="tableKey" :data="list.list" fit highlight-current-row style="width: 100%">
        <el-table-column
          label="审核状态"
          prop="status"
          :formatter="statusFor"
          width="100px"
          align="center"
        />
        <el-table-column label="审核药师" prop="pharmacistName" width="100px" align="center" />
        <el-table-column label="处方笺编号" prop="serialNumber" width="150px" align="center" />
        <el-table-column label="医生姓名" prop="doctorName" width="150px" align="center" />
        <el-table-column label="医生电话" prop="doctorPhone" width="150px" align="center" />
        <el-table-column label="患者姓名" prop="patientName" width="150px" align="center" />
        <el-table-column label="患者电话" prop="patientPhone" width="150px" align="center" />
        <el-table-column label="创建时间" prop="createdAt" width="140px" align="center" />
        <!-- <el-table-column label="失效时间" prop="expireAt" width="140px" align="center" /> -->
        <el-table-column label="操作" fixed="right" align="center">
          <template slot-scope="{row}">
            <!-- <el-button
            v-if="row.status == 0"
            v-waves
            v-permission="['medication:recom:audit']"
            type="primary"
            size="mini"
            @click="handleAudit(row.recomId,row.status)"
          >
            <span>查看</span>
          </el-button> -->
            <el-button
              v-waves
              v-permission="['medication:recom:detail']"
              type="primary"
              size="mini"
              @click="seeAudit(row.recomId,row.status)"
            >
              <span>查看</span>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="list.totalCount > 0"
        :total="list.totalCount"
        :page.sync="listQuery.pageNo"
        :limit.sync="listQuery.pageSize"
        @pagination="getList"
      />
      <el-dialog title="账号激活" :visible.sync="dialogFormVisible">
        <el-form ref="dataForm" :model="verInfo" :rules="rules">
          <div>
            <el-form-item label="手机号" label-width="38%" prop="phone">
              <el-input
                v-model="verInfo.phone"
                class="brandinput"
                style="width:170px"
              />
              <el-button type="primary" :disabled="verInfoDialog.disabled" @click="getCode">{{ verInfoDialog.msg }}</el-button>
            </el-form-item>
            <el-form-item label="验证码" label-width="38%" prop="verifyCode">
              <el-input
                v-model="verInfo.verifyCode"
                class="brandinput"
                placeholder="请输入验证码"
                style="width:170px"
              />
              <el-button type="primary" @click="setPharmacist">关联药师</el-button>
            </el-form-item>
          </div>
          <el-form-item label="药师" label-width="38%">
            <el-input
              v-model="verInfo.pharmacistName"
              class="brandinput"
              style="width:170px"
              readonly
            />
          </el-form-item>
          <el-form-item label="身份证号" label-width="38%" prop="idCard">
            <el-input
              v-model="verInfo.idCard"
              class="brandinput"
              placeholder="请输入身份证号"
              style="width:170px"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align:center;">
          <el-button type="primary" @click="verAudit('dataForm')">激活</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { getAuditList, veridentity, setidentity, getAuthCode, getPharmacist, getQrCode, removeBind } from '@/api/recommend/audit'
import { Message } from 'element-ui'
import DictSelect from '@/components/DictSelect'
import waves from '@/directive/waves'
import DatePicker from '@/components/DatePicker'
import qrcode from 'qrcode'
export default {
  name: 'Recommendaudit',
  directives: { waves },
  filters: {},
  components: {
    DatePicker,
    DictSelect
  },
  data() {
    return {
      timer: null,
      status: null,
      uuid: '',
      value: '',
      part: true,
      verInfo: {
        idCard: null,
        verifyCode: null,
        pharmacistName: '',
        phone: '',
        cistId: null
      },
      auditLevel: null,
      recomId: null,
      dialogFormVisible: false,
      phone: null,
      verInfoDialog: {
        msg: '获取验证码',
        disabled: false,
        isSendCode: false
      },
      verList: [],
      tableKey: 0,
      list: {
        pageNo: 1,
        totalCount: 1
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        drugType: 1,
        serialNumber: '',
        status: '',
        doctorName: '',
        doctorPhone: '',
        patientName: ''
      },
      dates: {},
      rules: {
        cistId: [{ required: true, message: '请选择关联药师', trigger: 'change' }],
        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
        verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
      },
      audit_type: ['待审核', '通过', '不通过', '超时未审核'],
      isFlage: false
    }
  },
  created() {
    this.$once('hook:beforeDestroy', () => {
      if (this.timer) {
        clearInterval(this.timer)
      }
    })
  },
  activated() {
    window.sessionStorage.removeItem('uuid')
    this.getQrCode()
    this.refurbishCode()
    this.status = window.sessionStorage.getItem('status')
    this.uuid = window.sessionStorage.getItem('uuid')
    this.value = window.sessionStorage.getItem('value')
    if (this.status === 1) {
      this.part = false
      this.getList()
      clearInterval(this.timer)
    } else if (this.status === 2) {
      this.part = true
      this.timer = window.setInterval(() => {
        setTimeout(this.getQrCode(), 0)
      }, 2000)
    } else if (this.status === 3) {
      this.part = true
      clearInterval(this.timer)
      this.uuid = ''
    }
    console.log('============activated=============')
  },
  deactivated() {
    console.log('============deactivated=============')
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  mounted() {},
  methods: {
    // 获取数据
    getList() {
      getAuditList(this.listQuery).then(response => {
        if (response) {
          this.list = response
        }
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.serialNumber = ''
      this.listQuery.status = ''
      this.listQuery.doctorName = ''
      this.listQuery.doctorPhone = ''
      this.listQuery.patientName = ''
      this.handleFilter()
      this.$refs.datePickerRef.reset()
    },
    handleAudit(id, status) {
      if (status !== 0) {
        this.$router.push({
          path: './info/' + id
        })
        return false
      }
      veridentity().then(response => {
        this.recomId = id
        this.verList = response.list
        if (response.status === 1) {
          this.dialogFormVisible = true
        } else {
          this.$router.push({
            path: './info/' + id
          })
        }
      })
    },
    checkAudit() {
      veridentity().then(response => {
        this.verList = response.list
        if (response.status === 1) {
          this.dialogFormVisible = true
        } else {
          this.auditLevel = response.auditLevel
          this.getList()
        }
      })
    },
    seeAudit(id) {
      this.$router.push({
        path: './info/' + id
      })
    },
    statusFor(row, column, cellValue, index) {
      return this.audit_type[cellValue]
    },
    verAudit(dataForm) {
      const that = this
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          if (!that.verInfoDialog.isSendCode) {
            Message({
              message: '请先发送验证码',
              type: 'error',
              duration: 5 * 1000
            })
          } else if (!(that.verInfo.verifyCode && that.verInfo.verifyCode !== '')) {
            Message({
              message: '请输入验证码',
              type: 'error',
              duration: 5 * 1000
            })
          } else if (!that.verInfo.pharmacistName) {
            Message({
              message: '请先关联药师',
              type: 'error',
              duration: 5 * 1000
            })
          } else {
            setidentity(this.verInfo).then(response => {
              this.dialogFormVisible = false
              this.getList()
            })
          }

        }
      })
    },
    beforeRouteEnter(to, from, next) {
      next(function(vm) {
        vm.checkAudit()
      })
    },
    cistChange(value) {
      console.log(value)
      for (let i = 0; i < this.verList.length; i++) {
        if (this.verList[i].id === value) {
          if (this.verList[i].authStatus === 1) {
            this.showPhone = false
          } else {
            this.showPhone = true
            this.phone = this.verList[i].phone
          }
          break
        }
      }
    },
    getCode() {
      const that = this
      const params = {}
      if (!(that.verInfo.phone && that.verInfo.phone !== '')) {
        Message({
          message: '请输入手机号',
          type: 'error',
          duration: 5 * 1000
        })
        return
      }
      that.verInfoDialog.disabled = true
      params.phone = that.verInfo.phone
      getAuthCode(params).then(res => {
        that.verInfoDialog.isSendCode = true
        that.verInfoDialog.msg = '60s'
        let time = 60
        const intval = window.setInterval(() => {
          time--
          that.verInfoDialog.msg = time + 's'
          if (time === 0) {
            that.verInfoDialog.disabled = false
            that.verInfoDialog.msg = '获取验证码'
            window.clearInterval(intval)
          }
        }, 1000)
      }).catch(e => {
        that.verInfoDialog.disabled = false
      })
    },
    setPharmacist() {
      const params = {}
      if (!this.verInfo.phone || !this.verInfo.verifyCode) {
        Message({
          message: '请输入手机号和验证码',
          type: 'error',
          duration: 5 * 1000
        })
        return false
      }
      params.phone = this.verInfo.phone
      params.verifyCode = this.verInfo.verifyCode
      getPharmacist(params).then(response => {
        this.verInfo.pharmacistName = response.name
        this.verInfo.cistId = response.id
      }).catch(e => {

      }).finally(() => {

      })
    },
    // 获取二维码
    getQrCode() {
      const params = {
        uuid: window.sessionStorage.getItem('uuid')
      }
      getQrCode(params).then(response => {
        if (response) {
          window.sessionStorage.setItem('status', response.status)
          // window.sessionStorage.setItem('uuid',response.uuid)
          window.sessionStorage.setItem('value', response.value)
          const canvas = document.getElementById('canvas')
          qrcode.toCanvas(canvas, response.value, { width: 200, height: 200 }, error => {
            if (error) {
              // this.$message.error('加载失败')
            }
          })
          if (response.status === 3) {
            this.status = response.status
            this.part = true
            this.uuid = window.sessionStorage.getItem('uuid')
            this.value = window.sessionStorage.getItem('value')
            console.log('3=', this.status, this.value, this.uuid)
            window.sessionStorage.removeItem('uuid')
            clearInterval(this.timer)
          }
          if (response.status === 2) {
            this.status = response.status
            window.sessionStorage.setItem('uuid', response.uuid)
            this.uuid = window.sessionStorage.getItem('uuid')
            this.value = window.sessionStorage.getItem('value')
            console.log('2=', this.status, this.value, this.uuid)
          }
          if (response.status === 1) {
            this.status = response.status
            this.part = false
            this.uuid = window.sessionStorage.getItem('uuid')
            this.value = window.sessionStorage.getItem('value')
            console.log('1=', this.status, this.value, this.uuid)
            this.getList()
            clearInterval(this.timer)
          }
        } else {
          this.$message.error(response.msg)
        }
      })
    },
    // 刷新二维码
    refurbishCode() {
      this.status = window.sessionStorage.getItem('status')
      this.uuid = window.sessionStorage.getItem('uuid')
      this.value = window.sessionStorage.getItem('value')
      this.timer = window.setInterval(() => {
        setTimeout(this.getQrCode(), 0)
      }, 2000)
    },
    // 解除绑定
    removeBind() {
      this.$confirm('是否解除绑定？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeBind().then(response => {
          window.sessionStorage.removeItem('status')
          this.$message.success('操作成功')
          this.part = true
          this.getQrCode()
          this.status = window.sessionStorage.getItem('status')
          this.uuid = window.sessionStorage.getItem('uuid')
          this.value = window.sessionStorage.getItem('value')
          this.timer = window.setInterval(() => {
            setTimeout(this.getQrCode(), 0)
          }, 2000)
        })
      })
    }
  }
}
</script>
