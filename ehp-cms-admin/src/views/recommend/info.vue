<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :inline="true"
      label-width="150px"
      class="demo-form-inline"
    >
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="处方笺编号:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.serialNumber }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="医生姓名:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.doctorName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="医生电话:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.doctorPhone }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者姓名:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.patientName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者性别:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ genders[data.patientGender] }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者年龄:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.patientAgeStr }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item
            class="elbottom"
            label="患者电话:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.patientAge/1 < 15 ? '无' : data.patientPhone }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            class="elbottom"
            label="临床诊断:"
            label-width="100px"
            prop="brandName"
            style="display:block;"
          >
            <div>{{ data.diagnosis }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="title">药品信息</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table
            :key="tableKey"
            :data="data.skus"
            fit
            highlight-current-row
          >
            <el-table-column
              label="skuID"
              prop="skuId"
              width="100px"
              align="center"
            />
            <el-table-column
              label="sku编码"
              prop="skuNumber"
              width="150px"
              align="center"
            />
            <el-table-column
              label="sku名称"
              prop="name"
              align="center"
            />
            <el-table-column
              label="数量"
              width="100px"
              align="center"
            >
              <template slot-scope="{row}">{{ row.quantity }}{{ row.quantityUnit }}</template>
            </el-table-column>
            <el-table-column
              label="用法用量"
              prop="usages"
              width="230px"
              align="center"
            />
            <el-table-column
              label="备注"
              prop="remark"
              width="160px"
              align="center"
            />
          </el-table>
        </el-col>
      </el-row>
      <div class="title">处方单预览</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table
            :key="tableKey"
            :data="data.files"
            fit
            highlight-current-row
          >
            <el-table-column
              label="处方单图片名称"
              prop="name"
              width="180"
              align="center"
            />
            <el-table-column
              label="处方单地址"
              prop="url"
              align="center"
            />
            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="80"
            >
              <template slot-scope="{row}">
                <el-button
                  type="primary"
                  size="mini"
                  @click="viewFile(row.url)"
                >
                  <span>查看</span>
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-col>
      </el-row>
      <el-row
        style="margin-top:30px"
        :gutter="20"
      >
        <el-col :span="12">
          <!-- <el-button
            v-permission="['medication:recom:audit']"
            type="primary"
            @click="handleShowAudit"
          >提交审核</el-button> -->
          <el-button
            type="primary"
            :disabled="data.invalid==1 || data.audit.status == 1 || data.audit.status == 2"
            @click="handleShowAudit"
          >提交审核</el-button>
        </el-col>
      </el-row>
      <div class="title">操作日志</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-table
            :key="tableKey"
            :data="list.list"
            fit
            highlight-current-row
          >
            <el-table-column
              label="操作人"
              prop="createdBy"
              width="100px"
              align="center"
            />
            <el-table-column
              label="操作时间"
              prop="createdAt"
              width="140px"
              align="center"
            />
            <el-table-column
              label="操作内容"
              prop="remark"
              align="center"
            />
          </el-table>
          <pagination
            v-show="list.totalCount > 0"
            :total="list.totalCount"
            :page.sync="listQuery.pageNo"
            :limit.sync="listQuery.pageSize"
            @pagination="getAuditLogs"
          />
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
      title="处方审核"
      :visible.sync="passdialog"
      append-to-body
    >

      <el-form
        ref="auditStatusRules"
        :model="auditStatus"
        :rules="auditStatusRules"
        label-position="right"
        label-width="150px"
        style="width:90%"
      >

        <el-form-item
          label="审核状态"
          prop="status"
        >

          <el-select
            v-model="auditStatus.status"
            placeholder="请选择审核状态"
          >
            <el-option
              v-for="item in auditType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
        </el-form-item>
        <el-form-item
          v-if="auditStatus.status == 2"
          label="审核原因"
          prop="remark"
        >
          <el-input
            v-model="auditStatus.remark"
            type="textarea"
            :rows="2"
            maxlength="200"
            placeholder="请输入内容"
          ></el-input>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="passdialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleStatusData()"
        >确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="填写密码"
      :visible.sync="singdialog"
      width="30%"
      append-to-body
    >

      <el-input
        v-model="auditStatus.signPwd"
        type="password"
        placeholder="请输入签名密码"
      ></el-input>
      <div slot="footer">
        <el-button @click="singdialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmPwd"
        >确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="查看图片"
      :visible.sync="image.isOpen"
      width="70%"
      :before-close="handleClose"
      class="img_view"
    >
      <el-image
        :src="image.src"
        fit="cover"
      ></el-image>
    </el-dialog>
    <el-dialog
      title="E签宝意愿签署页面"
      :visible.sync="dialogSignedVisible"
      width="70%"
      :show-close="false"
      @close="qmOnClose"
    >
      <div class="dialog-content">
        <iframe :src="qm" frameborder="0" width="100%" height="600px"></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="closeQm">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.title {
  padding-bottom: 10px;
  margin-top: 40px;
}
.tips {
  display: inline-block;
  margin-left: 10px;
}
.el-dialog__footer {
  padding-right: 20%;
}
.labelitem .el-form-item {
  display: flex;
}
.labelitem .el-form-item__content {
  flex: 1;
}
.el-dialog__footer {
  padding-right: 20px;
}
.img_view {
  text-align: center;
}
</style>
<script>
import recommendApi from '@/api/recommend/audit'
import { Message } from 'element-ui'
export default {
  name: '',
  filters: {},
  components: {},
  data() {
    return {
      tableKey: 0,
      auditLevel: null,
      data: {
        audit: {}
      },
      auditType: [
        { value: 1, label: '审核通过' },
        { value: 2, label: '审核不通过' }
      ],
      genders: ['女', '男'],
      auditStatus: {
        signPwd: '',
        remark: ''
      },
      recomId: 0,
      remark: '',
      image: {
        isOpen: false,
        src: ''
      },
      list: {
        pageNo: 1,
        totalCount: 1
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      auditStatusRules: {
        status: [
          { required: true, message: '请选择审核状态', trigger: 'blur' }
        ],
        remark: [
          { required: true, message: '请填写审核原因', trigger: 'blur' }
        ]
      },
      pharmacistId: null,
      sigInfo: null,
      singdialog: false,
      passdialog: false,
      dialogSignedVisible: false,
      qm: '',
      uniqueId: ''
    }
  },
  created() {
    const params = {}
    if (this.$route.params.id) {
      params.recomId = this.$route.params.id
      this.recomId = params.recomId
      this.getAuditDetail(params)
    }
  },
  mounted() {
    window.parentClose = this.parentClose
  },
  beforeDestroy() {
    window.parentClose = null
  },
  methods: {
    getAuditDetail(params) {
      const that = this
      recommendApi.getAuditDetail(params).then(function(data) {
        if (data) {
          that.data = data
          console.log(that.data, '415')
          that.pharmacistId = data.pharmacistId
          that.getAuditLogs()
          that.getNoSecret()
        }
      })
    },
    checkAudit() {
      recommendApi.veridentity().then(response => {
        this.auditLevel = response.auditLevel
      })
    },
    handleClose() {
    },
    viewFile(src) {
      // const that = this
      // if (src.toLocaleUpperCase().indexOf('.PDF') === -1) {
      //   that.image.src = src
      //   that.image.isOpen = true
      // } else {
      window.open(src)
      // }
    },
    getAuditLogs() {
      this.listQuery.recomId = this.recomId
      recommendApi.getAuditLogs(this.listQuery).then(res => {
        this.list = res
        console.log(res, 452)
      })
    },
    getNoSecret() {
      const params = {}
      params.pharmacistId = this.pharmacistId
      recommendApi.getNoSecret(params).then(res => {
        this.sigInfo = res
        console.log(this.sigInfo, 470)
      }).catch(e => {
        new Error(e)
      })
    },
    // 确认签名密码
    async confirmPwd() {
      if (this.auditStatus.signPwd == '') {
        this.$message({
          message: '请输入签名密码！',
          type: 'error'
        })
        return
      }
      this.singdialog = false
      const res = await recommendApi.getSignPre({
        recomId: this.recomId,
        pharmacistId: this.pharmacistId
      })
      this.auditStatus.uniqueId = res.uniqueId
      this.handleSubmitAudit()
    },
    // 获取iframe的src
    getUrl() {
      const that = this
      const params = {}
      params.recomId = that.recomId
      recommendApi.getCode(params).then(res => {
        console.log(res, 507)
        that.qm = res.faceUrl
        this.auditStatus.uniqueId = res.uniqueId
      })
    },
    // 提交审核
    handleStatusData() {
      this.$refs['auditStatusRules'].validate(async valid => {
        if (valid) {
          if (!this.sigInfo.noSecret && this.auditStatus.status === 1) {
            const { seviceName } = await recommendApi.getServiceType()
            console.log(seviceName === '2', 518)
            if (seviceName === '2') {
              // 易签宝
              this.dialogSignedVisible = true
              this.getUrl()
            } else {
              // 四川CA
              this.singdialog = true
              this.passdialog = false
            }
          } else {
            console.log('2')
            this.passdialog = false
            const res = await recommendApi.getSignPre({
              recomId: this.recomId,
              pharmacistId: this.pharmacistId
            })
            this.auditStatus.uniqueId = res.uniqueId
            this.handleSubmitAudit()
          }
        }
      })
    },
    handleShowAudit() {
      this.resetTemp()
      this.passdialog = true
    },
    handleSubmitAudit() {
      this.auditStatus.recomId = this.recomId
      recommendApi.audit(this.auditStatus).then((res) => {
        console.log(res, '=======498=======')
        Message({
          message: '操作成功',
          type: 'success'
        })
        this.getAuditDetail({ recomId: this.recomId })
      })
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['auditStatusRules'].clearValidate()
        this.auditStatus = {}
      })
    },
    parentClose() {
      const that = this
      const params = {}
      params.status = that.auditStatus.status
      params.remark = that.auditStatus.remark
      params.recomId = that.recomId
      params.signPwd = this.auditStatus.uniqueId
      params.force = 0
      that.loading = true
      recommendApi.audit(params, function(res) {
        that.loading = false
        res = JSON.parse(res)
        if (res.code === 0) {
          res.data = null
        }
        return res
      })
        .then(function(data) {
          if (data === null) {
            that.dialogSignedVisible = false
            that.passdialog = false
            Message({
              message: '操作成功',
              type: 'success',
              duration: 5 * 1000
            })
            that.getAuditDetail({ recomId: that.recomId })
          }
        }).finally(function() {
          that.loading = false
          // that.verInfo.verpass = ''
        })
    },
    closeQm() {
      this.dialogSignedVisible = false
      this.passdialog = false
    },
    qmOnClose() {
      // this.resetPrescription()
    }
  }
}
</script>
