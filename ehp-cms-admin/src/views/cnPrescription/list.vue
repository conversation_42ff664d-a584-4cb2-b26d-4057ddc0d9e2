<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input v-model="listQuery.name" placeholder="方剂" clearable class="filter-item" style="width: 120px;" @keyup.enter.native="handleFilter" />
      <el-button v-permission="['tcm:recipe:list']" type="primary" icon="el-icon-search" @click="handleFilter">搜索</el-button>
      <el-button v-permission="['tcm:recipe:list']" type="primary" icon="el-icon-refresh" @click="handleReset">重置</el-button>
      <el-button v-permission="['tcm:recipe:save']" type="primary" icon="el-icon-plus" @click="handleCreate">添加方剂</el-button>
    </div>
    <el-table :key="tableKey" :data="list" fit highlight-current-row>
      <el-table-column label="ID" prop="id" align="center" width="50px" />
      <el-table-column label="方剂名称" prop="name" width="120px" align="center">
        <template slot-scope="scope">
          <el-button v-permission="['tcm:recipe:list']" type="text" size="mini" @click="$router.push('/CN/info/' + scope.row.id)">{{ scope.row.name }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="适应症" prop="indications" align="center" min-width="200px" />
      <el-table-column label="处方" prop="itemStr" width="300px" align="center" />
      <el-table-column label="方剂类别" prop="categoryDescribe" width="100px" align="center" />
      <el-table-column label="操作" fixed="right" align="center" width="180px">
        <template slot-scope="{row}">
          <el-button v-permission="['tcm:recipe:update']" type="primary" @click="editProduct(row.id)">编辑</el-button>
          <el-button v-permission="['tcm:recipe:delete']" type="primary" @click="delProduct(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="listQuery.pageNo" :limit.sync="listQuery.pageSize" @pagination="getList" />
    <el-dialog :title="productData.id? '编辑' : '添加'" :visible.sync="dialogFormVisible" width="800px">
      <el-form ref="dataForm" :model="productData" :rules="rules" :inline="true" label-width="120px" class="demo-form-inline">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="方剂名称" prop="name">
              <el-input v-model="productData.name" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="方剂类别" prop="category">
              <DictSelect v-model="productData.category" placeholder="请选择" type="tcm-recipe-category" @change="categorySelect" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="方剂来源" prop="source">
              <el-input v-model="productData.source" clearable />
            </el-form-item>
          </el-col>
          <el-col v-if="priceShow" :span="12">
            <el-form-item label="协定方剂价格" prop="price">
              <el-input v-model="productData.price" oninput="value=value.replace(/^\.+|[^\d.]/g,'')" clearable @blur="priceChange" />
            </el-form-item>
            <span style="line-height:30px">元/剂</span>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24" class="col_input">
            <el-form-item label="适应症" prop="indications">
              <el-input v-model="productData.indications" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24" class="col_input">
            <el-form-item label="用法" prop="usageDosage">
              <el-input v-model="productData.usageDosage" type="textarea" :autosize="autosize" width="50%" placeholder />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24" class="col_input">
            <el-form-item label="治法" prop="treatment">
              <el-input v-model="productData.treatment" clearable type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24" class="col_input">
            <el-form-item label="书籍" prop="document">
              <el-input v-model="productData.document" clearable type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24" class="col_input">
            <el-form-item label="原文" prop="originText">
              <el-input v-model="productData.originText" clearable type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" class="labelitem">
          <el-col :span="24" class="col_input">
            <el-form-item label="药方原文" prop="originRecipe">
              <el-input v-model="productData.originRecipe" clearable type="textarea" :autosize="autosize" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="items">
              <span slot="label"><span style="color:red;">* </span>药方明细:</span>
              <el-autocomplete
                v-model="props.name"
                :fetch-suggestions="querySearchAsync"
                placeholder="请输入内容"
                @select="handleSelect"
              >
                <template slot-scope="{ item }">
                  <div class="name">{{ item.name }}</div>
                </template>
              </el-autocomplete>
            </el-form-item>
            <el-form-item label prop="items">
              <el-input v-model="props.sliceQuantity" clearable placeholder="输入用量" @keyup.native="formatPrice" @blur="sliceQuantityChange">
              </el-input>
            </el-form-item>
            <el-button @click="addAlias">添加</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table :key="tableKey1" :data="productData.items" fit highlight-current-row>
              <el-table-column label="ID" prop="sliceId" align="center" width="50px" />
              <el-table-column label="药材名称" prop="name" align="center" />
              <el-table-column label="用量( g )" prop="sliceQuantity" align="center" />
              <el-table-column label="操作" fixed="right" align="center" width="180px">
                <template slot-scope="{ $index }">
                  <el-button v-permission="['tcm:recipe:delete']" type="primary" @click="delProps($index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">返回</el-button>
        <el-button v-permission="['tcm:recipe:save']" type="primary" @click="nextCreate('dataForm')">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
.col_input /deep/ input {
  width: 100%;
}
.red {
  color: red;
}
.demo-form-inline /deep/.el-form-item__content{
  max-width: 246px;
}
.labelitem /deep/.el-form-item--mini.el-form-item{
  width: 100%;
}
.labelitem /deep/.el-form-item__content{
  width: 100%;
  max-width: 620px;
}
.labelitem /deep/.el-input{
  width: 100%;
}
</style>
<script>
import API from '@/api/cnPrescription/index'
import DictSelect from '@/components/DictSelect'
export default {
  name: '',
  filters: {},
  components: {
    DictSelect
  },
  data() {
    return {
      tableKey: 0,
      tableKey1: 1,
      list: null,
      total: 0,
      props: {
        name: '',
        sliceId: 0,
        sliceQuantity: ''
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        name: ''
      },
      dialogFormVisible: false,
      formLabelWidth: '25%',
      productData: {
        category: '',
        document: '',
        id: '',
        indications: '',
        items: [],
        name: '',
        originRecipe: '',
        originText: '',
        source: '',
        treatment: '',
        usageDosage: ''
      },
      rules: {
        name: [{ required: true, message: '请输入方剂名称', trigger: 'blur' }],
        category: [{ required: true, message: '请选择方剂类别', trigger: 'change' }],
        indications: [{ required: true, message: '请输入适应症', trigger: 'blur' }],
        price: [{ required: true, message: '请输入方剂价格', trigger: 'blur' }],
        items: [
          {
            validator: (rule, value, callback) => {
              if (this.productData.items.length <= 0) {
                callback('请添加药方明细')
              } else {
                callback()
              }
            },
            trigger: 'click'
          }
        ]
      },
      detail: [],
      autosize: { minRows: 2, maxRows: 20 },
      priceShow: false
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    // 获取数据
    getList() {
      API.list(this.listQuery).then(response => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleReset() {
      this.listQuery.name = ''
      this.handleFilter()
    },
    handleCreate() {
      this.dialogFormVisible = true
      this.priceShow = false
      this.clearForm()
    },
    nextCreate(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          API.upload(this.productData).then(res => {
            this.clearForm()
            this.dialogFormVisible = false
            this.getList()
            this.$message.success('操作成功')
          })
        }
      })
    },
    clearForm() {
      this.$nextTick(function() {
        this.productData.id = ''
        this.$refs.dataForm.resetFields()
        this.productData.items = []
        this.props = {
          name: '',
          sliceId: 0,
          sliceQuantity: ''
        }
      }, 1000)
    },
    editProduct(productId) {
      this.dialogFormVisible = true
      API.info(productId).then(res => {
        this.productData = res
        // eslint-disable-next-line eqeqeq
        if (this.productData.category == 4) {
          this.priceShow = true
        } else {
          this.priceShow = false
        }
        this.dialogFormVisible = true
      })
    },
    delProduct(productId) {
      this.$confirm('是删除此方剂？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        API.del(productId).then(res => {
          this.getList()
          this.$message.success('操作成功')
        })
      })
    },
    addAlias() {
      const that = this
      console.log(that.props)

      // eslint-disable-next-line eqeqeq
      if (that.props.sliceId && that.props.sliceQuantity && that.props.sliceQuantity != 0) {
        if (that.productData.items) {
          that.productData.items.push(that.props)
          this.props = {
            name: '',
            sliceId: 0,
            sliceQuantity: ''
          }
        }
      }
    },
    querySearchAsync(queryString, cb) {
      if (!queryString) return
      API.select({ name: queryString }).then(res => {
        cb(res)
      })
    },
    handleSelect(item) {
      const { props } = this
      props.name = item.name
      props.sliceId = item.id
    },
    delProps(idx) {
      this.$confirm('是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.productData.items.splice(idx, 1)
      })
    },
    categorySelect(e) {
      console.log(e)
      // eslint-disable-next-line eqeqeq
      if (e == 4) {
        this.priceShow = true
      } else {
        this.priceShow = false
      }
    },
    // （用量）只能输入数字
    sliceQuantityChange(e) {
      this.props.sliceQuantity = e.target.value
    },
    // （协定方价格）只能输入数字
    priceChange(e) {
      this.productData.price = e.target.value
    },
    // 只能输入正整数
    number() {
      this.props.sliceQuantity = this.props.sliceQuantity.replace(/[^\.\d]/g, '')
      this.props.sliceQuantity = this.props.sliceQuantity.replace('.', '')
    },
    formatPrice(e) {
      // 通过正则过滤小数点后两位
      e.target.value = e.target.value.match(/^\d*(\.?\d{0,6})/g)[0] || null
      this.props.sliceQuantity = e.target.value
    }
  }
}
</script>
