<template>
  <div class="app-container">
    <el-form
      ref="dataForm"
      :inline="true"
      label-width="150px"
      class="demo-form-inline"
    >
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">方剂名称</el-col>
        <el-col :span="18">{{ preInfo.name }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">方剂来源</el-col>
        <el-col :span="18">{{ preInfo.source }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">适应症</el-col>
        <el-col :span="18">{{ preInfo.indications }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">用法</el-col>
        <el-col :span="18">{{ preInfo.usageDosage }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">治法</el-col>
        <el-col :span="18">{{ preInfo.treatment }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">书籍</el-col>
        <el-col :span="18">{{ preInfo.document }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">原文</el-col>
        <el-col :span="18">{{ preInfo.originText }}</el-col>
      </el-row>
      <el-row :gutter="20" style="padding-top:10px;padding-bottom: 10px;">
        <el-col :span="3" class="label-info">药方明细</el-col>
        <el-col :span="18">{{ details }}</el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import API from '@/api/cnPrescription/index'
export default {
  name: '',
  data() {
    return {
      preInfo: {},
      preId: '',
      details: ''
    }
  },
  created() {
    this.preId = this.$route.params.id || ''
    if (this.preId) this.getInfo(this.preId)
  },
  methods: {
    getInfo(id) {
      const that = this
      let details = []
      API.info(id).then(data => {
        if (data) {
          that.preInfo = data
          details = data.items.map(item => `${item.name} ${item.sliceQuantity}克`)
          that.details = details.join('、')
        }
      })
    }
  }
}
</script>

<style scoped>
  .label-info{
    font-weight: bold;
  }

</style>
