<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        v-permission="['operation:banner:save']"
        type="primary"
        icon="el-icon-plus"
        @click="handleAdd"
      >添加
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="Banner序号"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column label="类型" prop="name" width="150" align="center">
        <template slot-scope="{ row }">
          {{ row.bannerType == 1 ? "图文" : "链接" }}
        </template>
      </el-table-column>
      <el-table-column label="标题" prop="title" min-width="150" align="center">
      </el-table-column>
      <el-table-column
        label="创建时间"
        prop="createdAt"
        width="150"
        align="center"
      />
      <el-table-column
        label="创建者"
        prop="createdBy"
        min-width="150"
        align="center"
      />
      <el-table-column label="状态" prop="name" width="150" align="center">
        <template slot-scope="{ row }">
          {{ row.enable == 0 ? "启用" : "停用" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template slot-scope="{ row }">
          <el-button
            v-if="row.enable == 1"
            v-permission="['operation:banner:enable']"
            type="primary"
            size="mini"
            @click="handleEnable(row.id, 0)"
          >启用</el-button>
          <el-button
            v-else
            v-permission="['operation:banner:enable']"
            size="mini"
            type="danger"
            @click="handleDisabled(row.id, 1)"
          >停用</el-button>
          <el-button
            v-permission="['operation:banner:edit']"
            type="primary"
            size="mini"
            @click="handleEdit(row.id)"
          >编辑信息
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="80%"
      top="2vh"
    >
      <el-form
        ref="dataForm"
        :model="formData"
        :rules="rules"
        label-position="right"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="类型" prop="bannerType">
              <el-select
                v-model="formData.bannerType"
                clearable
                placeholder="请选择类型"
                style="width:100%"
              >
                <el-option label="图文" :value="1"></el-option>
                <el-option label="链接" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标题" placeholder="请输入标题" prop="title">
              <el-input v-model="formData.title" clearable />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="banner图片" prop="bannerUrl">
              <uploadImage
                :value="formData.bannerUrl"
                action="/notice/message/upload"
                list-type="picture-card"
                :show-file-list="false"
                accept="image/png, image/jpeg, image/jpg"
                @success="onSuccess"
                @remove="onRemove"
              >
              </uploadImage>
              <div style="color:#999">比例尺寸：710 × 240像素</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="formData.bannerType == 1" label="视频ID">
              <el-input
                v-model="formData.videoId"
                type="textarea"
                :rows="3"
                placeholder="请输入视频ID"
              >
              </el-input>
            </el-form-item>
            <el-form-item
              v-if="formData.bannerType == 2"
              prop="url"
              label="地址链接"
            >
              <el-input
                v-model="formData.targetUrl"
                type="textarea"
                :rows="3"
                placeholder="请输入地址链接"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="formData.bannerType == 1" :gutter="20">
          <el-col :span="24">
            <el-form-item label="内容" prop="content">
              <wangEditor ref="editor" :text-value.sync="formData.content" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button v-waves @click="dialogVisible = false">取消</el-button>
        <el-button
          v-waves
          type="primary"
          @click="handlesubmit('dataForm')"
        >发布</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getList, add, update, detail, enable } from '@/api/operate/banner'
import { getToken, getTokenName } from '@/utils/auth'
import waves from '@/directive/waves'
import wangEditor from '@/components/wangEditor'
import uploadImage from '@/components/uploadImage'

export default {
  name: 'Banner',
  directives: { waves },
  components: {
    wangEditor,
    uploadImage
  },
  filters: {},
  data() {
    return {
      dialogVisible: false,
      dialogTitle: '上传',
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      formData: {},
      rules: {
        bannerType: [
          { required: true, message: '请选择类型', trigger: 'blur' }
        ],
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        bannerUrl: [
          { required: true, message: '请上传Banner图片', trigger: 'blur' }
        ],
        targetUrl: [
          { required: true, message: '请输入跳转地址', trigger: 'change' }
        ],
        content: [{ required: true, message: '请输入内容', trigger: 'blur' }]
      },
      uploadPath: '',
      headers: {}
    }
  },
  created() {
    this.headers[getTokenName()] = getToken()
    this.handleFilter()
  },
  methods: {
    getList() {
      getList(this.listQuery).then(response => {
        this.list = response
      })
    },
    handleFilter() {
      this.listQuery.pageNo = 1
      this.getList()
    },
    handleAdd() {
      this.dialogTitle = '新增'
      this.dialogVisible = true
      this.resetTemp()
    },
    handleEdit(id) {
      detail(id).then(response => {
        response.bannerUrl = [{ url: response.bannerUrl }]
        this.formData = response
        this.$nextTick(() => {
          this.$refs.editor.setValue(this.formData.content)
        })
        this.dialogTitle = '编辑'
        this.dialogVisible = true
      })
    },
    handlesubmit(dataForm) {
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          if (this.formData.id) {
            this.update()
          } else {
            this.save()
          }
        } else {
          this.$message.error('请检查表单是否填写完整')
        }
      })
    },
    save() {
      const params = Object.assign({}, this.formData)
      params.bannerUrl = params.bannerUrl.map(i => i.url).join()
      add(params).then(res => {
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    update() {
      const params = Object.assign({}, this.formData)
      params.bannerUrl = params.bannerUrl.map(i => i.url).join()
      update(params).then(res => {
        this.$message({
          message: '修改成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
        this.dialogVisible = false
      })
    },
    enable(id, status) {
      enable({
        id,
        status
      }).then(res => {
        this.$message({
          message: '成功',
          type: 'success',
          duration: 5 * 1000
        })
        this.getList()
      })
    },
    handleEnable(id, status) {
      this.$confirm('是否启用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.enable(id, status)
      })
    },
    handleDisabled(id, status) {
      this.$confirm('是否停用？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.enable(id, status)
      })
    },
    onSuccess(data) {
      this.formData.bannerUrl = []
      this.formData.bannerUrl.push({
        url: data.response.data,
        uid: data.uid
      })
      console.log(this.formData.bannerUrl, 'onSuccess', 278)
    },
    onRemove(data) {
      this.formData.bannerUrl = data
      console.log(this.formData.bannerUrl, 'onRemove', 301)
    },
    resetTemp() {
      this.$nextTick(() => {
        this.formData = {
          id: '',
          content: '',
          bannerUrl: [],
          bannerType: ''
        }
        this.$refs.editor.setValue('')
        this.$refs['dataForm'].clearValidate()
      })
    }
  }
}
</script>

<style></style>
