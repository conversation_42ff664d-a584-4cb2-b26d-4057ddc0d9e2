<template>
  <div class="app-container">
    <div class="filter-container">
      <el-button
        type="primary"
        @click="handleAdd"
      >添加
      </el-button>
    </div>
    <el-table
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column
        label="类型"
        prop="name"
        width="150"
        align="center"
      />

      <el-table-column
        label="创建时间"
        prop="createdAt"
        align="center"
      />
      <el-table-column
        label="操作"
        width="360"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handleEdit(scope.row)"
          >编辑
          </el-button>

          <el-button
            type="danger"
            size="mini"
            @click="handleDel(scope.row.id)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="fetchData"
    />
    <el-dialog
      :title="type==='add'?'添加类型':'修改类型'"
      :visible.sync="dialogAddForm"
      width="30%"
      :show-close="true"
      @close="handleClose"
    >
      <el-form
        ref="dataForm"
        :model="formInfo"
        :rules="rules"
      >
        <el-form-item
          label="类型名称："
          prop="name"
        >
          <el-input
            v-model="formInfo.name"
            placeholder="请输入类型名称"
            maxlength="10"
            clearable
            style="width:150px"
            @input="onInput($event)"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="handleSave"
        >保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { formTypeAdd, formTypeList, formTypeDelete, formTypeEdit } from '@/api/from/index'
import { Toast } from 'vant'
export default {
  name: 'FormType',
  data() {
    return {
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      dialogAddForm: false,
      rules: {
        name: [{ required: true, message: '请输入类型名称', trigger: 'blur' }]
      },
      forms: [],
      formInfo: {
        name: ''
      },
      type: 'add'
    }
  },
  created() {
    this.fetchData()
  },
  methods: {
    handleAdd() {
      this.type = 'add'
      this.dialogAddForm = true
      this.formInfo = {
        name: ''
      }
    },
    handleClose() {
      this.dialogAddForm = false
    },
    handleEdit(data) {
      this.type = 'edit'
      this.dialogAddForm = true
      this.formInfo.id = data.id
      this.formInfo.name = data.name
    },
    onInput(val) {
      this.$forceUpdate()
    },
    fetchData() {
      formTypeList().then((res) => {
        this.list = res.list
        this.total = res.totalCount
      })
    },
    handleSave() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.type === 'add') {
            formTypeAdd(this.formInfo).then((res) => {
              this.dialogAddForm = false
              this.fetchData()
              Toast({
                message: '添加成功',
                icon: 'success'
              })
            })
          } else {
            formTypeEdit(this.formInfo).then((res) => {
              this.dialogAddForm = false
              this.fetchData()
              Toast({
                message: '修改成功',
                icon: 'success'
              })
            })
          }

        }
      })
    },
    handleDel(id) {
      this.$confirm('确定删除当前表单类型吗，删除后将无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }) .then(() => {
        return formTypeDelete(id)
      }).then(() => {
        Toast({
          message: '删除成功',
          icon: 'success'
        })
        this.fetchData()
      })

    }

  }
}
</script>
