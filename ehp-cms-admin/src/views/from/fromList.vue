<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="请输入表单标题"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DictSelect
        v-model="listQuery.type"
        placeholder="表单属性"
        type="follow_up_form_type"
        style="width: 150px;"
        api="/followup/formType/allList"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        v-waves
        type="primary"
        icon="el-icon-refresh"
        @click="handleReset"
      >重置</el-button>
      <el-button
        type="primary"
        @click="handleAddForm"
      >新增表单
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column
        label="表单名称"
        prop="name"
        width="150"
        align="center"
      />
      <el-table-column
        label="创建人"
        prop="createdBy"
        width="150"
        align="center"
      />
      <el-table-column
        label="开启/关闭"
        prop="available"
        width="150"
        align="center"
      >
        <template slot-scope="{row}">
          <el-tag
            v-if="row.status == 0"
            type="success"
          >开启</el-tag>
          <el-tag
            v-else
            type="danger"
          >关闭</el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="表单属性"
        prop="typeDescribe"
        width="150px"
        align="center"
      />

      <el-table-column
        label="创建时间"
        prop="createdAt"
        align="center"
      />
      <el-table-column
        label="操作"
        width="360"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="primary"
            size="mini"
            @click="handlookForm(scope.row)"
          >浏览
          </el-button>
          <el-button
            v-if="scope.row.isself == 1 && scope.row.status != 0"
            type="primary"
            size="mini"
            @click="handCompile(scope.row)"
          >编辑
          </el-button>
          <el-button
            v-if="scope.row.isself == 1 && scope.row.status == 0"
            size="mini"
            type="danger"
            @click="handleSetStatus(scope.row.id,1)"
          >停用</el-button>
          <el-button
            v-if="scope.row.isself == 1 && scope.row.status == 1"
            type="primary"
            size="mini"
            @click="handleSetStatus(scope.row.id,0)"
          >启用</el-button>
          <el-button
            v-if="scope.row.isself == 1 && scope.row.status != 0"
            type="danger"
            size="mini"
            @click="handleDel(scope.row.id)"
          >删除</el-button>
          <el-button
            type="primary"
            size="mini"
            @click="handlookDetail(scope.row)"
          >统计
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />

    <el-dialog
      title="定义表单属性"
      :visible.sync="dialogAddForm"
      width="30%"
      :show-close="true"
      @close="handleClose"
    >
      <el-form
        ref="dataForm"
        :model="fromInfo"
        :rules="rules"
      >
        <el-form-item
          label="表单名称："
          prop="name"
        >
          <el-input
            v-model="fromInfo.name"
            placeholder="请输入表单名称"
            maxlength="20"
            clearable
            style="width:150px"
            @input="onInput($event)"
          />
        </el-form-item>
        <el-form-item
          label="表单类型："
          prop="type"
        >
          <DictSelect
            v-model="fromInfo.type"
            style="width:150px"
            placeholder="表单属性"
            type="follow_up_form_type"
            api="/followup/formType/allList"
          />
        </el-form-item>
        <el-form-item label="表单归属：">
          <el-checkbox
            v-model="fromInfo.checked"
            :disabled="true"
          >所有医生共享</el-checkbox>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="handleNext"
        >下一步</el-button>
      </div>
    </el-dialog>
    <el-dialog
      id="iframeDialog"
      :close-on-click-modal="false"
      title="预览"
      :visible.sync="dialogPreForm"
      width="400"
      top="5px"
    >
      <div id="completeDiv">
        <prevform
          :forms="forms"
          :is-disabled="isDisabled"
          :formtitle="formtitle"
          :describe="describe"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import waves from '@/directive/waves'
import Prevform from '@/components/form_preview/prevform'
import DictSelect from '@/components/DictSelect'
import { getList, getDrawing, delForm, setStatus } from '@/api/from/index'
export default {
  name: 'DoctorTable',
  components: {
    DictSelect,
    Prevform
  },
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10
      },
      fromInfo: {
        checked: true
      },
      dialogPreForm: false,
      dialogAddForm: false,
      rules: {
        type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        name: [{ required: true, message: '请输入表单名称', trigger: 'blur' }]
      },
      isDisabled: 'see',
      forms: [],
      isTitle: null,
      formtitle: '',
      describe: '',
      dialogForm: false
    }
  },
  created() {
    this.handleFilter()
  },
  methods: {
    getDetail(formId) {
      getDrawing(formId).then((response) => {
        this.forms = response
        this.dialogPreForm = true
      })
    },
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.getList()
    },
    // 浏览
    handlookForm(item) {
      this.getDetail(item.formId)
    },
    handlookDetail(data) {
      const { id } = data
      this.$router.push({
        path: './fromDetail?id=' + id
      })
    },
    // 新增
    handleAddForm() {
      console.log(this.$store)
      this.dialogAddForm = true
      this.resetTemp()
    },
    handleNext() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (!this.fromInfo.name) {
            this.$message({
              type: 'error',
              message: '请输入表单名称!'
            })
            return
          }
          if (!this.fromInfo.type) {
            this.$message({
              type: 'error',
              message: '请选择类型!'
            })
            return
          }
          const url = this.fromInfo.id
            ? `./addform?name=${this.fromInfo.name}&type=${this.fromInfo.type}&id=${this.fromInfo.id}`
            : `./addform?name=${this.fromInfo.name}&type=${this.fromInfo.type}`
          this.$router.push({
            path: url
          })
        }
      })
    },
    // 修改状态
    handleSetStatus(id, status) {
      console.log(status, 276)

      this.$confirm(
        status === 0
          ? '表单开启后不可进行编辑和删除操作，但医生可选择使用！'
          : '表单关闭后可进行编辑和删除操作，但医生无法选择使用！',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          this.setForm(id, status)
        })
        .catch(() => {
          console.log('取消')
        })
    },
    // 编辑
    handCompile(item) {
      this.dialogAddForm = true
      this.fromInfo.id = item.id
      this.fromInfo.name = item.name
      this.fromInfo.type = item.type
      this.fromInfo.checked = true
      console.log('编辑')
    },
    // 删除
    handleDel(id) {
      this.$confirm('确定删除当前随访表单，删除后将无法恢复！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.delForm(id)
        })
        .catch(() => {
          console.log('取消')
        })
    },
    handleReset() {
      this.listQuery = {
        pageNo: 1,
        pageSize: 10
      }
      this.handleFilter()
    },
    setForm(id, status) {
      setStatus({ id, status }).then((response) => {
        this.getList()
        this.$message({
          type: 'success',
          message: '修改成功!'
        })
      })
    },
    delForm(id) {
      delForm({ id: id }).then((response) => {
        this.getList()
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      })
    },
    onInput(val) {
      this.$forceUpdate()
    },
    handleClose() {
      this.resetTemp()
    },
    resetTemp() {
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
        this.fromInfo = {
          checked: true
        }
      })
    }
  }
}
</script>
<style scoped>
.completeDiv {
  position: relative;
}
.shade {
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}
</style>
