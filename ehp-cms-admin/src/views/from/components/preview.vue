<template>
  <div class="main__wrapper">
    <el-container>
      <el-main :style="cssProps">
        <div class="wrapper--forms preview__wrapper">
          <div
            v-for="(form, index) in forms"
            :key="index"
            v-bind="form"
            class="form__group"
          >
            <label v-show="form.hasOwnProperty('label')" class="form__label">{{ index+1 }}、{{ form.text }}</label>

            <component
              :is="form.fieldType"
              :current-field="form"
              class="form__field"
            >
            </component>

            <small
              v-show="form.isHelpBlockVisible"
              class="form__helpblock"
            >
              {{ form.helpBlockText }}
            </small>
          </div>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { FormBuilder } from '@/components/form_elements/formbuilder'

export default {
  name: 'Publish',
  store: ['themingVars'],
  components: FormBuilder.$options.components,
  props: {
    forms: {
      type: Array,
      default: () => { return [] }
    }
  },
  computed: {

    cssProps() {
      // Return an object that will generate css properties key
      // to match with the themingVars
      //
      // Example output: { '--theme-primary-color': this.themingVars.primaryColor }
      var result = {},
        themingVars = this.themingVars

      for (var v in themingVars) {
        if (themingVars.hasOwnProperty(v)) {
          var newV = '--theme-' + _.kebabCase(v),
            suffix = ''

          // Add px to the value if the default value contain 'px'
          if (_.includes(newV, 'size')) suffix = 'px'
          else if (_.includes(newV, 'margin')) suffix = 'px'
          else if (_.includes(newV, 'radius')) suffix = 'px'

          result [newV] = themingVars[v] + suffix
        }
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.main__wrapper .form__field{
	display: flex;
	flex-direction: column;
	margin: 10px 0;
};
</style>
