<template>
  <el-form
    ref="dataForm"
    :model="formData"
    :rules="rules"
    :inline="true"
    size="mini"
    label-width="150px"
    class="mt20"
  >
    <!-- <el-form-item style="display:flex" label="类型" prop="type">
      <el-radio-group v-model="formData.type" :disabled="formData.publishStatus===1">
        <el-radio :label="0">调查</el-radio>
        <el-radio :label="1">评估</el-radio>
      </el-radio-group>
    </el-form-item> -->
    <el-form-item style="display:flex" label="分类" prop="categoryId">
      <itemize ref="itemize" v-model="formData.categoryId" type="3" />
    </el-form-item>
    <el-form-item style="display:flex" label="标题" prop="title">
      <el-input v-model="formData.title" clearable />
    </el-form-item>
    <el-form-item style="display:flex" label="描述" prop="description">
      <el-input v-model="formData.description" type="textarea" :rows="3" clearable />
    </el-form-item>
    <el-form-item style="display:flex" label=" ">
      <el-button type="primary" @click="saveForm('dataForm')">保存</el-button>
    </el-form-item>
  </el-form>
</template>
<style lang="css" scoped>
.el-form-item--mini.el-form-item{
  margin-bottom: 18px;
}
</style>
<script>
// import { postQuestion, editQuestion } from '@/api/form/question'
// import { selectCategory } from '@/api/classify'
// import itemize from '@/views/components/itemize'

export default {
  // components: { itemize },
  props: {
    formData: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      categoryList: [],
      rules: {
        type: [{ required: true, message: '请选择类型', trigger: 'change' }],
        categoryId: [{ required: true, message: '请选择分类', trigger: 'change' }],
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }]
      }
    }
  },
  mounted() {
    console.log(this.formData)
    this.getCategory()
  },
  methods: {
    saveForm(dataForm) {
      this.formData.type = 0
      this.$refs[dataForm].validate(valid => {
        if (valid) {
          if (this.formData.id) {
            editQuestion(this.formData).then(res => {
              this.$message({
                message: '编辑成功',
                type: 'success'
              })
              this.$emit('func', { name: 'second' })
            })
          } else {
            // this.formData.publishStatus = 0
            // this.formData.storeType = 0
            // this.formData.questions = []
            postQuestion(this.formData).then(res => {
              const query = this.$route.query
              this.$router.push({
                path: '/form/addform/' + res,
                query: {
                  ...query,
                  tabs: 'second'
                }
              })
            })
          }
        }
      })
    //   this.$router.push('/form/addform/1')
    },
    goBack() {
      this.$router.go(-1)
    },
    getCategory() {
      selectCategory(6).then(res => {
        this.categoryList = res
      })
    }
  }
}
</script>
