<template>
  <div class="">
    <el-dialog
      title="创建任务"
      :visible.sync="createdTaskDialog"
      width="70%"
      :show-close="true"
    >
      <el-form
        ref="dataForm"
        :model="fromData"
        :rules="rules"
      >
        <el-form-item
          label="关联医生："
          prop="doctor"
        >
          <el-select
            v-model="fromData.doctor"
            clearable
            style="width:150px"
            placeholder="请选择医生"
          >
            <el-option
              v-for="item in fromData.doctorList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-button
          type="primary"
          @click="addTaskDialog = true"
        >添加患者</el-button>
        <el-table
          :data="list"
          fit
          highlight-current-row
          style="width: 100%"
        >
          <el-table-column
            label="序号"
            fixed
            prop="id"
            width="90"
            align="center"
          />
          <el-table-column
            label="患者名称"
            prop="fromName"
            width="150"
            align="center"
          />
          <el-table-column
            label="年龄"
            prop="parentName"
            width="150"
            align="center"
          />
          <el-table-column
            label="性别"
            prop="available"
            width="150"
            align="center"
          >
            <template slot-scope="{row}">
              {{ row.val?'开启':'关闭' }}
            </template>
          </el-table-column>

          <el-table-column
            label="出生日期"
            prop="fromTyep"
            width="150px"
            align="center"
          >
            <template slot-scope="{row}">
              {{ fromType[row.fromTyep].label }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            prop="createdAt"
            align="center"
          />
        </el-table>
        <el-form-item
          style="margin-top:10px"
          label="任务名称："
          prop="name"
        >
          <el-input
            v-model="fromData.name"
            placeholder="请输入表单名称"
            maxlength="6"
            clearable
            style="width:150px"
          />
        </el-form-item>
        <el-form-item
          label="任务类型："
          prop="type"
        >
          <el-select
            v-model="fromData.type"
            clearable
            style="width:150px"
            placeholder="任务类型"
          >
            <el-option
              v-for="item in fromData.taskList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="关联表单："
          prop="startTime"
        >
          <el-cascader
            v-model="fromData.parentId"
            :options="fromList"
            :props="props"
            clearable
          />
        </el-form-item>
        <el-form-item
          label="随访时间范围："
          prop="startTime"
        >
          <DatePicker
            gte="startTime"
            lte="endTime"
            start-placeholder="任务开始时间"
            end-placeholder="任务结束时间"
            :query-model="fromData"
            class="filter-item"
            style="width: 230px"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="handleSaveTask"
        >确定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :visible.sync="addTaskDialog"
      width="60%"
      append-to-body
    >
      <div class="filter-container">
        <el-input
          v-model="listQuery.name"
          clearable
          maxlength="10"
          placeholder="请输入患者姓名"
          style="width: 150px"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.gender"
          clearable
          style="width: 150px"
          placeholder="请选择性别"
          @change="handleFilter"
        >
          <el-option
            v-for="item in gender"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <DatePicker
          gte="startTime"
          lte="endTime"
          start-placeholder="开始出生日期"
          end-placeholder="结束出生日期"
          :query-model="listQuery"
          class="filter-item"
          style="width: 230px"
        />
        <el-button
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >搜索</el-button>
        <el-button
          type="primary"
        >重置
        </el-button>
        <el-table
          :data="list"
          fit
          highlight-current-row
          style="width: 100%"
          @selection-change="selectionChange"
        >
          <el-table-column type="selection" align="center" width="50px"></el-table-column>
          <el-table-column label="序号" prop="orderSn" width="140px" align="center" />
          <el-table-column label="患者姓名" fixed prop="recomId" width="180px" align="center" />
          <el-table-column label="年龄" prop="serialNumber" width="150px" align="center" />
          <el-table-column label="出生日期" prop="userId" align="center" />
        </el-table>

      </div>
      <div slot="footer">
        <el-button @click="addTaskDialog = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleSavePatient"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import DatePicker from '@/components/DatePicker'
export default {
  components: { DatePicker },
  data() {
    return {
      list: [],
      fromData: {},
      fromList: [],
      createdTaskDialog: false,
      addTaskDialog: false,
      props: {
        value: 'id',
        emitPath: false,
        label: 'name',
        checkStrictly: true
      },
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderBy: 'desc'
      },
      gender: [
        {
          value: 0,
          label: '女'
        },
        {
          value: 1,
          label: '男'
        }
      ],
      rules: {
        type: [{ required: true, message: '请选择任务类型', trigger: 'blur' }],
        doctor: [
          { required: true, message: '请选择关联医生', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        startTime: [
          { required: true, message: '请选择随访时间', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleCreatedTaskShow() {
      this.createdTaskDialog = true
    },
    handleSavePatient() {
      this.addTaskDialog = false
      console.log('保存选中患者')
    },
    handleSaveTask() {
      this.createdTaskDialog = false
      console.log('保存随访任务')
    },
    handleFilter() {

    },
    selectionChange(val) {

    }
  }
}
</script>
<style>
</style>
