<template>
  <div class="app-container">
    <div class="filter-container">
      <el-input
        v-model="listQuery.name"
        clearable
        placeholder="请输入任务名称"
        style="width: 150px"
        @keyup.enter.native="handleFilter"
      />
      <DatePicker
        gte="startTime"
        lte="endTime"
        start-placeholder="任务开始时间"
        end-placeholder="任务结束时间"
        :query-model="listQuery"
        class="filter-item"
        style="width: 230px"
        @change="handleFilter"
      />
      <el-button
        v-waves
        type="primary"
        icon="el-icon-search"
        @click="handleFilter"
      >搜索</el-button>
      <el-button
        type="primary"
      >重置
      </el-button>
      <el-button
        type="primary"
        @click="handleAddTask"
      >新建任务
      </el-button>
    </div>
    <el-table
      :key="tableKey"
      :data="list"
      fit
      highlight-current-row
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        fixed
        prop="id"
        width="90"
        align="center"
      />
      <el-table-column
        label="任务名称"
        prop="name"
        width="150"
        align="center"
      />
      <el-table-column
        label="关联医生"
        prop="parentName"
        width="150"
        align="center"
      />
      <el-table-column
        label="患者姓名"
        prop="parentName"
        width="150"
        align="center"
      />
      <el-table-column
        label="任务开始时间"
        prop="createdAt"
        width="150"
        align="center"
      />
      <el-table-column
        label="任务结束时间"
        prop="createdAt"
        width="150"
        align="center"
      />
      <el-table-column
        label="关联表单"
        prop="createdAt"
        width="150"
        align="center"
      />
      <el-table-column
        label="表单属性"
        prop="fromTyep"
        width="150px"
        align="center"
      >
        <template slot-scope="{row}">
          {{ fromType[row.fromTyep].label }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        prop="createdAt"
        align="center"
        width="155px"
      />
      <el-table-column
        label="操作"
        width="100"
        align="center"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button
            type="danger"
            size="mini"
            @click="handleDel(scope.row.id,0,scope.$index)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <addTask ref="task" />
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.pageNo"
      :limit.sync="listQuery.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import waves from '@/directive/waves'
import { getList } from '@/api/baseConfig/citys'
import DatePicker from '@/components/DatePicker'
import addTask from '@/views/from/components/addTask'
export default {
  name: 'DoctorTable',
  components: { DatePicker, addTask },
  directives: { waves },
  filters: {},
  data() {
    return {
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        pageNo: 1,
        pageSize: 10,
        orderBy: 'desc'
      },
      fromType: [
        {
          value: 0,
          label: '日常关怀'
        },
        {
          value: 1,
          label: '用药提醒'
        },
        {
          value: 2,
          label: '常见疾病'
        },
        {
          value: 3,
          label: '慢性疾病'
        }
      ]
    }
  },
  created() {
    // this.handleFilter()
    // this.getList()
  },
  methods: {
    // 获取数据
    getList() {
      getList(this.listQuery).then((response) => {
        this.list = response.list
        this.total = response.totalCount
      })
    },
    handleFilter() {
      this.getList()
    },
    handleAddTask() {
			 this.$refs['task'].handleCreatedTaskShow()
    }
  }
}
</script>
