const state = {
  forms: [],
  activeForm: [],
  activeTabForFields: 'elements',
  themingVars: {
    globalFontFamily: 'Arial',
    globalFontColor: '#777777',
    globalLinkColor: '#206C92',
    globalFontSize: '16',

    labelFontWeight: '500',
    labelFontSize: '16',
    labelMarginBottom: '10',

    helpTextColor: '#cccccc',
    helpTextFontSize: '12',
    helpTextMarginTop: '10',

    inputBorderRadius: '4',
    inputBorderColor: '#dcdfe6',
    inputHoverBorderColor: '#c0c4cc',
    inputFocusBorderColor: '#000000',
    inputShadowColor: '#9D9D9D',

    buttonBackground: '#000000',
    buttonBorderColor: '#000000',
    buttonColor: '#FFFFFF'
  }
}

const mutations = {
  SET_FORMS: (state, forms) => {
    state.forms = forms
  },
  SET_ACTIVEFORM: (state, forms) => {
    state.activeForm = forms
  },
  SET_ACTIVETABFORFIELDS: (state, forms) => {
    state.activeTabForFields = forms
  },
  SET_THEMINGVARS: (state, forms) => {
    state.themingVars = forms
  }
}

const actions = {
  setForms({ commit }, forms) {
    commit('SET_FORMS', forms)
  },
  setActiveForm({ commit }, forms) {
    commit('SET_ACTIVEFORM', forms)
  },
  setActiveTabForFields({ commit }, forms) {
    commit('SET_ACTIVETABFORFIELDS', forms)
  },
  setThemingVars({ commit }, forms) {
    commit('SET_THEMINGVARS', forms)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
