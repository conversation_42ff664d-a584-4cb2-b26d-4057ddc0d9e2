# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=이전 페이지
previous_label=이전
next.title=다음 페이지
next_label=다음

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=페이지
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=전체 {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=축소
zoom_out_label=축소
zoom_in.title=확대
zoom_in_label=확대
zoom.title=확대/축소
presentation_mode.title=프레젠테이션 모드로 전환
presentation_mode_label=프레젠테이션 모드
open_file.title=파일 열기
open_file_label=열기
print.title=인쇄
print_label=인쇄
download.title=다운로드
download_label=다운로드
bookmark.title=현재 뷰 (복사하거나 새 창에 열기)
bookmark_label=현재 뷰

# Secondary toolbar and context menu
tools.title=도구
tools_label=도구
first_page.title=첫 페이지로 이동
first_page.label=첫 페이지로 이동
first_page_label=첫 페이지로 이동
last_page.title=마지막 페이지로 이동
last_page.label=마지막 페이지로 이동
last_page_label=마지막 페이지로 이동
page_rotate_cw.title=시계방향으로 회전
page_rotate_cw.label=시계방향으로 회전
page_rotate_cw_label=시계방향으로 회전
page_rotate_ccw.title=시계 반대방향으로 회전
page_rotate_ccw.label=시계 반대방향으로 회전
page_rotate_ccw_label=시계 반대방향으로 회전

cursor_text_select_tool.title=텍스트 선택 도구 활성화
cursor_text_select_tool_label=텍스트 선택 도구
cursor_hand_tool.title=손 도구 활성화
cursor_hand_tool_label=손 도구

scroll_vertical.title=세로 스크롤 사용
scroll_vertical_label=세로 스크롤
scroll_horizontal.title=가로 스크롤 사용
scroll_horizontal_label=가로 스크롤
scroll_wrapped.title=감싼 스크롤 사용
scroll_wrapped_label=감싼 스크롤

spread_none.title=펼쳐진 페이지를 합치지 않음
spread_none_label=펼쳐짐 없음
spread_odd.title=홀수 페이지로 시작하게 펼쳐진 페이지 합침
spread_odd_label=홀수 펼쳐짐
spread_even.title=짝수 페이지로 시작하게 펼쳐진 페이지 합침
spread_even_label=짝수 펼쳐짐

# Document properties dialog box
document_properties.title=문서 속성…
document_properties_label=문서 속성…
document_properties_file_name=파일 이름:
document_properties_file_size=파일 크기:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}}바이트)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}}바이트)
document_properties_title=제목:
document_properties_author=작성자:
document_properties_subject=주제:
document_properties_keywords=키워드:
document_properties_creation_date=작성 날짜:
document_properties_modification_date=수정 날짜:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=작성 프로그램:
document_properties_producer=PDF 변환 소프트웨어:
document_properties_version=PDF 버전:
document_properties_page_count=페이지 수:
document_properties_page_size=페이지 크기:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=세로
document_properties_page_size_orientation_landscape=가로
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=레터
document_properties_page_size_name_legal=리걸
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=빠른 웹 보기:
document_properties_linearized_yes=예
document_properties_linearized_no=아니오
document_properties_close=닫기

print_progress_message=인쇄 문서 준비중…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=취소

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=탐색창 열고 닫기
toggle_sidebar_notification.title=탐색창 열고 닫기 (문서에 아웃라인이나 첨부파일이 들어있음)
toggle_sidebar_label=탐색창 열고 닫기
document_outline.title=문서 아웃라인 보기(더블 클릭해서 모든 항목 열고 닫기)
document_outline_label=문서 아웃라인
attachments.title=첨부파일 보기
attachments_label=첨부파일
thumbs.title=미리보기
thumbs_label=미리보기
findbar.title=검색
findbar_label=검색

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} 페이지
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} 페이지 미리보기

# Find panel button title and messages
find_input.title=찾기
find_input.placeholder=문서에서 찾기…
find_previous.title=지정 문자열에 일치하는 1개 부분을 검색
find_previous_label=이전
find_next.title=지정 문자열에 일치하는 다음 부분을 검색
find_next_label=다음
find_highlight=모두 강조 표시
find_match_case_label=대/소문자 구분
find_entire_word_label=전체 단어
find_reached_top=문서 처음까지 검색하고 끝으로 돌아와 검색했습니다.
find_reached_bottom=문서 끝까지 검색하고 앞으로 돌아와 검색했습니다.
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} 중 {{current}} 일치
find_match_count[two]={{total}} 중 {{current}} 일치
find_match_count[few]={{total}} 중 {{current}} 일치
find_match_count[many]={{total}} 중 {{current}} 일치
find_match_count[other]={{total}} 중 {{current}} 일치
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} 이상 일치
find_match_count_limit[one]={{limit}} 이상 일치
find_match_count_limit[two]={{limit}} 이상 일치
find_match_count_limit[few]={{limit}} 이상 일치
find_match_count_limit[many]={{limit}} 이상 일치
find_match_count_limit[other]={{limit}} 이상 일치
find_not_found=검색 결과 없음

# Error panel labels
error_more_info=정보 더 보기
error_less_info=정보 간단히 보기
error_close=닫기
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (빌드: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=메시지: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=스택: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=파일: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=줄 번호: {{line}}
rendering_error=페이지를 렌더링하는 중 오류가 발생했습니다.

# Predefined zoom values
page_scale_width=페이지 너비에 맞춤
page_scale_fit=페이지에 맞춤
page_scale_auto=자동 맞춤
page_scale_actual=실제 크기에 맞춤
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=오류
loading_error=PDF를 로드하는 중 오류가 발생했습니다.
invalid_file_error=유효하지 않거나 파손된 PDF 파일
missing_file_error=PDF 파일이 없습니다.
unexpected_response_error=예상치 못한 서버 응답입니다.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}} {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 주석]
password_label=이 PDF 파일을 열 수 있는 비밀번호를 입력하십시오.
password_invalid=잘못된 비밀번호입니다. 다시 시도해 주십시오.
password_ok=확인
password_cancel=취소

printing_not_supported=경고: 이 브라우저는 인쇄를 완전히 지원하지 않습니다.
printing_not_ready=경고: 이 PDF를 인쇄를 할 수 있을 정도로 읽어들이지 못했습니다.
web_fonts_disabled=웹 폰트가 비활성화됨: 내장된 PDF 글꼴을 사용할 수 없습니다.
document_colors_not_allowed=PDF 문서의 자체 색상 허용 안됨: “페이지 자체 색상 허용”이 브라우저에서 비활성화 되어 있습니다.
